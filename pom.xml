<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.wbyy</groupId>
    <artifactId>base-server</artifactId>
    <version>3.6.4</version>

    <name>wbyy</name>
    <url>http://www.wbyy.vip</url>
    <description>若依微服务系统</description>

    <properties>
        <wbyy.version>3.6.4</wbyy.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>17</java.version>
        <spring-boot.version>3.3.3</spring-boot.version>
        <spring-cloud.version>2023.0.3</spring-cloud.version>
        <spring-cloud-alibaba.version>2023.0.1.2</spring-cloud-alibaba.version>
        <spring-boot-admin.version>3.3.3</spring-boot-admin.version>
        <mybatis.version>3.5.16</mybatis.version>
        <mybatis-plus.version>********</mybatis-plus.version>
        <mybatis-plus-join.version>1.5.4</mybatis-plus-join.version>
        <swagger.fox.version>3.0.0</swagger.fox.version>
        <swagger.core.version>1.6.2</swagger.core.version>
        <kaptcha.version>2.3.3</kaptcha.version>
        <pagehelper.boot.version>2.1.0</pagehelper.boot.version>
        <druid.version>1.2.23</druid.version>
        <dynamic-ds.version>4.3.1</dynamic-ds.version>
        <commons.io.version>2.14.0</commons.io.version>
        <velocity.version>2.3</velocity.version>
        <fastjson.version>2.0.43</fastjson.version>
        <jjwt.version>0.9.1</jjwt.version>
        <minio.version>8.2.2</minio.version>
        <poi.version>5.4.0</poi.version>
        <springdoc.version>2.5.0</springdoc.version>
        <transmittable-thread-local.version>2.14.4</transmittable-thread-local.version>
        <lombok.version>1.18.34</lombok.version>
        <hutool.version>5.8.24</hutool.version>
        <weixin.version>4.6.0</weixin.version>
        <xxl-job.version>2.4.1</xxl-job.version>
        <commons-text.version>1.12.0</commons-text.version>
        <commons-langs.version>3.17.0</commons-langs.version>
        <wbyy-log.version>3.0.1</wbyy-log.version>
        <redisson.version>3.15.4</redisson.version>
        <okhttp.version>4.12.0</okhttp.version>
        <org.mapstruct.version>1.5.5.Final</org.mapstruct.version>
        <maven-compiler-plugin.version>3.11.0</maven-compiler-plugin.version>
        <oss.version>3.17.4</oss.version>
        <spring-test.version>6.2.2</spring-test.version>
        <weaver-open-api-sdk.version>10.0.2409.01</weaver-open-api-sdk.version>
        <logstash-logback-encoder.version>8.1</logstash-logback-encoder.version>
    </properties>

    <!-- 依赖声明 -->
    <dependencyManagement>
        <dependencies>

            <!-- SpringCloud 微服务 -->
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- SpringCloud Alibaba 微服务 -->
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-alibaba-dependencies</artifactId>
                <version>${spring-cloud-alibaba.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- SpringBoot 依赖配置 -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-test</artifactId>
                <version>${spring-test.version}</version>
            </dependency>

            <!-- Springdoc webmvc 依赖配置 -->
            <dependency>
                <groupId>org.springdoc</groupId>
                <artifactId>springdoc-openapi-starter-webmvc-ui</artifactId>
                <version>${springdoc.version}</version>
            </dependency>

            <!-- 验证码 -->
            <dependency>
                <groupId>pro.fessional</groupId>
                <artifactId>kaptcha</artifactId>
                <version>${kaptcha.version}</version>
            </dependency>

            <!-- pagehelper 分页插件 -->
            <dependency>
                <groupId>com.github.pagehelper</groupId>
                <artifactId>pagehelper-spring-boot-starter</artifactId>
                <version>${pagehelper.boot.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>mybatis-spring</artifactId>
                        <groupId>org.mybatis</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.mybatis</groupId>
                <artifactId>mybatis</artifactId>
                <version>${mybatis.version}</version>
            </dependency>

            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-spring-boot3-starter</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>

            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-jsqlparser</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>

            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-annotation</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>

            <dependency>
                <groupId>com.github.yulichang</groupId>
                <artifactId>mybatis-plus-join-boot-starter</artifactId>
                <version>${mybatis-plus-join.version}</version>
            </dependency>

            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.binarywang</groupId>
                <artifactId>weixin-java-cp</artifactId>
                <version>${weixin.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-text</artifactId>
                <version>${commons-text.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>${commons-langs.version}</version>
            </dependency>
            <!-- io常用工具类 -->
            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>${commons.io.version}</version>
            </dependency>

            <!-- excel工具 -->
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml</artifactId>
                <version>${poi.version}</version>
            </dependency>

            <!-- 代码生成使用模板 -->
            <dependency>
                <groupId>org.apache.velocity</groupId>
                <artifactId>velocity-engine-core</artifactId>
                <version>${velocity.version}</version>
            </dependency>

            <!-- JSON 解析器和生成器 -->
            <dependency>
                <groupId>com.alibaba.fastjson2</groupId>
                <artifactId>fastjson2</artifactId>
                <version>${fastjson.version}</version>
            </dependency>

            <!-- JWT -->
            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt</artifactId>
                <version>${jjwt.version}</version>
            </dependency>

            <!-- 线程传递值 -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>transmittable-thread-local</artifactId>
                <version>${transmittable-thread-local.version}</version>
            </dependency>

            <!-- XxlJob -->
            <dependency>
                <groupId>com.xuxueli</groupId>
                <artifactId>xxl-job-core</artifactId>
                <version>${xxl-job.version}</version>
            </dependency>

            <!-- Redisson -->
            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson</artifactId>
                <version>${redisson.version}</version>
            </dependency>

            <!-- OkHttp -->
            <dependency>
                <groupId>com.squareup.okhttp3</groupId>
                <artifactId>okhttp</artifactId>
                <version>${okhttp.version}</version>
            </dependency>
            <dependency>
                <groupId>com.weaver</groupId>
                <artifactId>weaver-openapi-sdk</artifactId>
                <version>${weaver-open-api-sdk.version}</version>
            </dependency>
            <!-- 日志服务 -->
            <dependency>
                <groupId>com.wbyy</groupId>
                <artifactId>wbyy-log-spring-boot3-starter</artifactId>
                <version>${wbyy-log.version}</version>
            </dependency>
            <!-- 核心模块 -->
            <dependency>
                <groupId>com.wbyy</groupId>
                <artifactId>wbyy-common-core</artifactId>
                <version>${wbyy.version}</version>
            </dependency>
            <!--     多环境       -->
            <dependency>
                <groupId>com.wbyy</groupId>
                <artifactId>wbyy-common-profiles</artifactId>
                <version>${wbyy.version}</version>
            </dependency>

            <!-- 接口模块 -->
            <dependency>
                <groupId>com.wbyy</groupId>
                <artifactId>wbyy-common-swagger</artifactId>
                <version>${wbyy.version}</version>
            </dependency>

            <!-- 安全模块 -->
            <dependency>
                <groupId>com.wbyy</groupId>
                <artifactId>wbyy-common-security</artifactId>
                <version>${wbyy.version}</version>
            </dependency>

            <!-- 数据脱敏 -->
            <dependency>
                <groupId>com.wbyy</groupId>
                <artifactId>wbyy-common-sensitive</artifactId>
                <version>${wbyy.version}</version>
            </dependency>

            <!-- 权限范围 -->
            <dependency>
                <groupId>com.wbyy</groupId>
                <artifactId>wbyy-common-datascope</artifactId>
                <version>${wbyy.version}</version>
            </dependency>

            <!-- 多数据源 -->
            <dependency>
                <groupId>com.wbyy</groupId>
                <artifactId>wbyy-common-datasource</artifactId>
                <version>${wbyy.version}</version>
            </dependency>

            <!-- 分布式事务 -->
            <dependency>
                <groupId>com.wbyy</groupId>
                <artifactId>wbyy-common-seata</artifactId>
                <version>${wbyy.version}</version>
            </dependency>

            <!-- 日志记录 -->
            <dependency>
                <groupId>com.wbyy</groupId>
                <artifactId>wbyy-common-log</artifactId>
                <version>${wbyy.version}</version>
            </dependency>
            <!--   集成企业微信SDK   -->
            <dependency>
                <groupId>com.wbyy</groupId>
                <artifactId>wbyy-common-qyweixin</artifactId>
                <version>${wbyy.version}</version>
            </dependency>
            <!-- 缓存服务 -->
            <dependency>
                <groupId>com.wbyy</groupId>
                <artifactId>wbyy-common-redis</artifactId>
                <version>${wbyy.version}</version>
            </dependency>
            <!-- MQ -->
            <dependency>
                <groupId>com.wbyy</groupId>
                <artifactId>wbyy-common-rabbitmq</artifactId>
                <version>3.6.4</version>
            </dependency>

            <dependency>
                <groupId>com.wbyy</groupId>
                <artifactId>wbyy-common-orm</artifactId>
                <version>${wbyy.version}</version>
            </dependency>

            <!-- 系统接口 -->
            <dependency>
                <groupId>com.wbyy</groupId>
                <artifactId>wbyy-api-system</artifactId>
                <version>${wbyy.version}</version>
            </dependency>
            <!-- 消息服务接口 -->
            <dependency>
                <groupId>com.wbyy</groupId>
                <artifactId>wbyy-api-message</artifactId>
                <version>${wbyy.version}</version>
            </dependency>

            <dependency>
                <groupId>com.wbyy</groupId>
                <artifactId>wbyy-api-weaver</artifactId>
                <version>${wbyy.version}</version>
            </dependency>

            <dependency>
                <groupId>com.wbyy</groupId>
                <artifactId>wbyy-api-pms</artifactId>
                <version>${wbyy.version}</version>
            </dependency>

            <dependency>
                <groupId>com.wbyy</groupId>
                <artifactId>wbyy-modules-crm</artifactId>
                <version>${wbyy.version}</version>
            </dependency>

            <dependency>
                <groupId>com.wbyy</groupId>
                <artifactId>wbyy-modules-kingdee</artifactId>
                <version>${wbyy.version}</version>
            </dependency>

            <dependency>
                <groupId>com.wbyy</groupId>
                <artifactId>wbyy-modules-log</artifactId>
                <version>${wbyy.version}</version>
            </dependency>

            <dependency>
                <groupId>com.wbyy</groupId>
                <artifactId>wbyy-modules-weaver</artifactId>
                <version>${wbyy.version}</version>
            </dependency>

            <dependency>
                <groupId>com.wbyy</groupId>
                <artifactId>wbyy-modules-thirdauth</artifactId>
                <version>${wbyy.version}</version>
            </dependency>
            <dependency>
                <groupId>com.wbyy</groupId>
                <artifactId>wbyy-biz-common-form</artifactId>
                <version>${wbyy.version}</version>
            </dependency>
            <dependency>
                <groupId>com.wbyy</groupId>
                <artifactId>wbyy-biz-common-drag</artifactId>
                <version>${wbyy.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId>
                <version>${org.mapstruct.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct-processor</artifactId>
                <version>${org.mapstruct.version}</version>
                <scope>provided</scope>
            </dependency>
<!--            elk日志收集-->
            <dependency>
                <groupId>net.logstash.logback</groupId>
                <artifactId>logstash-logback-encoder</artifactId>
                <version>${logstash-logback-encoder.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <modules>
        <module>wbyy-auth</module>
        <module>wbyy-gateway</module>
        <module>wbyy-visual</module>
        <module>wbyy-modules</module>
        <module>wbyy-api</module>
        <module>wbyy-common</module>
        <module>wbyy-biz-common</module>
    </modules>
    <packaging>pom</packaging>

    <dependencies>
        <!-- bootstrap 启动器 -->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-bootstrap</artifactId>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>${maven-compiler-plugin.version}</version>
                <configuration>
                    <annotationProcessorPaths>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>${lombok.version}</version>
                        </path>
                        <path>
                            <groupId>org.mapstruct</groupId>
                            <artifactId>mapstruct-processor</artifactId>
                            <version>${org.mapstruct.version}</version>
                        </path>
                    </annotationProcessorPaths>
                    <parameters>true</parameters>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <encoding>${project.build.sourceEncoding}</encoding>
                </configuration>
            </plugin>
        </plugins>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                    <version>${spring-boot.version}</version>
                    <executions>
                        <execution>
                            <goals>
                                <goal>repackage</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>

    <distributionManagement>
        <repository>
            <id>wbcro-maven-releases</id>
            <url>https://nexus.wbcro.com/repository/wbcro-maven-releases</url>
        </repository>
        <snapshotRepository>
            <id>wbcro-maven-snapshots</id>
            <url>https://nexus.wbcro.com/repository/wbcro-maven-snapshots</url>
        </snapshotRepository>
    </distributionManagement>
</project>