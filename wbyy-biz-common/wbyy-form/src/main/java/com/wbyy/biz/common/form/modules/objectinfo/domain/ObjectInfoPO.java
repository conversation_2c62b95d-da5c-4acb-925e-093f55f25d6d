package com.wbyy.biz.common.form.modules.objectinfo.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.wbyy.common.core.annotation.Excel;
import com.wbyy.orm.core.domain.BaseAuditEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

import java.io.Serial;
import java.util.List;

/**
 * 对象信息对象 object_info
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
@Data
@SuperBuilder
@NoArgsConstructor
@Accessors(chain = true)
@TableName(value = "object_info", autoResultMap = true)
@Schema(description = "对象信息实体类")
@EqualsAndHashCode(callSuper = true)
public class ObjectInfoPO extends BaseAuditEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /** 主键id */
    @Schema(description = "主键id（新增：不传；修改：必传）")
    private Long id;

    /** 对象组ID */
    @Excel(name = "对象组ID")
    @Schema(description = "对象组ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "对象组ID不能为空")
    private Long objectGroupId;

    /** 对象组名称 */
    @Excel(name = "对象组名称")
    @Schema(description = "对象组名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "对象组名称不能为空")
    @Size(max = 30, message = "对象组名称不能超过 30 个字符")
    private String name;

    /** 对象KEY */
    @Excel(name = "对象KEY")
    @Schema(description = "对象KEY", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "对象KEY不能为空")
    @Size(max = 100, message = "对象KEY不能超过 100 个字符")
    @Pattern(regexp = "^[a-z][a-z0-9]*(_[a-z0-9]+)*$", message = "必须使用小写字母、数字、下划线 ，禁止出现数字开头，禁止两个下划线中间只出现数字")
    private String keyValue;

    /** 是否发布(0:已发布;1:未发布) */
    @Schema(description = "是否发布(0:已发布;1:未发布)")
    @Excel(name = "是否发布", readConverterExp = "0=是,1=否")
    private Integer publish;

    /** 备注 */
    @Excel(name = "备注")
    @Schema(description = "备注")
    @Size(max = 100, message = "备注不能超过 100 个字符")
    private String remark;


    /**
     * 对象组ID集合 用于查询
     */
    @TableField(exist = false)
    private List<Long> objectGroupIds;



    /** 删除标志（0代表存在 1代表删除） */
    @Schema(description = "删除标志（0代表存在 1代表删除）")
    @TableLogic
    private Integer delFlag;

}
