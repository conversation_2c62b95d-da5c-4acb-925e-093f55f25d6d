package com.wbyy.biz.common.form.modules.objectgroup.dao;

import java.util.List;
import com.wbyy.biz.common.form.modules.objectgroup.domain.ObjectGroupPO;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 对象组信息Dao接口
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
public interface IObjectGroupDao extends IService<ObjectGroupPO> {
    /**
     * 查询对象组信息
     *
     * @param id 对象组信息主键
     * @return 对象组信息
     */
    ObjectGroupPO selectById(Long id);

    /**
     * 查询对象组信息列表
     *
     * @param ids 对象信息主键
     * @return 对象组信息集合
     */
    List<ObjectGroupPO> selectByIds(List<Long> ids);

    /**
     * 查询对象组信息列表
     *
     * @param po 对象组信息
     * @return 对象组信息集合
     */
    List<ObjectGroupPO> selectList(ObjectGroupPO po);

    /**
     * 根据 po 中不为null的字段 查询对象组信息列表
     *
     * @param po 对象组信息
     * @return 对象组信息列表
     */
    List<ObjectGroupPO> findByPO(ObjectGroupPO po);

    /**
     * 新增对象组信息
     *
     * @param po 对象组信息
     * @return 结果
     */
    boolean insert(ObjectGroupPO po);

    /**
     * 修改对象组信息
     *
     * @param po 对象组信息
     * @return 结果
     */
    boolean update(ObjectGroupPO po);

    /**
     * 批量删除对象组信息
     *
     * @param ids 需要删除的对象组信息主键集合
     * @return 结果
     */
    boolean deleteByIds(Long[] ids);

}
