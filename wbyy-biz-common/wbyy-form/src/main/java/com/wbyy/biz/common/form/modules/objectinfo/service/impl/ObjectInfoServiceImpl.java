package com.wbyy.biz.common.form.modules.objectinfo.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.wbyy.biz.common.form.common.enums.ObjectPublishEnum;
import com.wbyy.biz.common.form.modules.objectattribute.dao.IObjectAttributeInfoDao;
import com.wbyy.biz.common.form.modules.objectattribute.domain.ObjectAttributeInfoPO;
import com.wbyy.biz.common.form.modules.objectgroup.dao.IObjectGroupDao;
import com.wbyy.biz.common.form.modules.objectgroup.domain.ObjectGroupPO;
import com.wbyy.biz.common.form.modules.objectinfo.dao.IObjectInfoDao;
import com.wbyy.biz.common.form.modules.objectinfo.domain.ObjectInfoPO;
import com.wbyy.biz.common.form.modules.objectinfo.service.IObjectInfoService;
import com.wbyy.common.core.exception.ServiceException;
import com.wbyy.common.core.utils.DateUtils;
import com.wbyy.common.redis.annotation.RedisLock;
import com.wbyy.common.redis.service.RedisService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;

import java.math.BigDecimal;
import java.util.*;

import static com.wbyy.biz.common.form.common.constant.CommonConstants.TABLE_NAME_PREFIX;
import static com.wbyy.biz.common.form.common.constant.RedisConstants.OBJECT_INFO_LOCK;
import static com.wbyy.biz.common.form.common.constant.RedisConstants.TABLE_NAME_SUFFIX_KEY;


/**
 * 对象信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ObjectInfoServiceImpl implements IObjectInfoService {
    private final IObjectInfoDao objectInfoDao;
    private final IObjectAttributeInfoDao objectAttributeInfoDao;
    private final IObjectGroupDao objectGroupDao;

    private final RedisService redisService;
    private final TransactionTemplate transactionTemplate;

    @Override
    public ObjectInfoPO selectById(Long id) {
        ObjectInfoPO objectInfoPO = objectInfoDao.selectById(id);
        resetKeyValue(objectInfoPO);
        return objectInfoPO;
    }

    @Override
    public List<ObjectInfoPO> selectList(ObjectInfoPO po) {
        List<ObjectInfoPO> objectInfoPOS = objectInfoDao.selectList(po);
        objectInfoPOS.forEach(this::resetKeyValue);
        return objectInfoPOS;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean insert(ObjectInfoPO po) {
        // 表名键值 不能重复
        List<ObjectInfoPO> byPO = objectInfoDao.findByPO(ObjectInfoPO.builder()
                .keyValue(TABLE_NAME_PREFIX + po.getKeyValue())
                .build());
        if (CollUtil.isNotEmpty(byPO)) {
            throw new ServiceException("对象键值已存在");
        }

        po.setKeyValue(TABLE_NAME_PREFIX + po.getKeyValue() + "_" + increment(po.getObjectGroupId()));
        objectInfoDao.insert(po);
        createSystemBuiltInFieldsIfNecessary(po);
        return true;
    }

    /**
     * 对于系统内置对象组下的对象，创建系统内置字段
     * @param po
     */
    private void createSystemBuiltInFieldsIfNecessary(ObjectInfoPO po) {
        final Long objectGroupId = po.getObjectGroupId();
        final ObjectGroupPO objectGroup = objectGroupDao.selectById(objectGroupId);
        if (!objectGroup.getSystemBuildInFlag()) {
            return;
        }
        final String groupKey = objectGroup.getGroupKey();
        if (StrUtil.isEmpty(groupKey)) {
            return;
        }
        switch (groupKey) {
            case "project": // 项目对象

                break;
            case "contract": // 任务对象
                break;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @RedisLock(prefix = OBJECT_INFO_LOCK, key = "#po.id")
    public boolean update(ObjectInfoPO po) {
        // 检验 keyValue 不允许修改
        ObjectInfoPO byDB = objectInfoDao.selectById(po.getId());
        if (!byDB.getKeyValue().startsWith(TABLE_NAME_PREFIX + po.getKeyValue())) {
            throw new ServiceException("对象键值,不允许修改");
        }

        return objectInfoDao.update(po);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByIds(Long[] ids) {
        // 删除对象信息
        List<ObjectInfoPO> objectInfoPOList = objectInfoDao.listByIds(Arrays.asList(ids));
        if (CollUtil.isEmpty(objectInfoPOList)) {
            throw new ServiceException("对象信息不存在");
        }
        objectInfoPOList.forEach(po -> {
            //  此处删除对象信息，需要判断对象信息是否已经发布
            if (ObjectPublishEnum.PUBLISHED.getCode().equals(po.getPublish())) {
                //  如果已经发布，且表数据存在，不允许删除
                //  如果已经发布，且表数据不存在，允许删除（修改表前缀）
                boolean hasData = objectInfoDao.hasData(po.getKeyValue());
                if (hasData) {
                    throw new ServiceException("对象已发布，且表存在数据，请先删除表数据");
                }
                transactionTemplate.execute(status -> {
                    // 删除表
                    objectInfoDao.dropTable(po.getKeyValue());
                    // 删除表字段信息
                    objectAttributeInfoDao.deleteByObjectInfoId(po.getId());
                    // 删除对象信息
                    objectInfoDao.removeById(po.getId());
                    // TODO 删除对象对应的表单信息？？？
                    //  @王金都

                    return true;
                });
            } else {
                transactionTemplate.execute(status -> {
                    //  如果已经未发布，允许删除
                    objectAttributeInfoDao.deleteByObjectInfoId(po.getId());
                    return objectInfoDao.removeById(po.getId());
                });
            }
        });
        return true;
    }

    @Override
    public Boolean publish(ObjectInfoPO po) {
        ObjectInfoPO objectInfoPO = objectInfoDao.selectById(po.getId());
        if (objectInfoPO == null) {
            throw new ServiceException("对象不存在");
        }
        if (ObjectPublishEnum.PUBLISHED.getCode().equals(objectInfoPO.getPublish())) {
            throw new ServiceException("对象已发布，禁止重复操作");
        }

        // 查询对象字段信息
        List<ObjectAttributeInfoPO> objectAttributeInfoPOList =
                objectAttributeInfoDao.findByPO(ObjectAttributeInfoPO.builder()
                        .objectInfoId(objectInfoPO.getId())
                        .build());

        if (CollUtil.isEmpty(objectAttributeInfoPOList)) {
            throw new ServiceException("对象字段不存在，请先维护对象字段信息");
        }

        // 构造建表SQL
        Map<String, Object> params = new HashMap<>();
        List<String> sqlList = objectAttributeInfoPOList
                .stream().map(this::getCreateTableFieldSQL).filter(StrUtil::isNotEmpty).toList();
        params.put("fields", sqlList);
        params.put("tableName", objectInfoPO.getKeyValue());
        params.put("tableComment", objectInfoPO.getName());

        // 开启事物
        transactionTemplate.execute(status -> {
            // 创建表
            objectInfoDao.createdTable(params);
            // 更新对象信息
            objectInfoPO.setPublish(ObjectPublishEnum.PUBLISHED.getCode());
            return objectInfoDao.updateById(objectInfoPO);
        });
        return true;
    }


    /**
     * 构造建表时的 字段 SQL 片段
     *
     * @param objectAttributeInfoPO 字段信息
     * @return String
     * <AUTHOR>
     * @date 2025/7/16 10:04
     */
    private String getCreateTableFieldSQL(ObjectAttributeInfoPO objectAttributeInfoPO) {

        StringBuilder sql = new StringBuilder(
                StrUtil.format("`{}` {} ",
                        objectAttributeInfoPO.getKeyValue(),
                        objectAttributeInfoPO.getFieldType())
        );
        switch (objectAttributeInfoPO.getFieldType()) {
            case "varchar":
                // 需要设置长度
                sql.append(StrUtil.format("({}) ", objectAttributeInfoPO.getFieldLength()));

                // 默认值
                if (StrUtil.isNotEmpty(objectAttributeInfoPO.getDefaultValue())) {
                    sql.append(StrUtil.format("default '{}' ", objectAttributeInfoPO.getDefaultValue()));
                } else {
                    sql.append("default null ");
                }
                break;
            case "decimal":
                // 需要设置长度和精度
                sql.append(StrUtil.format("({}, {}) ",
                        objectAttributeInfoPO.getFieldLength(),
                        objectAttributeInfoPO.getFieldDecimalLength()
                ));
                // 默认值
                if (StrUtil.isNotEmpty(objectAttributeInfoPO.getDefaultValue())) {
                    sql.append(StrUtil.format("default {} ", new BigDecimal(objectAttributeInfoPO.getDefaultValue())));
                } else {
                    sql.append("default null ");
                }
                break;
            case "date":
            case "datetime":
                // 默认值
                if (StrUtil.isNotEmpty(objectAttributeInfoPO.getDefaultValue())) {
                    if (DateUtils.isDate(objectAttributeInfoPO.getDefaultValue())) {
                        sql.append(StrUtil.format("default '{}' ", objectAttributeInfoPO.getDefaultValue()));
                    } else {
                        sql.append(StrUtil.format("default {} ", objectAttributeInfoPO.getDefaultValue()));
                    }
                } else {
                    sql.append("default null ");
                }
                break;
            case "text":
            case "longtext":
                // 默认值
                if (StrUtil.isNotEmpty(objectAttributeInfoPO.getDefaultValue())) {
                    sql.append(StrUtil.format("default '{}' ", objectAttributeInfoPO.getDefaultValue()));
                } else {
                    sql.append("default null ");
                }
                break;
            case "int":
            case "bigint":
                // 默认值
                if (StrUtil.isNotEmpty(objectAttributeInfoPO.getDefaultValue())) {
                    sql.append(StrUtil.format("default {} ", objectAttributeInfoPO.getDefaultValue()));
                } else {
                    sql.append("default null ");
                }
                break;
            default:
                throw new ServiceException("字段类型错误");
        }
        // 字段描述
        sql.append(StrUtil.format("comment '{}' ,", objectAttributeInfoPO.getName()));
        return sql.toString();
    }


    /**
     * 重写KeyValue
     *
     * @param po
     * @return void
     * <AUTHOR>
     * @date 2025/7/16 09:40
     */
    private void resetKeyValue(ObjectInfoPO po) {
        String keyValue = po.getKeyValue();
        int startIndex = TABLE_NAME_PREFIX.length();
        int lastIndex = keyValue.lastIndexOf("_");
        po.setKeyValue(po.getKeyValue().substring(startIndex, lastIndex));
    }


    /**
     * 比较 keyvalue 是否相等
     *
     * @param keyValueDB      数据库的 keyValue
     * @param keyValueVisible 界面的 keyValue
     * @return boolean
     * <AUTHOR>
     * @date 2025/7/11 15:40
     */
    private boolean keyValueEquals(String keyValueDB, String keyValueVisible) {
        int lastIndexOf = keyValueDB.lastIndexOf("_");
        int startIndex = TABLE_NAME_PREFIX.length();
        String keyValueDBNew = keyValueDB.substring(startIndex, lastIndexOf);
        return keyValueDBNew.equals(keyValueVisible);
    }


    /**
     * 採番
     *
     * @param objectGroupId 对象组ID
     * @return Long
     * <AUTHOR>
     * @date 2025/7/11 15:01
     */
    private long increment(Long objectGroupId) {
        Long cacheMapValue = redisService.getCacheMapValue(TABLE_NAME_SUFFIX_KEY, objectGroupId.toString());
        if (cacheMapValue == null) {
            cacheMapValue = 0L;
        }
        cacheMapValue += 1;
        redisService.setCacheMapValue(TABLE_NAME_SUFFIX_KEY, objectGroupId.toString(), cacheMapValue);
        return cacheMapValue;
    }
}
