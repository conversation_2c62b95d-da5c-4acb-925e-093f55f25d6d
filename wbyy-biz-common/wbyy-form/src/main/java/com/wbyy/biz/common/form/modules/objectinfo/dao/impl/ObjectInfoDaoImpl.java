package com.wbyy.biz.common.form.modules.objectinfo.dao.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wbyy.biz.common.form.modules.objectinfo.dao.IObjectInfoDao;
import com.wbyy.biz.common.form.modules.objectinfo.domain.ObjectInfoPO;
import com.wbyy.biz.common.form.modules.objectinfo.mapper.ObjectInfoMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 对象信息Dao实现层
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ObjectInfoDaoImpl extends ServiceImpl<ObjectInfoMapper, ObjectInfoPO> implements IObjectInfoDao {

    @Override
    public ObjectInfoPO selectById(Long id) {
        return this.getById(id);
    }

    @Override
    public List<ObjectInfoPO> selectList(ObjectInfoPO po) {
        return this.findByPO(po);
    }

    @Override
    public List<ObjectInfoPO> findByPO(ObjectInfoPO po) {
        if (po == null) {
            return List.of();
        }
        LambdaQueryWrapper<ObjectInfoPO> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(po.getPublish() != null, ObjectInfoPO::getPublish, po.getPublish());
        wrapper.eq(po.getObjectGroupId() != null, ObjectInfoPO::getObjectGroupId, po.getObjectGroupId());
        wrapper.eq(StrUtil.isNotEmpty(po.getName()), ObjectInfoPO::getName, po.getName());
        wrapper.likeRight(StrUtil.isNotEmpty(po.getKeyValue()), ObjectInfoPO::getKeyValue, po.getKeyValue());
        wrapper.eq(StrUtil.isNotEmpty(po.getRemark()), ObjectInfoPO::getRemark, po.getRemark());
        wrapper.in(CollUtil.isNotEmpty(po.getObjectGroupIds()), ObjectInfoPO::getObjectGroupId, po.getObjectGroupIds());
        wrapper.orderByAsc(ObjectInfoPO::getId);
        return this.list(wrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean insert(ObjectInfoPO po) {
        return this.save(po);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean update(ObjectInfoPO po) {
        return this.updateById(po);
    }

    /**
     * 批量删除对象信息
     *
     * @param ids 需要删除的对象信息主键
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByIds(Long[] ids) {
        return this.removeBatchByIds(Arrays.asList(ids));
    }

    @Override
    public boolean createdTable(Map<String, Object> params) {
        if (!CollUtil.isEmpty(params)
                && params.containsKey("fields")
                && !CollUtil.isEmpty((Collection<?>) params.get("fields"))) {
            log.info("[自定义表单]创建对象表 - 开始，参数：{}", params);
            boolean ok = baseMapper.createdTable(params);
            log.info("[自定义表单]创建对象表 - 结束，结果：{}", ok);
            return ok;
        } else {
            return false;
        }
    }

    @Override
    public boolean hasData(String tableName) {
        return getBaseMapper().hasData(tableName);
    }

    @Override
    public boolean dropTable(String tableName) {
        return getBaseMapper().dropTable(tableName);
    }
}
