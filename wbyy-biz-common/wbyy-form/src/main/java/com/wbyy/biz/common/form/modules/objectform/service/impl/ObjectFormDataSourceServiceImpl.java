package com.wbyy.biz.common.form.modules.objectform.service.impl;

import java.util.List;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import com.wbyy.biz.common.form.modules.objectform.dao.IObjectFormDataSourceDao;
import com.wbyy.biz.common.form.modules.objectform.domain.ObjectFormDataSourcePO;
import com.wbyy.biz.common.form.modules.objectform.service.IObjectFormDataSourceService;
import org.springframework.transaction.annotation.Transactional;

/**
 * 对象单数据源配置Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ObjectFormDataSourceServiceImpl implements IObjectFormDataSourceService {
    private final IObjectFormDataSourceDao objectFormDataSourceDao;

    @Override
    public ObjectFormDataSourcePO selectById(Long id) {

        return objectFormDataSourceDao.selectById(id);
    }

    @Override
    public List<ObjectFormDataSourcePO> selectList(ObjectFormDataSourcePO po) {

        return objectFormDataSourceDao.selectList(po);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean insert(ObjectFormDataSourcePO po) {
        String configId = po.getConfigId();
        ObjectFormDataSourcePO byConfigId = objectFormDataSourceDao.getByConfigId(configId);
        if (null!=byConfigId){
            po.setId(byConfigId.getId());
        }
        return objectFormDataSourceDao.saveOrUpdate(po);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean update(ObjectFormDataSourcePO po) {

        return objectFormDataSourceDao.update(po);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByIds(Long[] ids) {

        return objectFormDataSourceDao.deleteByIds(ids);
    }

    @Override
    public void deleteByConfigId(String configId) {
        objectFormDataSourceDao.deleteByConfigId(configId);
    }
}
