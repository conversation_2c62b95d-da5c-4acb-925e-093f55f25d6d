package com.wbyy.biz.common.form.modules.objectinfo.service;

import com.wbyy.biz.common.form.modules.objectinfo.domain.ObjectInfoPO;

import java.util.List;

/**
 * 对象信息Service接口
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
public interface IObjectInfoService {
    /**
     * 查询对象信息
     *
     * @param id 对象信息主键
     * @return 对象信息
     */
    ObjectInfoPO selectById(Long id);

    /**
     * 查询对象信息列表
     *
     * @param po 对象信息
     * @return 对象信息集合
     */
    List<ObjectInfoPO> selectList(ObjectInfoPO po);

    /**
     * 新增对象信息
     *
     * @param po 对象信息
     * @return 结果
     */
    boolean insert(ObjectInfoPO po);

    /**
     * 修改对象信息
     *
     * @param po 对象信息
     * @return 结果
     */
    boolean update(ObjectInfoPO po);

    /**
     * 批量删除对象信息
     *
     * @param ids 需要删除的对象信息主键集合
     * @return 结果
     */
    boolean deleteByIds(Long[] ids);

    /**
     * 对象发布
     *
     * @param po 对象信息
     * @return Boolean 状态
     * <AUTHOR>
     * @date 2025/7/16 09:40
     */
    Boolean publish(ObjectInfoPO po);
}
