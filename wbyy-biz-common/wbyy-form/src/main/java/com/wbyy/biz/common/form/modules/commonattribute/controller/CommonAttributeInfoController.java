package com.wbyy.biz.common.form.modules.commonattribute.controller;

import com.wbyy.biz.common.form.modules.commonattribute.domain.CommonAttributeInfoPO;
import com.wbyy.biz.common.form.modules.commonattribute.service.ICommonAttributeInfoService;
import com.wbyy.common.core.domain.R;
import com.wbyy.common.core.utils.poi.ExcelUtil;
import com.wbyy.common.core.web.controller.BaseController;
import com.wbyy.common.core.web.page.TableDataInfo;
import com.wbyy.common.log.annotation.Log;
import com.wbyy.common.log.enums.BusinessType;
import com.wbyy.common.security.annotation.RequiresPermissions;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 通用属性信息Controller
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
@RestController
@RequiredArgsConstructor
@Tag(name = "通用属性信息")
@RequestMapping("/common/attribute/info")
public class CommonAttributeInfoController extends BaseController {

    private final ICommonAttributeInfoService commonAttributeInfoService;

    @Operation(summary = "分页列表")
    @RequiresPermissions("commonattribute:attribute:list")
    @GetMapping("/page")
    public TableDataInfo<CommonAttributeInfoPO> page(@ParameterObject CommonAttributeInfoPO po) {
        startPage();
        List<CommonAttributeInfoPO> list = commonAttributeInfoService.selectList(po);
        return getDataTable(list);
    }

    @Operation(summary = "导出列表")
    @RequiresPermissions("commonattribute:attribute:export")
    @Log(title = "通用属性信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CommonAttributeInfoPO po) {
        List<CommonAttributeInfoPO> list = commonAttributeInfoService.selectList(po);
        ExcelUtil<CommonAttributeInfoPO> util = new ExcelUtil<CommonAttributeInfoPO>(CommonAttributeInfoPO.class);
        util.exportExcel(response, list, "通用属性信息数据");
    }

    @Operation(summary = "获取详细信息")
    @RequiresPermissions("commonattribute:attribute:query")
    @GetMapping(value = "/{id}")
    public R<CommonAttributeInfoPO> getInfo(@PathVariable("id") Long id) {
        return R.ok(commonAttributeInfoService.selectById(id));
    }

    @Operation(summary = "新增")
    @RequiresPermissions("commonattribute:attribute:add")
    @Log(title = "通用属性信息", businessType = BusinessType.INSERT)
    @PostMapping
    public R<Boolean> add(@RequestBody @Validated CommonAttributeInfoPO po) {
        return R.ok(commonAttributeInfoService.insert(po));
    }

    @Operation(summary = "修改")
    @RequiresPermissions("commonattribute:attribute:edit")
    @Log(title = "通用属性信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<Boolean> edit(@RequestBody @Validated CommonAttributeInfoPO po) {
        return R.ok(commonAttributeInfoService.update(po));
    }

    @Operation(summary = "删除")
    @RequiresPermissions("commonattribute:attribute:remove")
    @Log(title = "通用属性信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Boolean> remove(@PathVariable Long[] ids) {
        return R.ok(commonAttributeInfoService.deleteByIds(ids));
    }
}
