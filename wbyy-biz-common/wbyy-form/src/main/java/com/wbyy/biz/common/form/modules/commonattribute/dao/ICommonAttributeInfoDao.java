package com.wbyy.biz.common.form.modules.commonattribute.dao;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wbyy.biz.common.form.modules.commonattribute.domain.CommonAttributeInfoPO;

import java.util.List;

/**
 * 通用属性信息Dao接口
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
public interface ICommonAttributeInfoDao extends IService<CommonAttributeInfoPO> {
    /**
     * 查询通用属性信息
     *
     * @param id 通用属性信息主键
     * @return 通用属性信息
     */
    CommonAttributeInfoPO selectById(Long id);

    /**
     * 查询通用属性信息列表
     *
     * @param po 通用属性信息
     * @return 通用属性信息集合
     */
    List<CommonAttributeInfoPO> selectList(CommonAttributeInfoPO po);

    /**
     * 根据 po 中不为null的字段 查询通用属性信息列表
     *
     * @param po 通用属性信息
     * @return 通用属性信息列表
     */
    List<CommonAttributeInfoPO> findByPO(CommonAttributeInfoPO po);

    /**
     * 新增通用属性信息
     *
     * @param po 通用属性信息
     * @return 结果
     */
    boolean insert(CommonAttributeInfoPO po);

    /**
     * 修改通用属性信息
     *
     * @param po 通用属性信息
     * @return 结果
     */
    boolean update(CommonAttributeInfoPO po);

    /**
     * 批量删除通用属性信息
     *
     * @param ids 需要删除的通用属性信息主键集合
     * @return 结果
     */
    boolean deleteByIds(Long[] ids);

    /**
     * 根据 keyValue 查询通用属性信息
     *
     * @param keyValue 键值
     * @return 通用属性信息
     */
    CommonAttributeInfoPO findOneByKeyValue(String keyValue);

    /**
     * 根据 name 查询通用属性信息
     *
     * @param name 名称
     * @return 通用属性信息
     */
    CommonAttributeInfoPO findOneByName(String name);
}
