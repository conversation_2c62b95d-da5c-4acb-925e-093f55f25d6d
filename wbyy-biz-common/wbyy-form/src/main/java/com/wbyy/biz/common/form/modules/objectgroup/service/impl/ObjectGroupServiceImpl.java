package com.wbyy.biz.common.form.modules.objectgroup.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.wbyy.biz.common.form.modules.objectgroup.dao.IObjectGroupDao;
import com.wbyy.biz.common.form.modules.objectgroup.domain.ObjectGroupPO;
import com.wbyy.biz.common.form.modules.objectgroup.service.IObjectGroupService;
import com.wbyy.biz.common.form.modules.objectinfo.dao.IObjectInfoDao;
import com.wbyy.biz.common.form.modules.objectinfo.domain.ObjectInfoPO;
import com.wbyy.common.core.exception.ServiceException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 对象组信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ObjectGroupServiceImpl implements IObjectGroupService {
    private final IObjectGroupDao objectGroupDao;
    private final IObjectInfoDao iObjectInfoDao;

    @Override
    public ObjectGroupPO selectById(Long id) {

        return objectGroupDao.selectById(id);
    }

    @Override
    public List<ObjectGroupPO> selectList(ObjectGroupPO po) {
        List<ObjectGroupPO> objectGroupPOS = objectGroupDao.selectList(po);
        if (CollUtil.isEmpty(objectGroupPOS))
            return objectGroupPOS;
        // 查询所有对象
        List<ObjectInfoPO> objectInfoPOList = iObjectInfoDao.findByPO(ObjectInfoPO.builder()
                .publish(po.getPublish()).build());
        if (CollUtil.isNotEmpty(objectInfoPOList)) {
            Map<Long, List<ObjectInfoPO>> collect = objectInfoPOList.stream()
                    .collect(Collectors.groupingBy(ObjectInfoPO::getObjectGroupId));
            objectGroupPOS.forEach(item -> {
                List<ObjectInfoPO> objectInfoPOS = collect.get(item.getId());
                item.setObjectInfoList(objectInfoPOS);
            });
        }
        return objectGroupPOS;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean insert(ObjectGroupPO po) {
        // 新增只能新增非系统内置对象组
        po.setSystemBuildInFlag(Boolean.FALSE);
        return objectGroupDao.insert(po);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean update(ObjectGroupPO po) {
        final ObjectGroupPO exit = objectGroupDao.selectById(po.getId());
        if (exit != null && exit.getSystemBuildInFlag()) {
            throw new ServiceException("系统内置对象组不允许修改");
        }
        // 不允许修改系统内置对象组
        po.setSystemBuildInFlag(Boolean.FALSE);
        return objectGroupDao.update(po);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByIds(Long[] ids) {
        final List<Long> idList = Arrays.asList(ids);
        // 内置对象组不允许删除
        final List<ObjectGroupPO> objectGroups = objectGroupDao.selectByIds(idList);
        if (objectGroups.stream().anyMatch(ObjectGroupPO::getSystemBuildInFlag)) {
            throw new ServiceException("系统内置对象组不允许删除");
        }

        // 查询当前组下是否有对象，如果有先删除对象
        List<ObjectInfoPO> byPO = iObjectInfoDao.findByPO(ObjectInfoPO.builder().objectGroupIds(idList).build());
        if (CollUtil.isNotEmpty(byPO)) {
            throw new ServiceException("当前组下有对象，请先删除组下对象后，再删除");
        }

        return objectGroupDao.deleteByIds(ids);
    }
}
