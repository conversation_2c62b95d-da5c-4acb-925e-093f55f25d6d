package com.wbyy.biz.common.form.modules.objectattribute;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/7/11 09:15
 */
@Data
@Schema(description = "拖拽对象信息")
public class DragDTO {


    /**
     * 主键id
     */
    @Schema(description = "主键id")
    @NotNull(message = "主键必传")
    private Long id;

    @Schema(description = "自增ID（排序字段）")
    @TableField
    @NotNull(message = "排序字段必传")
    private Integer sort;

    @Schema(description = "对象信息ID")
    private Long objectInfoId;

    @Schema(description = "拖拽后的参照对象主键ID")
    @NotNull(message = "拖拽后的参照对象主键ID必传")
    private Long targetObjectId;

    @Schema(description = "拖拽后的参照对象排序字段")
    @NotNull(message = "拖拽后的参照对象排序字段必传")
    private Long targetObjectSort;

    /**
     * 是否向上插，默认:是
     */
    @Schema(description = "是否向上插，默认:是")
    private Boolean insertUp = Boolean.TRUE;
}