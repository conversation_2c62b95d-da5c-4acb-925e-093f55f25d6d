package com.wbyy.biz.common.form.modules.objectgroup.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.wbyy.biz.common.form.modules.objectinfo.domain.ObjectInfoPO;
import com.wbyy.common.core.annotation.Excel;
import com.wbyy.orm.core.domain.BaseAuditEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

import java.io.Serial;
import java.util.List;

/**
 * 对象组信息对象 object_group
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
@Data
@SuperBuilder
@NoArgsConstructor
@Accessors(chain = true)
@TableName(value = "object_group", autoResultMap = true)
@Schema(description = "对象组信息实体类")
@EqualsAndHashCode(callSuper = true)
public class ObjectGroupPO extends BaseAuditEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /** 主键id */
    @Schema(description = "主键id（新增：不传；修改：必传）")
    private Long id;

    /** 对象组名称 */
    @Excel(name = "对象组名称")
    @Schema(description = "对象组名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "对象组名称不能为空")
    @Size(max = 30, message = "对象组名称不能超过 30 个字符")
    private String name;

    /** 对象KEY */
    @Excel(name = "对象组KEY")
    @Schema(description = "对象组KEY")
    private String groupKey;

    /** 备注 */
    @Excel(name = "备注")
    @Schema(description = "备注")
    @Size(max = 100, message = "备注不能超过 100 个字符")
    private String remark;

    /**
     * 对象信息
     */
    @TableField(exist = false)
    private List<ObjectInfoPO> objectInfoList;


    /** 是否发布(0:已发布;1:未发布) */
    @TableField(exist = false)
    @Schema(description = "是否发布(0:已发布;1:未发布)")
    @Excel(name = "是否发布", readConverterExp = "0=是,1=否")
    private Integer publish;


    /** 删除标志（0代表存在 1代表删除） */
    @Schema(description = "删除标志（0代表存在 1代表删除）")
    @TableLogic
    private Integer delFlag;


    @NotNull(message = "系统内置对象组标识不能为空")
    @Schema(description = "是否系统内置对象组")
    private Boolean systemBuildInFlag = Boolean.FALSE;
}
