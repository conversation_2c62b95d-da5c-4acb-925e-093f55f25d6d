package com.wbyy.biz.common.form.modules.objectform.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;

import java.util.List;

import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.wbyy.common.core.domain.R;
import com.wbyy.common.log.annotation.Log;
import com.wbyy.common.log.enums.BusinessType;
import com.wbyy.common.security.annotation.RequiresPermissions;
import com.wbyy.biz.common.form.modules.objectform.domain.ObjectFormWidgetPO;
import com.wbyy.biz.common.form.modules.objectform.service.IObjectFormWidgetService;
import com.wbyy.common.core.web.controller.BaseController;
import com.wbyy.common.core.utils.poi.ExcelUtil;
import com.wbyy.common.core.web.page.TableDataInfo;

/**
 * 对象表单组件信息Controller
 *
 * <AUTHOR>
 * @date 2025-07-11
 */
@RestController
@RequiredArgsConstructor
@Tag(name = "对象表单组件信息")
@RequestMapping("/object/form/widget")
public class ObjectFormWidgetController extends BaseController {

    private final IObjectFormWidgetService objectFormWidgetService;

    @Operation(summary = "分页列表")
    @RequiresPermissions("form:objectform:list")
    @GetMapping("/page")
    public TableDataInfo<ObjectFormWidgetPO> page(@ParameterObject ObjectFormWidgetPO po) {
        startPage();
        List<ObjectFormWidgetPO> list = objectFormWidgetService.selectList(po);
        return getDataTable(list);
    }

    @Operation(summary = "导出列表")
    @RequiresPermissions("form:objectform:export")
    @Log(title = "对象表单组件信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ObjectFormWidgetPO po) {
        List<ObjectFormWidgetPO> list = objectFormWidgetService.selectList(po);
        ExcelUtil<ObjectFormWidgetPO> util = new ExcelUtil<ObjectFormWidgetPO>(ObjectFormWidgetPO.class);
        util.exportExcel(response, list, "对象表单组件信息数据");
    }

    @Operation(summary = "获取详细信息")
    @RequiresPermissions("form:objectform:query")
    @GetMapping(value = "/{id}")
    public R<ObjectFormWidgetPO> getInfo(@PathVariable("id") Long id) {
        return R.ok(objectFormWidgetService.selectById(id));
    }

    @Operation(summary = "新增")
    @RequiresPermissions("form:objectform:add")
    @Log(title = "对象表单组件信息", businessType = BusinessType.INSERT)
    @PostMapping
    public R<Boolean> add(@RequestBody @Validated ObjectFormWidgetPO po) {
        return R.ok(objectFormWidgetService.insert(po));
    }

    @Operation(summary = "修改")
    @RequiresPermissions("form:objectform:edit")
    @Log(title = "对象表单组件信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<Boolean> edit(@RequestBody @Validated ObjectFormWidgetPO po) {
        return R.ok(objectFormWidgetService.update(po));
    }

    @Operation(summary = "删除")
    @RequiresPermissions("form:objectform:remove")
    @Log(title = "对象表单组件信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Boolean> remove(@PathVariable Long[] ids) {
        return R.ok(objectFormWidgetService.deleteByIds(ids));
    }
}
