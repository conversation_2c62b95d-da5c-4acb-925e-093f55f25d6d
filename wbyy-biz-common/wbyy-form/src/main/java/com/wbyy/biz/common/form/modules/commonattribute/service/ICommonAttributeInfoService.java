package com.wbyy.biz.common.form.modules.commonattribute.service;

import java.util.List;

import com.wbyy.biz.common.form.modules.commonattribute.domain.CommonAttributeInfoPO;

/**
 * 通用属性信息Service接口
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
public interface ICommonAttributeInfoService {
    /**
     * 查询通用属性信息
     *
     * @param id 通用属性信息主键
     * @return 通用属性信息
     */
    CommonAttributeInfoPO selectById(Long id);

    /**
     * 查询通用属性信息列表
     *
     * @param po 通用属性信息
     * @return 通用属性信息集合
     */
    List<CommonAttributeInfoPO> selectList(CommonAttributeInfoPO po);

    /**
     * 新增通用属性信息
     *
     * @param po 通用属性信息
     * @return 结果
     */
    boolean insert(CommonAttributeInfoPO po);

    /**
     * 修改通用属性信息
     *
     * @param po 通用属性信息
     * @return 结果
     */
    boolean update(CommonAttributeInfoPO po);

    /**
     * 批量删除通用属性信息
     *
     * @param ids 需要删除的通用属性信息主键集合
     * @return 结果
     */
    boolean deleteByIds(Long[] ids);

}
