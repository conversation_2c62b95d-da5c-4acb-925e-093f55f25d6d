package com.wbyy.biz.common.form.modules.commonattribute.service.impl;

import com.wbyy.biz.common.form.modules.commonattribute.dao.ICommonAttributeInfoDao;
import com.wbyy.biz.common.form.modules.commonattribute.domain.CommonAttributeInfoPO;
import com.wbyy.biz.common.form.modules.commonattribute.service.ICommonAttributeInfoService;
import com.wbyy.common.core.exception.ServiceException;
import com.wbyy.common.redis.helper.SimpleLockHelper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.List;

import static com.wbyy.biz.common.form.common.constant.RedisConstants.COMMON_FIELD_LOCK;

/**
 * 通用属性信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CommonAttributeInfoServiceImpl implements ICommonAttributeInfoService {
    private final ICommonAttributeInfoDao commonAttributeInfoDao;
    private final SimpleLockHelper lockHelper;
    private final TransactionTemplate transactionTemplate;

    @Override
    public CommonAttributeInfoPO selectById(Long id) {

        return commonAttributeInfoDao.selectById(id);
    }

    @Override
    public List<CommonAttributeInfoPO> selectList(CommonAttributeInfoPO po) {

        return commonAttributeInfoDao.selectList(po);
    }

    @Override
    public boolean insert(CommonAttributeInfoPO po) {
        String lockKey = String.format(COMMON_FIELD_LOCK, po.getName(), po.getKeyValue());
        lockHelper.execute(lockKey,()-> transactionTemplate.execute(status -> {
            CommonAttributeInfoPO exist = commonAttributeInfoDao.findOneByKeyValue(po.getKeyValue());
            if (exist != null) {
                throw new ServiceException("字段键值不能和已有字段重复");
            }
            exist = commonAttributeInfoDao.findOneByName(po.getName());
            if (exist != null) {
                throw new ServiceException("字段名称不能和已有字段重复");
            }
            return commonAttributeInfoDao.insert(po);
        }));
        return false;
    }

    @Override
    public boolean update(CommonAttributeInfoPO po) {
        String lockKey = String.format(COMMON_FIELD_LOCK, po.getName(), po.getKeyValue());
        lockHelper.execute(lockKey,()-> transactionTemplate.execute(status -> {
            CommonAttributeInfoPO exist = commonAttributeInfoDao.findOneByKeyValue(po.getKeyValue());
            if (exist != null && !exist.getId().equals(po.getId())) {
                throw new ServiceException("字段键值不能和已有字段重复");
            }
            exist = commonAttributeInfoDao.findOneByName(po.getName());
            if (exist != null && !exist.getId().equals(po.getId())) {
                throw new ServiceException("字段名称不能和已有字段重复");
            }
            return commonAttributeInfoDao.update(po);
        }));
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByIds(Long[] ids) {

        return commonAttributeInfoDao.deleteByIds(ids);
    }
}
