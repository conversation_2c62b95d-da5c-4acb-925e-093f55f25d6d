package com.wbyy.biz.common.form.modules.objectform.dao.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;

import java.util.Collection;
import java.util.List;
import java.util.Arrays;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import com.wbyy.biz.common.form.modules.objectform.mapper.ObjectFormWidgetMapper;
import com.wbyy.biz.common.form.modules.objectform.dao.IObjectFormWidgetDao;
import com.wbyy.biz.common.form.modules.objectform.domain.ObjectFormWidgetPO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.transaction.annotation.Transactional;

/**
 * 对象表单组件信息Dao实现层
 *
 * <AUTHOR>
 * @date 2025-07-11
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ObjectFormWidgetDaoImpl extends ServiceImpl<ObjectFormWidgetMapper, ObjectFormWidgetPO> implements IObjectFormWidgetDao {

    @Override
    public ObjectFormWidgetPO selectById(Long id) {
        return this.getById(id);
    }

    @Override
    public List<ObjectFormWidgetPO> selectList(ObjectFormWidgetPO po) {
        return this.findByPO(po);
    }

    @Override
    public List<ObjectFormWidgetPO> findByPO(ObjectFormWidgetPO po) {
        if (po == null) {
            return List.of();
        }
        LambdaQueryWrapper<ObjectFormWidgetPO> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(po.getObjectFormId() != null, ObjectFormWidgetPO::getObjectFormId, po.getObjectFormId());
        wrapper.eq(po.getObjectAttributeInfoId() != null, ObjectFormWidgetPO::getObjectAttributeInfoId, po.getObjectAttributeInfoId());
        wrapper.eq(StrUtil.isNotEmpty(po.getWidgetConfig()), ObjectFormWidgetPO::getWidgetConfig, po.getWidgetConfig());
        wrapper.orderByDesc(ObjectFormWidgetPO::getId);
        return this.list(wrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean insert(ObjectFormWidgetPO po) {
        return this.save(po);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean update(ObjectFormWidgetPO po) {
        return this.updateById(po);
    }

    /**
     * 批量删除对象表单组件信息
     *
     * @param ids 需要删除的对象表单组件信息主键
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByIds(Long[] ids) {
        return this.removeBatchByIds(Arrays.asList(ids));
    }

    @Override
    public void deleteByObjectFormIds(Collection<Long> objectFormIds) {
        if (CollectionUtil.isEmpty(objectFormIds)) return;
        LambdaQueryWrapper<ObjectFormWidgetPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(ObjectFormWidgetPO::getObjectFormId,objectFormIds);
        this.remove(queryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void copy(Long oldObjectFormId, Long newObjectFormId) {
        // 老数据
        List<ObjectFormWidgetPO> toCopyWidgets = this.listByObjectFormId(oldObjectFormId);
        if (CollectionUtil.isEmpty(toCopyWidgets)) return;
        toCopyWidgets.forEach(item->{
            item.clearAudit();
            item.setObjectFormId(newObjectFormId);
        });
        this.saveBatch(toCopyWidgets);
    }

    @Override
    public List<ObjectFormWidgetPO> listByObjectFormId(Long objectFormId) {
        LambdaQueryWrapper<ObjectFormWidgetPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ObjectFormWidgetPO::getObjectFormId,objectFormId);
        return this.list(queryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveBatchByObjectFormId(List<ObjectFormWidgetPO> widgets, Long objectFormId) {
        this.deleteByObjectFormIds(List.of(objectFormId));
        if (CollectionUtil.isEmpty(widgets)) return;
        widgets.forEach(item->{
            item.setObjectFormId(objectFormId);
        });
        this.saveBatch(widgets);
    }

}
