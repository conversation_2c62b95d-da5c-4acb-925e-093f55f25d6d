package com.wbyy.biz.common.form.modules.objectgroup.service;

import java.util.List;

import com.wbyy.biz.common.form.modules.objectgroup.domain.ObjectGroupPO;

/**
 * 对象组信息Service接口
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
public interface IObjectGroupService {
    /**
     * 查询对象组信息
     *
     * @param id 对象组信息主键
     * @return 对象组信息
     */
    ObjectGroupPO selectById(Long id);

    /**
     * 查询对象组信息列表
     *
     * @param po 对象组信息
     * @return 对象组信息集合
     */
    List<ObjectGroupPO> selectList(ObjectGroupPO po);

    /**
     * 新增对象组信息
     *
     * @param po 对象组信息
     * @return 结果
     */
    boolean insert(ObjectGroupPO po);

    /**
     * 修改对象组信息
     *
     * @param po 对象组信息
     * @return 结果
     */
    boolean update(ObjectGroupPO po);

    /**
     * 批量删除对象组信息
     *
     * @param ids 需要删除的对象组信息主键集合
     * @return 结果
     */
    boolean deleteByIds(Long[] ids);

}
