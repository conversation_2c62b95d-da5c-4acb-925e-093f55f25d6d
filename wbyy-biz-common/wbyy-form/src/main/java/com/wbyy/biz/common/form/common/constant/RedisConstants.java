package com.wbyy.biz.common.form.common.constant;

/**
 * 锁前缀
 *
 * <AUTHOR>
 * @date 2025/7/11 09:17
 */
public interface RedisConstants {

    /**
     * 对象属性拖拽锁前缀
     * IObjectAttributeInfoDao
     */
    String LOCK_DRAG = "form:object:drag:";


    /**
     * 表明后缀  {} 需要填入对象组ID
     */
    String TABLE_NAME_SUFFIX_KEY = "pms:from:table_name";

    /**
     * 对象表单锁
     */
    String OBJECT_FORM_LOCK = "OBJECT:FORM:LOCK:%s:%s";

    /**
     * 对象信息锁
     */
    String OBJECT_INFO_LOCK = "OBJECT:INFO:";

    /**
     * 通用字段新增锁
     */
    String COMMON_FIELD_LOCK = "COMMON:FIELD:LOCK:%s:%s";


}