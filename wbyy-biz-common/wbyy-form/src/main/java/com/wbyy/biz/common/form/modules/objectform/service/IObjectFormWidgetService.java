package com.wbyy.biz.common.form.modules.objectform.service;

import java.util.List;

import com.wbyy.biz.common.form.modules.objectform.domain.ObjectFormWidgetPO;

/**
 * 对象表单组件信息Service接口
 *
 * <AUTHOR>
 * @date 2025-07-11
 */
public interface IObjectFormWidgetService {
    /**
     * 查询对象表单组件信息
     *
     * @param id 对象表单组件信息主键
     * @return 对象表单组件信息
     */
    ObjectFormWidgetPO selectById(Long id);

    /**
     * 查询对象表单组件信息列表
     *
     * @param po 对象表单组件信息
     * @return 对象表单组件信息集合
     */
    List<ObjectFormWidgetPO> selectList(ObjectFormWidgetPO po);

    /**
     * 新增对象表单组件信息
     *
     * @param po 对象表单组件信息
     * @return 结果
     */
    boolean insert(ObjectFormWidgetPO po);

    /**
     * 修改对象表单组件信息
     *
     * @param po 对象表单组件信息
     * @return 结果
     */
    boolean update(ObjectFormWidgetPO po);

    /**
     * 批量删除对象表单组件信息
     *
     * @param ids 需要删除的对象表单组件信息主键集合
     * @return 结果
     */
    boolean deleteByIds(Long[] ids);

}
