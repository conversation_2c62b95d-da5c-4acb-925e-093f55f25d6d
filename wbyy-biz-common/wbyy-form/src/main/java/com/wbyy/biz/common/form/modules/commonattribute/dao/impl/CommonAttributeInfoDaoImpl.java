package com.wbyy.biz.common.form.modules.commonattribute.dao.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wbyy.biz.common.form.modules.commonattribute.dao.ICommonAttributeInfoDao;
import com.wbyy.biz.common.form.modules.commonattribute.domain.CommonAttributeInfoPO;
import com.wbyy.biz.common.form.modules.commonattribute.mapper.CommonAttributeInfoMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.List;

/**
 * 通用属性信息Dao实现层
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CommonAttributeInfoDaoImpl extends ServiceImpl<CommonAttributeInfoMapper, CommonAttributeInfoPO> implements ICommonAttributeInfoDao {

    @Override
    public CommonAttributeInfoPO selectById(Long id) {
        return this.getById(id);
    }

    @Override
    public List<CommonAttributeInfoPO> selectList(CommonAttributeInfoPO po) {
        if (po == null) {
            return List.of();
        }
        LambdaQueryWrapper<CommonAttributeInfoPO> wrapper = Wrappers.lambdaQuery();
        wrapper.in(CollUtil.isNotEmpty(po.getIds()), CommonAttributeInfoPO::getId, po.getIds());
        wrapper.like(StrUtil.isNotEmpty(po.getName()), CommonAttributeInfoPO::getName, po.getName());
        wrapper.eq(StrUtil.isNotEmpty(po.getKeyValue()), CommonAttributeInfoPO::getKeyValue, po.getKeyValue());
        wrapper.eq(StrUtil.isNotEmpty(po.getFieldType()), CommonAttributeInfoPO::getFieldType, po.getFieldType());
        wrapper.eq(po.getFieldLength() != null, CommonAttributeInfoPO::getFieldLength, po.getFieldLength());
        wrapper.eq(po.getFieldDecimalLength() != null, CommonAttributeInfoPO::getFieldDecimalLength, po.getFieldDecimalLength());
        wrapper.eq(StrUtil.isNotEmpty(po.getDefaultValue()), CommonAttributeInfoPO::getDefaultValue, po.getDefaultValue());
        wrapper.eq(StrUtil.isNotEmpty(po.getUnit()), CommonAttributeInfoPO::getUnit, po.getUnit());
        wrapper.eq(StrUtil.isNotEmpty(po.getUnitEn()), CommonAttributeInfoPO::getUnitEn, po.getUnitEn());
        wrapper.orderByDesc(CommonAttributeInfoPO::getId);
        return this.list(wrapper);
    }

    @Override
    public List<CommonAttributeInfoPO> findByPO(CommonAttributeInfoPO po) {
        if (po == null) {
            return List.of();
        }
        LambdaQueryWrapper<CommonAttributeInfoPO> wrapper = Wrappers.lambdaQuery();
        wrapper.in(CollUtil.isNotEmpty(po.getIds()), CommonAttributeInfoPO::getId, po.getIds());
        wrapper.eq(StrUtil.isNotEmpty(po.getName()), CommonAttributeInfoPO::getName, po.getName());
        wrapper.eq(StrUtil.isNotEmpty(po.getKeyValue()), CommonAttributeInfoPO::getKeyValue, po.getKeyValue());
        wrapper.eq(StrUtil.isNotEmpty(po.getFieldType()), CommonAttributeInfoPO::getFieldType, po.getFieldType());
        wrapper.eq(po.getFieldLength() != null, CommonAttributeInfoPO::getFieldLength, po.getFieldLength());
        wrapper.eq(po.getFieldDecimalLength() != null, CommonAttributeInfoPO::getFieldDecimalLength, po.getFieldDecimalLength());
        wrapper.eq(StrUtil.isNotEmpty(po.getDefaultValue()), CommonAttributeInfoPO::getDefaultValue, po.getDefaultValue());
        wrapper.eq(StrUtil.isNotEmpty(po.getUnit()), CommonAttributeInfoPO::getUnit, po.getUnit());
        wrapper.eq(StrUtil.isNotEmpty(po.getUnitEn()), CommonAttributeInfoPO::getUnitEn, po.getUnitEn());
        wrapper.orderByDesc(CommonAttributeInfoPO::getId);
        return this.list(wrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean insert(CommonAttributeInfoPO commonAttributeInfo) {
        return this.save(commonAttributeInfo);
    }

    /**
     * 修改通用属性信息
     *
     * @param commonAttributeInfo 通用属性信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean update(CommonAttributeInfoPO commonAttributeInfo) {
        return this.updateById(commonAttributeInfo);
    }

    /**
     * 批量删除通用属性信息
     *
     * @param ids 需要删除的通用属性信息主键
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByIds(Long[] ids) {
        return this.removeBatchByIds(Arrays.asList(ids));
    }

    @Override
    public CommonAttributeInfoPO findOneByKeyValue(String keyValue) {
        if (StrUtil.isEmpty(keyValue)) {
            return null;
        }
        
        LambdaQueryWrapper<CommonAttributeInfoPO> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(CommonAttributeInfoPO::getKeyValue, keyValue);
        
        return this.getOne(wrapper);
    }

    @Override
    public CommonAttributeInfoPO findOneByName(String name) {
        if (StrUtil.isEmpty(name)) {
            return null;
        }

        LambdaQueryWrapper<CommonAttributeInfoPO> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(CommonAttributeInfoPO::getName, name);

        return this.getOne(wrapper);
    }
}
