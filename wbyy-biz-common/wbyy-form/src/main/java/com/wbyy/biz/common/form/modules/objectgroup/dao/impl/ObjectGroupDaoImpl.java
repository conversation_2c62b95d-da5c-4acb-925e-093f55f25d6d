package com.wbyy.biz.common.form.modules.objectgroup.dao.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wbyy.biz.common.form.modules.objectgroup.dao.IObjectGroupDao;
import com.wbyy.biz.common.form.modules.objectgroup.domain.ObjectGroupPO;
import com.wbyy.biz.common.form.modules.objectgroup.mapper.ObjectGroupMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.List;

/**
 * 对象组信息Dao实现层
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ObjectGroupDaoImpl extends ServiceImpl<ObjectGroupMapper, ObjectGroupPO> implements IObjectGroupDao {

    @Override
    public ObjectGroupPO selectById(Long id) {
        return this.getById(id);
    }

    @Override
    public List<ObjectGroupPO> selectByIds(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return List.of();
        }
        return listByIds(ids);
    }

    @Override
    public List<ObjectGroupPO> selectList(ObjectGroupPO po) {
        return this.findByPO(po);
    }

    @Override
    public List<ObjectGroupPO> findByPO(ObjectGroupPO po) {
        if (po == null) {
            return List.of();
        }
        LambdaQueryWrapper<ObjectGroupPO> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(StrUtil.isNotEmpty(po.getName()), ObjectGroupPO::getName, po.getName());
        wrapper.eq(StrUtil.isNotEmpty(po.getRemark()), ObjectGroupPO::getRemark, po.getRemark());
        wrapper.orderByAsc(ObjectGroupPO::getId);
        return this.list(wrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean insert(ObjectGroupPO po) {
        return this.save(po);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean update(ObjectGroupPO po) {
        return this.updateById(po);
    }

    /**
     * 批量删除对象组信息
     *
     * @param ids 需要删除的对象组信息主键
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByIds(Long[] ids) {
        return this.removeBatchByIds(Arrays.asList(ids));
    }

}
