package com.wbyy.biz.common.drag.utils;

import cn.hutool.core.util.StrUtil;
import com.wbyy.biz.common.drag.domain.BaseVxeEntity;
import com.wbyy.biz.common.drag.domain.BaseVxeHierarchyEntity;
import com.wbyy.biz.common.drag.domain.BaseVxeHierarchyWbsEntity;

/**
 * 拖拽相关工具包
 *
 * <AUTHOR>
 * @date 2025/7/10 13:42
 */
public class DragUtils {


    /**
     * 根据基准对象的 sort 获取 sort
     * @param entity 基准对象
     * @param next 是否下一个
     * @return sort
     */
    public static <T extends BaseVxeEntity> Integer getNextSort(T entity, Boolean next) {
        if (entity == null) {
            return 1;
        }

        if (next) {
            return entity.getSort() + 1;
        } else {
            return entity.getSort();
        }
    }

    /**
     *根据基准对象的 wbs 获取 wbs
     * @param entity 基准对象
     * @param next 是否下一个
     * @return
     */
    public static <T extends BaseVxeHierarchyWbsEntity> String getNextWbs(T entity, Boolean next) {
        if (entity == null) {
            return "1";
        }
        String[] split = entity.getWbs().split("\\.");
        String wbsItem = split[split.length - 1];

        if (next) {
            split[split.length - 1] = String.valueOf(Integer.parseInt(wbsItem) + 1);
        } else {
            return entity.getWbs();
        }
        return StrUtil.join(".", (Object) split);
    }
}