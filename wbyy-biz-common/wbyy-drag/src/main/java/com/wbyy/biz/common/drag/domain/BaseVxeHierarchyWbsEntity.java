package com.wbyy.biz.common.drag.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

import java.io.Serial;

/**
 * 有层级的 VXE table Domain
 *
 * <AUTHOR>
 * @date 2025-04-24
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@SuperBuilder
@Accessors(chain = true)
@Schema(description = "共同拖拽 - (有父级) WBS 实体类")
public class BaseVxeHierarchyWbsEntity extends BaseVxeHierarchyEntity {
    @Serial
    private static final long serialVersionUID = -5127954342127378996L;

    @Schema(description = "工作分解结构编码(WBS:1,2.1,2.3.1)")
    @Size(max = 128, message = "WBS不能超过 128 个字符")
    private String wbs;
}
