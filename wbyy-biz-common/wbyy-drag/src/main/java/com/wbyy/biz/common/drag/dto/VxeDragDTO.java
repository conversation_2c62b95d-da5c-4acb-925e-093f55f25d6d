package com.wbyy.biz.common.drag.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 拖拽入参
 * <AUTHOR>
 */
@Data
public class VxeDragDTO implements Serializable {
    @Serial
    private static final long serialVersionUID = 552840869624266687L;

    /**
     * 主键id
     */
    @Schema(description = "主键id")
    @NotNull(message = "主键必传")
    private Long id;

    @Schema(description = "自增ID（排序字段）")
    @TableField
    @NotNull(message = "排序字段必传")
    private Integer sort;

    @Schema(description = "项目ID")
    private Long projectId;

    @Schema(description = "拖拽后的参照对象主键ID")
    @TableField(exist = false)
    @NotNull(message = "拖拽后的参照对象主键ID必传")
    private Long targetObjectId;

    @Schema(description = "拖拽后的参照对象排序字段")
    @TableField(exist = false)
    @NotNull(message = "拖拽后的参照对象排序字段必传")
    private Long targetObjectSort;

    /**
     * 是否向上插，默认:是
     */
    @TableField(exist = false)
    @Schema(description = "是否向上插，默认:是")
    private Boolean insertUp = Boolean.TRUE;

}
