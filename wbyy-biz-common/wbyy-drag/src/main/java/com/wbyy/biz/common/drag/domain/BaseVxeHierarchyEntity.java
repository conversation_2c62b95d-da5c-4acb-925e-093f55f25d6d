package com.wbyy.biz.common.drag.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

import java.io.Serial;

/**
 * 有层级的 VXE table Domain
 *
 * <AUTHOR>
 * @date 2025-04-24
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@SuperBuilder
@Accessors(chain = true)
@Schema(description = "共同拖拽 - (有父级)实体类")
public class BaseVxeHierarchyEntity extends BaseVxeEntity {
    @Serial
    private static final long serialVersionUID = -5127954342127378996L;

    /**
     * 父级任务ID
     */
    @Schema(description = "父级任务ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long parentId;

    @Schema(description = "是否升级（默认升级，降级传 false）", defaultValue = "true")
    @TableField(exist = false)
//    @JsonIgnore
    private Boolean up = Boolean.TRUE;
}
