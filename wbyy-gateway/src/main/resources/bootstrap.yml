# Tomcat
server:
  port: 11000

# Spring
spring: 
  application:
    # 应用名称
    name: wbyy-gateway
  profiles:
    # 环境配置
    active: dev
  cloud:
    nacos:
      discovery:
        # 服务注册地址
        server-addr: 121.41.167.10:8848
        # 命名空间
        namespace: c3cebc4d-1968-4889-a050-6179800ceae7

      config:
        # 配置中心地址
        server-addr: 121.41.167.10:8848
        # 配置文件格式
        file-extension: yml
        # 共享配置
        shared-configs:
          - application-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
          - application-redis-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
        # 命名空间
        namespace: c3cebc4d-1968-4889-a050-6179800ceae7
      username: nacos
      password: Wbyy_nacos_e__fasa3w
