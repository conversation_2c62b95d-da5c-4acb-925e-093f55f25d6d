# 使用官方OpenJDK Alpine镜像（轻量级）
FROM harbor.wbcro.com/common/openjdk-17:v1.0.0

# 维护者信息
LABEL maintainer="wangjindu"

# 创建工作目录并设置权限
WORKDIR /home/<USER>
VOLUME /home/<USER>
# 复制JAR文件并设置所有权
COPY ./wbyy-auth.jar /home/<USER>/wbyy-auth.jar



ENTRYPOINT ["java","-jar","wbyy-auth.jar", "--spring.profiles.active=test", "--spring.cloud.nacos.discovery.ip=***********", "--spring.cloud.nacos.discovery.port=9100"]
