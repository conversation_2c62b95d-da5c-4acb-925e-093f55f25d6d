package com.wbyy.message.api.utils;

import cn.hutool.core.collection.CollUtil;
import com.wbyy.common.core.domain.R;
import com.wbyy.common.core.exception.ServiceException;

import java.util.List;

/**
 * 远程调用 参数解析
 *
 * <AUTHOR>
 */
public class WbyyApiUtils {


    /**
     * 获取 data 数据 List
     *
     * @param apiResult
     * @param <T>
     * @return
     */
    public static <T> List<T> getListData(R<List<T>> apiResult) {

        if (R.FAIL == apiResult.getCode()) {
            throw new ServiceException(apiResult.getMsg());
        }
        if (CollUtil.isNotEmpty(apiResult.getData())) {
            return apiResult.getData();
        }
        return List.of();
    }

    /**
     * 获取 data 数据 - Obj
     *
     * @param apiResult
     * @param <T>
     * @return
     */
    public static <T> T getObjData(R<T> apiResult, Class<T> clazz) {

        if (R.FAIL == apiResult.getCode()) {
            throw new ServiceException(apiResult.getMsg());
        }
        if (apiResult.getData() != null) {
            return apiResult.getData();
        }
        try {
            return clazz.getDeclaredConstructor().newInstance();
        } catch (Exception e) {
            throw new ServiceException(e);
        }
    }

}
