package com.wbyy.system.api.domain;

import com.wbyy.common.core.xss.Xss;
import com.wbyy.system.api.model.ApiBaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;
import java.util.List;

/**
 * 用户对象 sys_user
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "用户信息")
public class ApiUser extends ApiBaseEntity {

    /**
     * 用户ID
     */
    @Schema(description = "用户ID")
    private Long userId;

    /**
     * 用户账号
     */
    @Schema(description = "用户账号")
    private String userName;

    /**
     * 真实姓名
     */
    @Schema(description = "真实姓名")
    private String realName;

    /**
     * 工号
     */
    @Schema(description = "工号")
    private String workNumber;

    /**
     * 用户邮箱
     */
    @Schema(description = "用户邮箱")
    private String email;

    /**
     * 手机号码
     */
    @Schema(description = "手机号码")
    private String phone;

    /**
     * 用户性别
     */
    @Schema(description = "用户性别")
    private Integer sex;

    /**
     * 用户头像
     */
    @Schema(description = "用户头像")
    private String avatar;
    /**
     * 企微用户ID
     */
    @Schema(description = "企微用户ID")
    private String qwUserId;

    /**
     * 密码
     */
    @Schema(description = "密码")
    private String password;

    /**
     * 帐号状态（0正常 1停用）
     */
    @Schema(description = "帐号状态（0正常 1停用）")
    private Integer status;

    /**
     * 删除标志（0代表存在 1代表删除）
     */
    @Schema(description = "删除标志")
    private Integer delFlag;

    /**
     * 最后登录IP
     */
    @Schema(description = "最后登录IP")
    private String loginIp;

    /**
     * 最后登录时间
     */
    @Schema(description = "最后登录时间")
    private Date loginDate;

    @Schema(description = "备注")
    private String remark;

    /**
     * 部门对象
     */
    private List<ApiDept> depts;

    /**
     * 角色对象
     */
    private List<ApiRole> roles;

    /**
     * 部门组
     */
    private Long[] deptIds;

    /**
     * 角色组
     */
    private Long[] roleIds;

    /**
     * 岗位组
     */
    private Long[] postIds;

    /**
     * 角色ID
     */
    private Long roleId;

    private boolean checkAdmin;

    private Long postId;
    private String postName;
    private Long deptId;

    @Schema(description = "入职时间")
    private Date hireDate;

    @Schema(description = "离职时间")
    private Date departureDate;

    public ApiUser() {

    }

    public ApiUser(Long userId) {
        this.userId = userId;
    }

    @Xss(message = "用户昵称不能包含脚本字符")
    @Size(min = 0, max = 30, message = "用户昵称长度不能超过30个字符")
    public String getRealName() {
        return realName;
    }

    @Xss(message = "用户账号不能包含脚本字符")
    @NotBlank(message = "用户账号不能为空")
    @Size(min = 0, max = 30, message = "用户账号长度不能超过30个字符")
    public String getUserName() {
        return userName;
    }

    @Xss(message = "用户工号不能包含脚本字符")
    @NotBlank(message = "用户工号不能为空")
    @Size(min = 0, max = 8, message = "用户工号长度不能超过8个字符")
    public String getWorkNumber() {
        return workNumber;
    }

    @Email(message = "邮箱格式不正确")
    @Size(min = 0, max = 50, message = "邮箱长度不能超过50个字符")
    public String getEmail() {
        return email;
    }

    @Size(min = 0, max = 11, message = "手机号码长度不能超过11个字符")
    public String getPhone() {
        return phone;
    }


    /**
     * 岗位
     */
    private List<ApiDept> posts;

    private String centerDeptName;

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("userId", getUserId())
                .append("userName", getUserName())
                .append("nickName", getRealName())
                .append("email", getEmail())
                .append("phonenumber", getPhone())
                .append("sex", getSex())
                .append("avatar", getAvatar())
                .append("password", getPassword())
                .append("status", getStatus())
                .append("delFlag", getDelFlag())
                .append("loginIp", getLoginIp())
                .append("loginDate", getLoginDate())
                .append("remark", getRemark())
                .append("dept", getDepts())
                .toString();
    }
}
