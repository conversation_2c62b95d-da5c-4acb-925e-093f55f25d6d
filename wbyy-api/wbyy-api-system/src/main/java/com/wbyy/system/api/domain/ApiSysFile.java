package com.wbyy.system.api.domain;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 文件信息
 *
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ApiSysFile {
    /**
     * 文件名称,带后缀
     */
    private String name;

    /**
     * 路径，默认未空
     */
    private String url;

    /**
     * 文件id
     */
    private Long fileId;

    /**
     * 文件后缀
     */
    private String fileSuffix;

    /**
     * 文件大小
     */
    private Long fileSize;
}
