package com.wbyy.system.api.utils;

import cn.hutool.core.collection.CollUtil;
import com.wbyy.common.core.domain.R;
import com.wbyy.common.core.exception.ServiceException;
import com.wbyy.system.api.domain.ApiUser;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.wbyy.common.core.constant.HttpMagConstants.USER_REAL_NAME_NULL;
import static com.wbyy.common.core.constant.HttpMagConstants.USER_WORK_NUMBER_NULL;

/**
 * 远程调用 参数解析
 *
 * <AUTHOR>
 */
public class WbyyApiUtils {


    /**
     * 获取 data 数据 List
     *
     * @param apiResult
     * @param <T>
     * @return
     */
    public static <T> List<T> getListData(R<List<T>> apiResult) {

        if (R.FAIL == apiResult.getCode()) {
            throw new ServiceException(apiResult.getMsg());
        }
        if (CollUtil.isNotEmpty(apiResult.getData())) {
            return apiResult.getData();
        }
        return List.of();
    }

    /**
     * 获取 data 数据 - Obj
     *
     * @param apiResult
     * @param <T>
     * @return
     */
    public static <T> T getObjData(R<T> apiResult, Class<T> clazz) {

        if (R.FAIL == apiResult.getCode()) {
            throw new ServiceException(apiResult.getMsg());
        }
        if (apiResult.getData() != null) {
            return apiResult.getData();
        }
        try {
            return clazz.getDeclaredConstructor().newInstance();
        } catch (Exception e) {
            throw new ServiceException(e);
        }
    }

    /**
     * List<ApiUser> 转 userId, realName map
     *
     * @param listData 数据
     * @return map
     */
    public static Map<Long, String> userRealNameMap(List<ApiUser> listData) {
        if (CollUtil.isEmpty(listData)) {
            return Map.of();
        }
        try {
            return listData.stream()
                    .collect(Collectors.toMap(ApiUser::getUserId, ApiUser::getRealName));
        } catch (Exception e) {
            throw new ServiceException(USER_REAL_NAME_NULL);
        }
    }

    /**
     * List<ApiUser> 转 userId, workNumber map
     *
     * @param listData 数据
     * @return map
     */
    public static Map<Long, String> userWorkNumberMap(List<ApiUser> listData) {
        if (CollUtil.isEmpty(listData)) {
            return Map.of();
        }

        try {
            return listData.stream().collect(Collectors.toMap(ApiUser::getUserId, ApiUser::getWorkNumber));
        } catch (Exception e) {
            throw new ServiceException(USER_WORK_NUMBER_NULL);
        }
    }
    /**
     * List<ApiUser> 转 userId, qwUserId map
     *
     * @param listData 数据
     * @return map
     */
    public static Map<Long, String> userQwUserIdMap(List<ApiUser> listData) {
        if (CollUtil.isEmpty(listData)) {
            return Map.of();
        }

        try {
            return listData.stream().collect(Collectors.toMap(ApiUser::getUserId, ApiUser::getQwUserId));
        } catch (Exception e) {
            throw new ServiceException(USER_WORK_NUMBER_NULL);
        }
    }

    /**
     * List<ApiUser> 转 userId, avatar map
     *
     * @param listData 数据
     * @return map
     */
    public static Map<Long, String> userAvatarMap(List<ApiUser> listData) {
        if (CollUtil.isEmpty(listData)) {
            return Map.of();
        }

        try {
            return listData.stream().collect(Collectors.toMap(ApiUser::getUserId, ApiUser::getAvatar));
        } catch (Exception e) {
            throw new ServiceException(USER_WORK_NUMBER_NULL);
        }
    }
}
