package com.wbyy.system.api.factory;

import com.wbyy.common.core.domain.R;
import com.wbyy.common.core.web.page.TableDataInfo;
import com.wbyy.system.api.UserApi;
import com.wbyy.system.api.domain.ApiUser;
import com.wbyy.system.api.domain.ApiUserData;
import com.wbyy.system.api.model.LoginUser;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 用户服务降级处理
 * 
 * <AUTHOR>
 */
@Component
public class RemoteUserFallbackFactory implements FallbackFactory<UserApi>
{
    private static final Logger log = LoggerFactory.getLogger(RemoteUserFallbackFactory.class);

    @Override
    public UserApi create(Throwable throwable)
    {
        log.error("用户服务调用失败:{}", throwable.getMessage());
        return new UserApi() {
            @Override
            public R<LoginUser> getUserInfo(String username, String source)
            {
                return R.fail("获取用户失败:" + throwable.getMessage());
            }

            @Override
            public R<Boolean> registerUserInfo(ApiUser sysUser, String source)
            {
                return R.fail("注册用户失败:" + throwable.getMessage());
            }

            @Override
            public R<Boolean> recordUserLogin(ApiUser sysUser, String source)
            {
                return R.fail("记录用户登录信息失败:" + throwable.getMessage());
            }

            @Override
            public R<List<ApiUser>> infoList(List<Long> userIds, String source) {
                return R.fail("获取用户信息失败:" + throwable.getMessage());
            }

            @Override
            public R<ApiUser> getById(Long userId, String source) {
                return R.fail("获取用户信息失败:" + throwable.getMessage());
            }

            @Override
            public R<List<ApiUser>> getUserInfoByUserIds(List<Long> userIds, String source) {

                return R.fail("获取用户失败:" + throwable.getMessage());
            }

            /**
             * 通过企微用户id查询用户信息
             *
             * @param qwUserId 企微用户id
             * @param source   请求来源
             * @return 结果
             */
            @Override
            public R<LoginUser> getUserInfoByQw(String qwUserId, String source) {
                return R.fail("通过企微用户id查询用户信息失败:" + throwable.getMessage());
            }

            @Override
            public R<ApiUser> infoByWeaverUserId(String weaverUserId, String source) {
                return R.fail("通过OA用户id查询用户信息失败:" + throwable.getMessage());
            }

            @Override
            public R<List<ApiUser>> getAllUserByDept(Long deptId, String keyword, String source) {
                return R.fail("通过部门ID查询用户信息失败:" + throwable.getMessage());
            }

            @Override
            public TableDataInfo<ApiUser> pageAllUserByDept(Long deptId, String keyword, String pageNum, String pageSize, String source) {
                return new TableDataInfo<>("通过部门ID查询用户分页信息失败:" + throwable.getMessage());
            }

            @Override
            public R<ApiUser> getByWorkNumber(String workNumber, String source) {
                return R.fail("通过工号查询用户信息失败:" + throwable.getMessage());
            }
        };
    }
}
