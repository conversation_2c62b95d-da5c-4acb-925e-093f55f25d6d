package com.wbyy.system.api;

import com.wbyy.common.core.constant.SecurityConstants;
import com.wbyy.common.core.constant.ServiceNameConstants;
import com.wbyy.common.core.domain.R;
import com.wbyy.common.core.web.page.TableDataInfo;
import com.wbyy.system.api.domain.ApiUser;
import com.wbyy.system.api.domain.ApiUserData;
import com.wbyy.system.api.factory.RemoteUserFallbackFactory;
import com.wbyy.system.api.model.LoginUser;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 用户服务
 *
 * <AUTHOR>
 */
@FeignClient(contextId = "remoteUserService", value = ServiceNameConstants.SYSTEM_SERVICE, fallbackFactory = RemoteUserFallbackFactory.class)
public interface UserApi {
    /**
     * 通过用户名查询用户信息
     *
     * @param username 用户名
     * @param source   请求来源
     * @return 结果
     */
    @GetMapping("/user/info/{username}")
    public R<LoginUser> getUserInfo(@PathVariable("username") String username, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 根据用户 IDS 查询用户信息
     *
     * @param userIds 用户ID 集合
     * @return 用户信息
     */
    @GetMapping("/user/info/by-user-ids")
    R<List<ApiUser>> getUserInfoByUserIds(@RequestParam(value = "userIds") List<Long> userIds, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 注册用户信息
     *
     * @param sysUser 用户信息
     * @param source  请求来源
     * @return 结果
     */
    @PostMapping("/user/register")
    public R<Boolean> registerUserInfo(@RequestBody ApiUser sysUser, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 记录用户登录IP地址和登录时间
     *
     * @param sysUser 用户信息
     * @param source  请求来源
     * @return 结果
     */
    @PutMapping("/user/recordlogin")
    public R<Boolean> recordUserLogin(@RequestBody ApiUser sysUser, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    @GetMapping("/user/info-list")
    public R<List<ApiUser>> infoList(@RequestParam("userIds") List<Long> userIds, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    @GetMapping("/user/get-by-id")
    public R<ApiUser> getById(@RequestParam("userId") Long userId, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 通过企微用户id查询用户信息
     *
     * @param qwUserId 企微用户id
     * @param source   请求来源
     * @return 结果
     */
    @GetMapping("/user/info-by-qw")
    R<LoginUser> getUserInfoByQw(@RequestParam("qwUserId") String qwUserId, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    @GetMapping("/user/info-by-weaver-user-id")
    public R<ApiUser> infoByWeaverUserId(@RequestParam("weaverUserId") String weaverUserId, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 根据部门ID查询部门及以下的用户
     *
     * @param deptId 部门ID
     * @param source 请求来源
     * @return R<List < ApiUser>>
     * <AUTHOR>
     * @date 2025/6/30 16:00
     */
    @GetMapping("/user/get-all-user-by-dept")
    R<List<ApiUser>> getAllUserByDept(@RequestParam("deptId") Long deptId,
                                      @RequestParam(value = "keyword", required = false) String keyword,
                                      @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    @GetMapping("/user/get-all-user-by-dept/page")
    TableDataInfo<ApiUser> pageAllUserByDept(@RequestParam("deptId") Long deptId,
                                             @RequestParam(value = "keyword", required = false) String keyword,
                                             @RequestParam(required = false) String pageNum,
                                             @RequestParam(required = false) String pageSize,
                                             @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    @GetMapping("/user/get-by-work-number")
    R<ApiUser> getByWorkNumber(@RequestParam("workNumber")String workNumber,
                               @RequestHeader(SecurityConstants.FROM_SOURCE) String source);
}
