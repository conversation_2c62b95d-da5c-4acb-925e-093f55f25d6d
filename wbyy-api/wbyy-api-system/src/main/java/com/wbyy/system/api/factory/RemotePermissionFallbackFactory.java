package com.wbyy.system.api.factory;

import com.wbyy.common.core.domain.R;
import com.wbyy.system.api.PermissionApi;
import com.wbyy.system.api.domain.ApiPermissionSql;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

/**
 * 权限服务降级处理
 * 
 * <AUTHOR>
 */
@Component
public class RemotePermissionFallbackFactory implements FallbackFactory<PermissionApi>
{
    private static final Logger log = LoggerFactory.getLogger(RemotePermissionFallbackFactory.class);

    @Override
    public PermissionApi create(Throwable throwable)
    {
        log.error("权限接口调用失败:{}", throwable.getMessage());
        return new PermissionApi()
        {
            @Override
            public R<String> getPermissionSql(ApiPermissionSql apiPermissionSql, String source) {
                return R.fail("获取权限SQL失败:" + throwable.getMessage());
            }
        };
    }
}
