package com.wbyy.system.api.factory;

import com.wbyy.common.core.domain.R;
import com.wbyy.system.api.CalendarApi;
import com.wbyy.system.api.domain.ApiCalendar;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @author: 王金都
 * @date: 2025/5/15 10:17
 */
@Component
public class RemoteCalendarFallbackFactory implements FallbackFactory<CalendarApi> {
    @Override
    public CalendarApi create(Throwable throwable) {
        return new CalendarApi() {
            @Override
            public R<List<ApiCalendar>> list(String startDate, String endDate, String source) {
                return R.fail("获取日历失败:" + throwable.getMessage());
            }
        };
    }
}
