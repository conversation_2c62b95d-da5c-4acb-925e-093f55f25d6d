package com.wbyy.system.api;

import com.wbyy.common.core.constant.SecurityConstants;
import com.wbyy.common.core.constant.ServiceNameConstants;
import com.wbyy.common.core.domain.R;
import com.wbyy.system.api.domain.ApiDept;
import com.wbyy.system.api.factory.RemoteDeptFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Set;

@FeignClient(contextId = "remoteDeptService", value = ServiceNameConstants.SYSTEM_SERVICE, fallbackFactory = RemoteDeptFallbackFactory.class)
public interface DeptApi {

    @GetMapping("/dept/{deptId}")
    R<ApiDept> getDept(@PathVariable("deptId")Long deptId, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    @GetMapping("/dept/get-center-by-id")
    R<ApiDept> getCenterById(@RequestParam("deptId") Long deptId, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    @GetMapping("/dept/center-list")
    R<List<ApiDept>> centerList(@RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    @GetMapping("/dept/select-by-ancestors/{deptId}")
    R<List<ApiDept>> selectByAncestors(@PathVariable("deptId")Long deptId, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    @GetMapping("/dept/find-by-deptIds")
    R<List<ApiDept>> findByDeptIds(@RequestParam("deptIds") Set<Long> deptIds, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);
}
