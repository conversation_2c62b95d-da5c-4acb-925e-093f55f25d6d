package com.wbyy.system.api.domain;

import lombok.Builder;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/7/4  17:14
 * @description 返回分页total
 */
@Data
@Builder
public class ApiUserData implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    private List<ApiUser> apiUser;

    private Integer total;
}
