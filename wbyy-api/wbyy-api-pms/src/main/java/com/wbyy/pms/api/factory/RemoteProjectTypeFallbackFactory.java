package com.wbyy.pms.api.factory;

import com.alibaba.fastjson2.JSONArray;
import com.wbyy.common.core.domain.R;
import com.wbyy.pms.api.ProjectTypeApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025/7/28 17:57
 */
@Component
@Slf4j
public class RemoteProjectTypeFallbackFactory implements FallbackFactory<ProjectTypeApi> {
    @Override
    public ProjectTypeApi create(Throwable throwable) {
        log.error("PMS服务调用失败:{}", throwable.getMessage());
        return new ProjectTypeApi() {
            @Override
            public R<JSONArray> simpleTree(String source) {
                return R.fail("获取项目业务类型失败:" + throwable.getMessage());            }
        };
    }
}
