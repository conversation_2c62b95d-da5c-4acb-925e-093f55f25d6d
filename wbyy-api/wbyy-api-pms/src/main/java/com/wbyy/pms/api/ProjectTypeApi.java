package com.wbyy.pms.api;

import com.alibaba.fastjson2.JSONArray;
import com.wbyy.common.core.constant.SecurityConstants;
import com.wbyy.common.core.constant.ServiceNameConstants;
import com.wbyy.common.core.domain.R;
import com.wbyy.pms.api.factory.RemoteProjectTypeFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;

/**
 * <AUTHOR>
 * @date 2025/7/28 17:56
 */
@FeignClient(contextId = "remoteProjectTypeService", value = ServiceNameConstants.PMS_SERVICE, fallbackFactory = RemoteProjectTypeFallbackFactory.class)
public interface ProjectTypeApi {

    @GetMapping("/project/type/simple-tree")
    R<JSONArray> simpleTree(@RequestHeader(SecurityConstants.FROM_SOURCE) String source);
}
