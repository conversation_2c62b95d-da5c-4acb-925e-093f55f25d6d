package com.wbyy.weaver.api.factory;

import com.wbyy.common.core.domain.R;
import com.wbyy.weaver.api.PositionApi;
import com.wbyy.weaver.api.domain.RemoteDeptInfo;
import org.springframework.cloud.openfeign.FallbackFactory;

import java.util.List;

/**
 * @author: 王金都
 * @date: 2025/5/20 10:13
 */
public class RemotePositionFallbackFactory implements FallbackFactory<PositionApi> {
    @Override
    public PositionApi create(Throwable throwable) {
        return new PositionApi() {
            @Override
            public R<List<RemoteDeptInfo>> listPositionV2(String source) {
                return R.fail("获取岗位列表失败："+throwable.getMessage());
            }
        };
    }
}
