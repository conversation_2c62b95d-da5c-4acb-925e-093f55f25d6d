package com.wbyy.weaver.api.factory;

import com.wbyy.common.core.domain.R;
import com.wbyy.weaver.api.DepartmentApi;
import com.wbyy.weaver.api.domain.RemoteDeptInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @author: 王金都
 * @date: 2025/4/27 16:45
 */
@Component
@Slf4j
public class RemoteDepartmentFallbackFactory implements FallbackFactory<DepartmentApi> {
    @Override
    public DepartmentApi create(Throwable throwable) {
        log.error("weaver调用失败:{}", throwable.getMessage());
        return new DepartmentApi() {
            @Override
            public R<List<RemoteDeptInfo>> v2List(String status, String isDelete, Long deptId, String source) {
                return R.fail("获取部门列表失败："+throwable.getMessage());
            }
        };
    }
}
