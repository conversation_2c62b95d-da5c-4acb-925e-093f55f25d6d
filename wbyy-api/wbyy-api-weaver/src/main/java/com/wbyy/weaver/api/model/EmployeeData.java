package com.wbyy.weaver.api.model;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * @author: 王金都
 * @date: 2025/4/28 8:27
 */
@Data
public class EmployeeData implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    private UserParam education;
    private String accumfundaccount;
    private UserParam nation;
    private String residence_place;
    private String modifier;
    private String active_date;
    private String type;
    private String bankid;
    private String id_no;
    private String bank_accountname;
    private String id;
    private String bank_account;
    private String created;
    private UserParam degree;
    private String first_work_date;
    private Integer work_year;
    private String formdata;
    private String login_status;
    private String native_place;
    private String last_update_time;
    private String pinyin;
    private String user_id;
    private String child_status;
    private String birthday;
    private String tenant_key;
    private String family_contact;
    private Integer sec_level;
    private String update_time;
    private String job_num;
    private String department;
    private String logogram;
    private String personnel_status;
    private String im_uid;
    private UserParam household_type;
    private String graduate_school;
    private String marital_status;
    private UserParam politics_status;
    private Integer age;
    private String username;
    private String mobile;
    private String email;
    private String position;
}
