package com.wbyy.weaver.api;

import com.wbyy.common.core.constant.SecurityConstants;
import com.wbyy.common.core.constant.ServiceNameConstants;
import com.wbyy.common.core.domain.R;
import com.wbyy.weaver.api.domain.RemoteEmployeeData;
import com.wbyy.weaver.api.factory.RemoteEmployeeFallbackFactory;
import com.wbyy.weaver.api.model.dto.QueryEmployeeDto;
import com.wbyy.weaver.api.model.dto.UserDto;
import com.wbyy.weaver.api.model.vo.UserVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

import java.util.List;

/**
 * @author: 王金都
 * @date: 2025/4/27 16:53
 */
@FeignClient(contextId = "remoteEmployeeService", value = ServiceNameConstants.WEAVER_SERVICE, fallbackFactory = RemoteEmployeeFallbackFactory.class)
public interface EmployeeApi {


    @PostMapping("/employee/query")
    R<RemoteEmployeeData> query(@RequestBody QueryEmployeeDto dto,@RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    @PostMapping("/employee/department-users-v3")
    R<List<UserVo>> departmentUsersV3(@RequestBody UserDto dto,@RequestHeader(SecurityConstants.FROM_SOURCE) String source);
}
