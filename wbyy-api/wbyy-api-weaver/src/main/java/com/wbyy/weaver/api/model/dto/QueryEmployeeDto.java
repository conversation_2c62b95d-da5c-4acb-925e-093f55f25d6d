package com.wbyy.weaver.api.model.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * @author: 王金都
 * @date: 2025/4/27 16:28
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class QueryEmployeeDto extends PageDto implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;
    private List<String> departmentIds;
    private List<String> multiFieldList;
}
