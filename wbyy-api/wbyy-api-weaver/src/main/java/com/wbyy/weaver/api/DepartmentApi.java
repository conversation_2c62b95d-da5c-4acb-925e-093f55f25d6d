package com.wbyy.weaver.api;

import com.wbyy.common.core.constant.SecurityConstants;
import com.wbyy.common.core.constant.ServiceNameConstants;
import com.wbyy.common.core.domain.R;
import com.wbyy.weaver.api.domain.RemoteDeptInfo;
import com.wbyy.weaver.api.factory.RemoteDepartmentFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * @author: 王金都
 * @date: 2025/4/27 16:43
 */
@FeignClient(contextId = "remoteDepartmentService", value = ServiceNameConstants.WEAVER_SERVICE, fallbackFactory = RemoteDepartmentFallbackFactory.class)
public interface DepartmentApi {

    @GetMapping("/department/list-v2")
    R<List<RemoteDeptInfo>> v2List(@RequestParam(name = "status", required = false) String status,
                                   @RequestParam(name = "isDelete", required = false) String isDelete,
                                   @RequestParam(name = "deptId", required = false) Long deptId,
                                   @RequestHeader(SecurityConstants.FROM_SOURCE) String source);
}
