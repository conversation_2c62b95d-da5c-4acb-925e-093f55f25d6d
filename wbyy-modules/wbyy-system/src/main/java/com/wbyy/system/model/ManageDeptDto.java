package com.wbyy.system.model;

import com.wbyy.orm.core.domain.BaseEntity;
import com.wbyy.system.domain.SysDept;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.util.Set;

/**
 * @author: 王金都
 * @date: 2025/6/12 17:05
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ManageDeptDto extends BaseEntity {
    @Serial
    private static final long serialVersionUID = 1L;
    private Set<Long> deptIds;
}
