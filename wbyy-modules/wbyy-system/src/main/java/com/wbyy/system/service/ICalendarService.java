package com.wbyy.system.service;

import java.io.IOException;
import java.util.List;
import java.util.Map;

import com.wbyy.system.api.domain.ApiCalendar;
import com.wbyy.system.domain.Calendar;
import com.baomidou.mybatisplus.extension.service.IService;
import com.wbyy.system.domain.vo.CalendarVO;

/**
 * 系统日历Service接口
 *
 * <AUTHOR>
 * @date 2025-04-18
 */
public interface ICalendarService  extends IService<Calendar> {
    /**
     * 查询系统日历
     *
     * @param id 系统日历主键
     * @return 系统日历
     */
    Calendar selectCalendarById(Long id);

    /**
     * 查询系统日历列表
     *
     * @param calendar 系统日历
     * @return 系统日历集合
     */
    List<Calendar> selectCalendarList(Calendar calendar);

    /**
     * 新增系统日历
     *
     * @param calendar 系统日历
     * @return 结果
     */
    boolean insertCalendar(Calendar calendar);

    /**
     * 修改系统日历
     *
     * @param calendar 系统日历
     * @return 结果
     */
    boolean updateCalendar(Calendar calendar);

    /**
     * 批量删除系统日历
     *
     * @param ids 需要删除的系统日历主键集合
     * @return 结果
     */
    boolean deleteCalendarByIds(Long[] ids);

    /**
     * 查询节假日信息
     * @param year
     * @param calendarType
     * @return
     */
    Map<String, CalendarVO> getByYear(String year, String calendarType);

    /**
     *  系统日历 - 导入
     * @param calendars
     * @param year
     * @param calenderType
     * @return
     */
    String importData(List<Calendar> calendars, String year, String calenderType);

    /**
     * @Description: 查日历列表
     * @param startDate
     * @param endDate
     * @return: List<RemoteCalendar>
     * @author: 王金都
     * @Date: 2025/5/14 18:20
     */
    List<ApiCalendar> list(String startDate, String endDate);

    /**
     * @Description: 同步工作日、节假日、调休
     * @param year
     * @param calendarType
     * @return: void
     * @author: 王金都
     * @Date: 2025/5/15 9:19
     */
    void async(String year,String calendarType) throws IOException;
}
