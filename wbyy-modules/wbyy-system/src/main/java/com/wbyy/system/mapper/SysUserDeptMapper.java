package com.wbyy.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wbyy.system.domain.SysUserDept;
import com.wbyy.system.domain.SysUserRole;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface SysUserDeptMapper extends BaseMapper<SysUserDept> {
    /**
     * 通过用户ID删除用户和部门关联
     *
     * @param userId 用户ID
     * @return 结果
     */
    public int deleteUserDeptByUserId(Long userId);

    /**
     * 批量删除用户和部门关联
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    public int deleteUserDept(Long[] ids);

    /**
     * 通过部门ID查询部门使用数量
     *
     * @param deptId 角色ID
     * @return 结果
     */
    public int countUserDeptByDeptId(Long deptId);

    /**
     * 批量新增用户部门信息
     *
     * @param userDeptList 用户部门列表
     * @return 结果
     */
    public int batchUserDept(List<SysUserDept> userDeptList);

    /**
     * 删除用户和部门关联信息
     *
     * @param userDept 用户和部门关联信息
     * @return 结果
     */
    public int deleteUserDeptInfo(SysUserDept userDept);

    /**
     * 批量取消授权用户部门
     *
     * @param deptId 部门ID
     * @param userIds 需要删除的用户数据ID
     * @return 结果
     */
    public int deleteUserDeptInfos(@Param("deptId") Long deptId, @Param("userIds") Long[] userIds);
}
