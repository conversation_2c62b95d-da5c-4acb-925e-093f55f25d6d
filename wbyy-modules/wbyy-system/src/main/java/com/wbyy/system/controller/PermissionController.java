package com.wbyy.system.controller;

import com.wbyy.common.core.domain.R;
import com.wbyy.common.security.annotation.InnerAuth;
import com.wbyy.system.api.domain.ApiPermissionSql;
import com.wbyy.system.service.IPermissionService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 数据权限接口
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/permission")
@Tag(name = "数据权限")
@Slf4j
public class PermissionController {

    private final IPermissionService permissionService;

    @Operation(summary = "获取权限字符串（SQL）")
    @GetMapping("/permission-sql")
    @InnerAuth
    public R<String> getPermissionSql(ApiPermissionSql permissionSqlDTO) {
        return R.ok(permissionService.getPermissionSql(permissionSqlDTO));
    }
}
