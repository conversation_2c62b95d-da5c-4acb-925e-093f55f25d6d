package com.wbyy.system.controller;

import java.util.Arrays;

import com.wbyy.common.core.constant.CacheConstants;
import com.wbyy.common.core.exception.ServiceException;
import com.wbyy.common.core.utils.bean.BeanUtils;
import com.wbyy.common.core.utils.security.RSAUtils;
import com.wbyy.common.redis.service.RedisService;
import com.wbyy.system.api.domain.ApiUser;
import com.wbyy.system.model.UpdatePwdDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import com.wbyy.common.core.domain.R;
import com.wbyy.common.core.utils.StringUtils;
import com.wbyy.common.core.utils.file.FileTypeUtils;
import com.wbyy.common.core.utils.file.MimeTypeUtils;
import com.wbyy.common.core.web.controller.BaseController;
import com.wbyy.common.core.web.domain.AjaxResult;
import com.wbyy.common.log.annotation.Log;
import com.wbyy.common.log.enums.BusinessType;
import com.wbyy.common.security.service.TokenService;
import com.wbyy.common.security.utils.SecurityUtils;
import com.wbyy.system.api.FileApi;
import com.wbyy.system.api.domain.ApiSysFile;
import com.wbyy.system.domain.SysUser;
import com.wbyy.system.api.model.LoginUser;
import com.wbyy.system.service.ISysUserService;

/**
 * 个人信息 业务处理
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/user/profile")
@Slf4j
public class SysProfileController extends BaseController
{
    @Autowired
    private ISysUserService userService;
    
    @Autowired
    private TokenService tokenService;
    
    @Autowired
    private FileApi fileApi;
    @Autowired
    private RedisService redisService;

    /**
     * 个人信息
     */
    @GetMapping
    public AjaxResult profile()
    {
        String username = SecurityUtils.getUsername();
        SysUser user = userService.selectUserByUserName(username);
        AjaxResult ajax = AjaxResult.success(user);
        ajax.put("roleGroup", userService.selectUserRoleGroup(username));
        ajax.put("postGroup", userService.selectUserPostGroup(username));
        return ajax;
    }

    /**
     * 修改用户
     */
    @Log(title = "个人信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult updateProfile(@RequestBody SysUser user)
    {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        ApiUser sysUser = loginUser.getSysUser();
        SysUser currentUser = new SysUser();
        BeanUtils.copyBeanProp(currentUser,sysUser);
        currentUser.setRealName(user.getRealName());
        currentUser.setEmail(user.getEmail());
        currentUser.setPhone(user.getPhone());
        currentUser.setSex(user.getSex());
        if (StringUtils.isNotEmpty(user.getPhone()) && !userService.checkPhoneUnique(currentUser))
        {
            return error("修改用户'" + loginUser.getUsername() + "'失败，手机号码已存在");
        }
        if (StringUtils.isNotEmpty(user.getEmail()) && !userService.checkEmailUnique(currentUser))
        {
            return error("修改用户'" + loginUser.getUsername() + "'失败，邮箱账号已存在");
        }
        if (userService.updateUserProfile(currentUser))
        {
            // 更新缓存用户信息
            tokenService.setLoginUser(loginUser);
            return success();
        }
        return error("修改个人信息异常，请联系管理员");
    }

    /**
     * 重置密码
     */
    @Log(title = "个人信息", businessType = BusinessType.UPDATE)
    @PostMapping("/updatePwd")
    public AjaxResult updatePwd(@RequestBody @Validated UpdatePwdDto dto)
    {
        String cacheKey = String.format(CacheConstants.RSA_PRIVATE_KEY,dto.getKeyId());
        String base64PrivateKey = redisService.getCacheObject(cacheKey);
        String newPassword;
        String oldPassword;
        try {
            newPassword = RSAUtils.decrypt(dto.getNewPassword(), RSAUtils.base64ToPrivateKey(base64PrivateKey));
            oldPassword = RSAUtils.decrypt(dto.getOldPassword(), RSAUtils.base64ToPrivateKey(base64PrivateKey));
        } catch (Exception e) {
            log.error("密码解密失败",e);
            throw new ServiceException("密码格式错误");
        }finally {
            redisService.deleteObject(cacheKey);
        }
        String username = SecurityUtils.getUsername();
        SysUser user = userService.selectUserByUserName(username);
        String password = user.getPassword();
        if (!SecurityUtils.matchesPassword(oldPassword, password))
        {
            return error("修改密码失败，旧密码错误");
        }
        if (SecurityUtils.matchesPassword(newPassword, password))
        {
            return error("新密码不能与旧密码相同");
        }
        newPassword = SecurityUtils.encryptPassword(newPassword);
        if (userService.resetUserPwd(username, newPassword) > 0)
        {
            // 更新缓存用户密码
            LoginUser loginUser = SecurityUtils.getLoginUser();
            loginUser.getSysUser().setPassword(newPassword);
            tokenService.setLoginUser(loginUser);
            return success();
        }
        return error("修改密码异常，请联系管理员");
    }
    
    /**
     * 头像上传
     */
    @Log(title = "用户头像", businessType = BusinessType.UPDATE)
    @PostMapping("/avatar")
    public AjaxResult avatar(@RequestParam("avatarfile") MultipartFile file)
    {
        if (!file.isEmpty())
        {
            LoginUser loginUser = SecurityUtils.getLoginUser();
            String extension = FileTypeUtils.getExtension(file);
            if (!StringUtils.equalsAnyIgnoreCase(extension, MimeTypeUtils.IMAGE_EXTENSION))
            {
                return error("文件格式不正确，请上传" + Arrays.toString(MimeTypeUtils.IMAGE_EXTENSION) + "格式");
            }
            R<ApiSysFile> fileResult = fileApi.upload(file);
            if (StringUtils.isNull(fileResult) || StringUtils.isNull(fileResult.getData()))
            {
                return error("文件服务异常，请联系管理员");
            }
            String url = fileResult.getData().getUrl();
            if (userService.updateUserAvatar(loginUser.getUsername(), url))
            {
                AjaxResult ajax = AjaxResult.success();
                ajax.put("imgUrl", url);
                // 更新缓存用户头像
                loginUser.getSysUser().setAvatar(url);
                tokenService.setLoginUser(loginUser);
                return ajax;
            }
        }
        return error("上传图片异常，请联系管理员");
    }
}
