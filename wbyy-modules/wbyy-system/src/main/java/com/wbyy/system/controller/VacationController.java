package com.wbyy.system.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import com.wbyy.common.security.annotation.InnerAuth;
import com.wbyy.system.api.domain.ApiVacation;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.wbyy.common.core.domain.R;
import com.wbyy.common.log.annotation.Log;
import com.wbyy.common.log.enums.BusinessType;
import com.wbyy.common.security.annotation.RequiresPermissions;
import com.wbyy.system.domain.Vacation;
import com.wbyy.system.service.IVacationService;
import com.wbyy.common.core.web.controller.BaseController;
import com.wbyy.common.core.utils.poi.ExcelUtil;
import com.wbyy.common.core.web.page.TableDataInfo;

/**
 * 请假Controller
 * 
 * <AUTHOR>
 * @date 2025-06-13
 */
@RestController
@RequiredArgsConstructor
@Tag(name = "请假")
@RequestMapping("/vacation")
public class VacationController extends BaseController
{

    private final IVacationService vacationService;

    @Operation(summary = "分页列表")
    @RequiresPermissions("system:vacation:list")
    @GetMapping("/page")
    public TableDataInfo<Vacation> page(@ParameterObject Vacation vacation)
    {
        startPage();
        List<Vacation> list = vacationService.selectList(vacation);
        return getDataTable(list);
    }

    @Operation(summary = "导出列表")
    @RequiresPermissions("system:vacation:export")
    @Log(title = "请假", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Vacation vacation)
    {
        List<Vacation> list = vacationService.selectList(vacation);
        ExcelUtil<Vacation> util = new ExcelUtil<Vacation>(Vacation.class);
        util.exportExcel(response, list, "请假数据");
    }

    @Operation(summary = "获取详细信息")
    @RequiresPermissions("system:vacation:query")
    @GetMapping(value = "/{id}")
    public R<Vacation> getInfo(@PathVariable("id") Long id)
    {
        return R.ok(vacationService.selectById(id));
    }

    @Operation(summary = "新增")
    @RequiresPermissions("system:vacation:add")
    @Log(title = "请假", businessType = BusinessType.INSERT)
    @PostMapping
    public R<Boolean> add(@RequestBody @Validated Vacation vacation)
    {
        return R.ok(vacationService.insert(vacation));
    }

    @Operation(summary = "修改")
    @RequiresPermissions("system:vacation:edit")
    @Log(title = "请假", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<Boolean> edit(@RequestBody @Validated Vacation vacation)
    {
        return R.ok(vacationService.update(vacation));
    }

    @Operation(summary = "删除")
    @RequiresPermissions("system:vacation:remove")
    @Log(title = "请假", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public R<Boolean> remove(@PathVariable Long[] ids)
    {
        return R.ok(vacationService.deleteByIds(ids));
    }

    @GetMapping("/async-all")
    public R<?> asyncAll(@RequestParam("startDate")String startDate,@RequestParam("endDate")String endDate){
        vacationService.asyncAll(startDate,endDate);
        return R.ok();
    }

    @GetMapping("/async-pre-day")
    public R<?> asyncPreDay(){
        vacationService.asyncPreDay();
        return R.ok();
    }

    @GetMapping("/list-user-vacation")
    @InnerAuth
    public R<List<ApiVacation>> listUserVacation(@RequestParam("userId")Long userId,
                                                 @RequestParam("startDate") String startDate,
                                                 @RequestParam("endDate") String endDate){
        List<Vacation> vacations = vacationService.listUserVacation(userId, DateUtil.parseDate(startDate), DateUtil.parseDate(endDate));
        List<ApiVacation> apiVacations = new ArrayList<>();
        for (Vacation vacation : vacations) {
            ApiVacation apiVacation = new ApiVacation();
            BeanUtil.copyProperties(vacation,apiVacation);
            apiVacations.add(apiVacation);
        }
        return R.ok(apiVacations);
    }

    @GetMapping("/list-users-vacation")
    @InnerAuth
    public R<List<ApiVacation>> listUsersVacation(@RequestParam("userIds")List<Long> userIds,
                                                  @RequestParam("startDate") String startDate,
                                                  @RequestParam("endDate") String endDate){
        List<Vacation> vacations = vacationService.listUsersVacation(userIds, DateUtil.parseDate(startDate), DateUtil.parseDate(endDate));
        List<ApiVacation> apiVacations = new ArrayList<>();
        for (Vacation vacation : vacations) {
            ApiVacation apiVacation = new ApiVacation();
            BeanUtil.copyProperties(vacation,apiVacation);
            apiVacations.add(apiVacation);
        }
        return R.ok(apiVacations);
    }
}
