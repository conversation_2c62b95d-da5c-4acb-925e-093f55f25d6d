package com.wbyy.system.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.wbyy.common.core.constant.AppCodeConst;
import com.wbyy.common.core.constant.SecurityConstants;
import com.wbyy.common.core.constant.UserConstants;
import com.wbyy.common.core.domain.R;
import com.wbyy.common.core.enums.SysYesNo;
import com.wbyy.common.core.exception.ServiceException;
import com.wbyy.common.core.utils.file.FileUtils;
import com.wbyy.common.qyweixin.config.WxCpConfiguration;
import com.wbyy.common.redis.service.RedisService;
import com.wbyy.common.security.utils.SecurityUtils;
import com.wbyy.system.api.FileApi;
import com.wbyy.system.api.domain.ApiSysFile;
import com.wbyy.system.domain.*;
import com.wbyy.system.mapper.SysUserDeptMapper;
import com.wbyy.system.mapper.SysUserPostMapper;
import com.wbyy.system.mapper.SysUserRoleMapper;
import com.wbyy.system.model.AsyncDeptDto;
import com.wbyy.system.model.AsyncUserDto;
import com.wbyy.system.model.DeptTree;
import com.wbyy.system.service.AddressBookService;
import com.wbyy.system.service.ISysDeptService;
import com.wbyy.system.service.ISysPostService;
import com.wbyy.system.service.ISysUserService;
import com.wbyy.weaver.api.DepartmentApi;
import com.wbyy.weaver.api.EmployeeApi;
import com.wbyy.weaver.api.PositionApi;
import com.wbyy.weaver.api.WeaverFileApi;
import com.wbyy.weaver.api.domain.RemoteDeptInfo;
import com.wbyy.weaver.api.domain.RemoteEmployeeData;
import com.wbyy.weaver.api.model.EmployeeData;
import com.wbyy.weaver.api.model.dto.QueryEmployeeDto;
import com.wbyy.weaver.api.model.dto.UserDto;
import com.wbyy.weaver.api.model.vo.FileData;
import com.wbyy.weaver.api.model.vo.UserVo;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.cp.api.WxCpService;
import me.chanjar.weixin.cp.api.WxCpUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.web.multipart.MultipartFile;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.wbyy.common.core.constant.Constants.*;

/**
 * @author: 王金都
 * @date: 2025/4/28 8:55
 */
@Service("weaverAddressBookService")
@Slf4j
public class WeaverAddressBookServiceImpl implements AddressBookService {

    @Autowired
    private ISysDeptService sysDeptService;
    @Autowired
    private ISysUserService sysUserService;
    @Autowired
    private SysUserDeptMapper userDeptMapper;
    @Autowired
    private SysUserRoleMapper sysUserRoleMapper;
    @Autowired
    private TransactionTemplate transactionTemplate;
    @Autowired
    private DepartmentApi departmentApi;
    @Autowired
    private EmployeeApi employeeApi;
    @Autowired
    private PositionApi positionApi;
    @Autowired
    private WeaverFileApi weaverFileApi;
    @Autowired
    private FileApi fileApi;
    @Autowired
    private ISysPostService sysPostService;
    @Autowired
    private SysUserPostMapper sysUserPostMapper;
    @Autowired
    private RedisService redisService;

    private static final List<String> DISABLE_PERSONAL_STATUS = List.of("6","7");

    public static final String NORMAL = "normal";

    public static final String MALE = "male";
    public static final String FEMALE = "female";

    @XxlJob(value = "weaverTxlAsyncJobHandler")
    @Override
    public ReturnT<String> async() {
        // 获取泛微部门
        R<List<RemoteDeptInfo>> deptResult;
        try {
            deptResult = departmentApi.v2List("all", null, null, SecurityConstants.INNER);
        } catch (Exception e) {
            log.error("获取泛微部门失败", e);
            return ReturnT.FAIL;
        }
        if (R.FAIL == deptResult.getCode()) {
            log.error("获取泛微部门失败,{}", deptResult.getMsg());
            return ReturnT.FAIL;
        }
        List<RemoteDeptInfo> remoteDeptList = deptResult.getData();
        // 考虑没有部门为空的可能
        if (CollectionUtil.isEmpty(remoteDeptList)) {
            log.error("获取泛微部门为空");
            return ReturnT.FAIL;
        }
        // 通过树形结构构造泛微部门的ancestors，祖级数据结构
        List<DeptTree> weaverDeptList = handleDeptList(remoteDeptList);
        // 系统部门数据
        List<SysDept> deptList = sysDeptService.list();
        // 部门idmap
        Map<Long, SysDept> deptIdMap = deptList.stream()
                .collect(Collectors.toMap(SysDept::getDeptId, Function.identity(), (o1, o2) -> o1));
        // 要新增的部门数据
        List<SysDept> saveDeptList = new ArrayList<>();
        // 要更新的部门数据
        List<SysDept> updateDeptList = new ArrayList<>();
        // 泛微部门id集合
        Set<Long> weaverDeptIdSet = new HashSet<>();
        // 系统用户数据
        List<SysUser> userList = sysUserService.list();
        // 工号map
        Map<String, SysUser> workerNumberMap = userList.stream()
                .collect(Collectors.toMap(SysUser::getWorkNumber, Function.identity(), (o1, o2) -> o1));
        // 要新增的用户数据
        List<SysUser> saveUserList = new ArrayList<>();
        // 要更新的用户数据
        List<SysUser> updateUserList = new ArrayList<>();
        // 泛微用户工号集合
        Set<String> weaverUserIdSet = new HashSet<>();
        int deptSort = 0;
        // 用户工号和泛微部门id关系映射
        Map<String, Set<Long>> workerNumberDeptIdSetMap = new HashMap<>();
        // 循环处理每个泛微部门
        for (DeptTree weaverDept : weaverDeptList) {
            Long weaverDeptId = weaverDept.getId();
            weaverDeptIdSet.add(weaverDeptId);
            SysDept findDept = deptIdMap.get(weaverDeptId);
            // 部门新增
            if (null == findDept) {
                findDept = new SysDept();
                findDept.setDeptId(weaverDeptId);
                findDept.setParentId(weaverDept.getParentId());
                findDept.setAncestors(weaverDept.getAncestors());
                findDept.setDeptName(weaverDept.getName());
                findDept.setOrderSort(deptSort);
                findDept.setLeader(weaverDept.getLeader());
                findDept.setStatus(weaverDept.getStatus());
                findDept.setDelFlag(NO_DELETE);
                findDept.setCenter(SysYesNo.N.getDictValue());
                findDept.setCreateBy(SecurityUtils.getUserId());
                findDept.setWeaverDeptId(weaverDeptId);
                saveDeptList.add(findDept);
            }
            // 部门更新
            else {
                findDept.setParentId(weaverDept.getParentId());
                findDept.setAncestors(weaverDept.getAncestors());
                findDept.setDeptName(weaverDept.getName());
                findDept.setLeader(weaverDept.getLeader());
                findDept.setStatus(weaverDept.getStatus());
                findDept.setUpdateBy(SecurityUtils.getUserId());
                updateDeptList.add(findDept);
            }
            deptSort++;
            // 根据泛微部门id获取泛微部门下人员
            R<List<UserVo>> employeeResult;
            try {
                UserDto dto = new UserDto();
                dto.setDepid(weaverDeptId);
                dto.setStatus("all");
                dto.setFetch_child("0");
                employeeResult = employeeApi.departmentUsersV3(dto, SecurityConstants.INNER);
            } catch (Exception e) {
                log.error("获取泛微用户失败", e);
                return ReturnT.FAIL;
            }
            if (R.FAIL == employeeResult.getCode()) {
                log.error("获取泛微用户失败,{}", employeeResult.getMsg());
                return ReturnT.FAIL;
            }
            List<UserVo> employeeList = employeeResult.getData();
            if (CollectionUtil.isEmpty(employeeList)) {
                log.warn("根据泛微部门【{}-{}】获取用户为空", weaverDeptId,weaverDept.getName());
                continue;
            }
            // 处理用户
            for (UserVo employee : employeeList) {
                String weaverUserId = employee.getUserid().toString();
                weaverUserIdSet.add(weaverUserId);
                // 用户-部门映射关系处理
                Set<Long> deptIdSet = workerNumberDeptIdSetMap.get(weaverUserId);
                if (CollectionUtil.isNotEmpty(deptIdSet)){
                    deptIdSet.add(weaverDeptId);
                    workerNumberDeptIdSetMap.put(weaverUserId,deptIdSet);
                    continue;
                }else {
                    deptIdSet = new HashSet<>();
                    deptIdSet.add(weaverDeptId);
                    workerNumberDeptIdSetMap.put(weaverUserId,deptIdSet);
                }
                SysUser findUser = workerNumberMap.get(employee.getJobNum());
                Integer sex = StrUtil.isBlank(employee.getSex())?2:switch (employee.getSex()){
                    case MALE -> 0;
                    case FEMALE -> 1;
                    default -> 2;
                };
                Integer status = NORMAL.equals(employee.getStatus())?ENABLE_STATUS:DISABLE_STATUS;
                // 新增用户
                if (null == findUser) {
                    findUser = new SysUser();
                    findUser.setUserId(IdUtil.getSnowflake().nextId());
                    findUser.setUserName(employee.getUsername());
                    findUser.setRealName(employee.getUsername());
                    findUser.setWorkNumber(employee.getJobNum());
                    findUser.setPhone(employee.getMobile());
                    findUser.setEmail(employee.getEmail());
                    findUser.setSex(sex);
                    findUser.setPassword(SecurityUtils.encryptPassword(DEFAULT_PASSWORD));
                    findUser.setStatus(status);
                    findUser.setDelFlag(NO_DELETE);
                    findUser.setWeaverUserId(weaverUserId);
                    findUser.setCreateBy(SecurityUtils.getUserId());
                    saveUserList.add(findUser);
                }
                // 更新用户
                else {
                    findUser.setUserName(employee.getUsername());
                    findUser.setRealName(employee.getUsername());
                    findUser.setWorkNumber(employee.getJobNum());
                    findUser.setPhone(employee.getMobile());
                    findUser.setEmail(employee.getEmail());
                    findUser.setSex(sex);
                    findUser.setStatus(status);
                    findUser.setUpdateBy(SecurityUtils.getUserId());
                    updateUserList.add(findUser);
                }
            }
        }
        Set<Long> deleteDeptIdSet = deptList.stream().map(SysDept::getDeptId)
                .filter(deptId -> !weaverDeptIdSet.contains(deptId)).collect(Collectors.toSet());
        Set<Long> deleteUserIdSet = userList.stream().filter(item -> !weaverUserIdSet.contains(item.getWeaverUserId()))
                .filter(item -> !(null!=item.getUserId()&&item.getUserId()==1L))
                .map(SysUser::getUserId).collect(Collectors.toSet());
        Set<Long> deleteUserDeptIdSet = new HashSet<>(deleteUserIdSet);
        List<SysUserDept> saveUserDeptList = new ArrayList<>();
        // 构造用户-部门关系表数据
        for (String weaverUserId : workerNumberDeptIdSetMap.keySet()) {
            Long userId = null;
            if (CollectionUtil.isNotEmpty(saveUserList)){
                userId = saveUserList.stream().filter(item->weaverUserId.equals(item.getWeaverUserId()))
                        .map(SysUser::getUserId).findFirst().orElse(null);
            }else if (CollectionUtil.isNotEmpty(updateUserList)){
                userId = updateUserList.stream().filter(item->weaverUserId.equals(item.getWeaverUserId()))
                        .map(SysUser::getUserId).findFirst().orElse(null);
            }
            if (null==userId) continue;
            Set<Long> deptIdSet = workerNumberDeptIdSetMap.get(weaverUserId);
            for (Long deptId : deptIdSet) {
                SysUserDept userDept = new SysUserDept();
                userDept.setUserId(userId);
                userDept.setDeptId(deptId);
                deleteUserDeptIdSet.add(userId);
                saveUserDeptList.add(userDept);
            }
        }
        // 新用户绑定默认角色
        List<SysUserRole> userRoleList = new ArrayList<>();
        Set<Long> saveUserIdSet = saveUserList.stream().map(SysUser::getUserId).collect(Collectors.toSet());
        for (Long userId : saveUserIdSet) {
            SysUserRole userRole = new SysUserRole();
            userRole.setRoleId(NORMAL_EMPLOYEE_ROLE);
            userRole.setUserId(userId);
            userRoleList.add(userRole);
        }
        return transactionTemplate.execute(status -> {
            try {
                if (CollectionUtil.isNotEmpty(deleteUserIdSet)){
                    sysUserService.deleteFromRemoteByUserIdSet(deleteUserIdSet);
                }
                if (CollectionUtil.isNotEmpty(deleteDeptIdSet)){
                    sysDeptService.removeBatchByIds(deleteDeptIdSet);
                }
                if (CollectionUtil.isNotEmpty(deleteUserDeptIdSet)){
                    userDeptMapper.deleteUserDept(deleteUserDeptIdSet.toArray(Long[]::new));
                }
                if (CollectionUtil.isNotEmpty(saveDeptList)){
                    sysDeptService.saveBatchFromRemote(saveDeptList);
                }
                if (CollectionUtil.isNotEmpty(updateDeptList)){
                    sysDeptService.updateBatchFromRemote(updateDeptList);
                }
                if (CollectionUtil.isNotEmpty(saveUserList)){
                    sysUserService.saveBatchFromRemote(saveUserList);
                }
                if (CollectionUtil.isNotEmpty(updateUserList)){
                    sysUserService.updateBatchFromRemote(updateUserList);
                }
                if (CollectionUtil.isNotEmpty(saveUserDeptList)){
                    userDeptMapper.batchUserDept(saveUserDeptList);
                }
                if (CollectionUtil.isNotEmpty(userRoleList)){
                    sysUserRoleMapper.batchUserRole(userRoleList);
                }
                asyncPost();
                asyncUserid();
            }catch (Exception e){
                log.error("数据库操作失败",e);
                status.setRollbackOnly();
                return ReturnT.FAIL;
            }
            log.info("企微通讯录同步成功");
            return ReturnT.SUCCESS;
        });
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean asyncUser(List<AsyncUserDto> dto) {
        Set<String> jobNumSet = dto.stream().map(AsyncUserDto::getJobNum).collect(Collectors.toSet());
        // 按工号获取用户
        List<SysUser> sysUserList = sysUserService.listByWorkNumbers(jobNumSet);
        List<SysUser> saveUserList = new ArrayList<>();
        List<SysUser> updateUserList = new ArrayList<>();
        List<SysUserDept> saveUserDeptList = new ArrayList<>();
        List<SysUserPost> saveUserPostList = new ArrayList<>();
        Set<Long> userIdList = new HashSet<>();
        SimpleDateFormat sdf = new SimpleDateFormat("EEE MMM dd HH:mm:ss zzz yyyy", Locale.ENGLISH);
        sdf.setTimeZone(TimeZone.getTimeZone("Asia/Shanghai"));
        for (AsyncUserDto userDto : dto) {
            String jobNum = userDto.getJobNum();
            SysUser findUser = sysUserList.stream().filter(item -> jobNum.equals(item.getWorkNumber()))
                    .findFirst().orElse(null);
            Integer sex = StrUtil.isBlank(userDto.getSex())?2:switch (userDto.getSex()){
                case MALE -> 0;
                case FEMALE -> 1;
                default -> 2;
            };
            Integer status = StrUtil.isBlank(userDto.getStatus())?
                    DISABLE_STATUS:
                    (DISABLE_PERSONAL_STATUS.contains(userDto.getStatus())?DISABLE_STATUS:ENABLE_STATUS);
            WxCpService cpService = WxCpConfiguration.getCpService(AppCodeConst.MESSAGE);
            WxCpUserService userService = cpService.getUserService();
            String qwUserId="";
            // 只有启用状态，即在职状态的用户去请求企微用户id
            if (StrUtil.isNotBlank(userDto.getMobile())&&status.equals(ENABLE_STATUS)&&!"13223456789".equals(userDto.getMobile())){
                try {
                    qwUserId = userService.getUserId(userDto.getMobile());
                } catch (WxErrorException e) {
                    log.error("根据手机号【{}】获取企微userid失败",userDto.getMobile(),e);
                }
            }
            // 获取头像
            String avatar = userDto.getAvatar();
            String avatarUrl="";
            if (StrUtil.isNotBlank(avatar)){
                avatarUrl = uploadAvatar(avatar,userDto.getUserid());
            }
            // 入离职时间
            Date hireDate = null;
            if (StrUtil.isNotBlank(userDto.getHireDate())){
                try {
                    hireDate = sdf.parse(userDto.getHireDate());
                } catch (ParseException e) {
                    log.error("OA入职时间【{}】转Date失败",userDto.getHireDate(),e);
                }
            }
            Date departureDate = null;
            if (StrUtil.isNotBlank(userDto.getDepartureDate())){
                try {
                    departureDate = DateUtil.parseDate(userDto.getDepartureDate());
                } catch (Exception e) {
                    log.error("OA离职时间【{}】转Date失败",userDto.getDepartureDate(),e);
                }
            }
            if (null==findUser){
                findUser = new SysUser();
                findUser.setUserId(IdUtil.getSnowflake().nextId());
                findUser.setPassword(SecurityUtils.encryptPassword(DEFAULT_PASSWORD));
                findUser.setDelFlag(NO_DELETE);
                findUser.setCreateBy(SecurityUtils.getUserId());
                findUser.setWorkNumber(jobNum);
                findUser.setSex(sex);
                findUser.setPhone(userDto.getMobile());
                findUser.setWeaverUserId(userDto.getUserid());
                findUser.setEmail(userDto.getEmail());
                findUser.setStatus(status);
                findUser.setQwUserId(qwUserId);
                findUser.setUserName(userDto.getUsername());
                findUser.setRealName(userDto.getUsername());
                findUser.setAvatar(avatarUrl);
                findUser.setDepartureDate(departureDate);
                findUser.setHireDate(hireDate);
                saveUserList.add(findUser);
            }else {
                findUser.setUpdateBy(SecurityUtils.getUserId());
                findUser.setWorkNumber(jobNum);
                findUser.setSex(sex);
                findUser.setPhone(userDto.getMobile());
                findUser.setWeaverUserId(userDto.getUserid());
                findUser.setEmail(userDto.getEmail());
                findUser.setStatus(status);
                findUser.setQwUserId(qwUserId);
                findUser.setUserName(userDto.getUsername());
                findUser.setRealName(userDto.getUsername());
                findUser.setAvatar(avatarUrl);
                findUser.setDepartureDate(departureDate);
                findUser.setHireDate(hireDate);
                updateUserList.add(findUser);
            }
            userIdList.add(findUser.getUserId());
            if (StrUtil.isNotBlank(userDto.getDeptId())){
                SysUserDept userDept = new SysUserDept();
                userDept.setDeptId(Long.valueOf(userDto.getDeptId()));
                userDept.setUserId(findUser.getUserId());
                saveUserDeptList.add(userDept);
            }
            if (StrUtil.isNotBlank(userDto.getPosition())){
                SysUserPost userPost = new SysUserPost();
                userPost.setPostId(Long.valueOf(userDto.getPosition()));
                userPost.setUserId(findUser.getUserId());
                saveUserPostList.add(userPost);
            }
        }
        // 新用户绑定默认角色
        List<SysUserRole> userRoleList = new ArrayList<>();
        Set<Long> saveUserIdSet = saveUserList.stream().map(SysUser::getUserId).collect(Collectors.toSet());
        for (Long userId : saveUserIdSet) {
            SysUserRole userRole = new SysUserRole();
            userRole.setRoleId(NORMAL_EMPLOYEE_ROLE);
            userRole.setUserId(userId);
            userRoleList.add(userRole);
        }
        if (CollectionUtil.isNotEmpty(userIdList)){
            userDeptMapper.deleteUserDept(userIdList.toArray(Long[]::new));
            sysUserPostMapper.deleteUserPost(userIdList.toArray(Long[]::new));
        }
        if (CollectionUtil.isNotEmpty(saveUserList)){
            for (SysUser user : saveUserList) {
                if (null != user.getHireDate()) {
                    redisService.setCacheObject(String.format(UserConstants.HIRE_TIME_CACHE_KEY,
                                    user.getUserId().toString()),
                            DateUtil.formatDateTime(user.getHireDate()));
                }
                if (null!=user.getDepartureDate()){
                    redisService.setCacheObject(String.format(UserConstants.DEPARTURE_TIME_CACHE_KEY,
                                    user.getUserId().toString()),
                            DateUtil.formatDateTime(user.getHireDate()));
                }
            }
            sysUserService.saveBatchFromRemote(saveUserList);
        }
        if (CollectionUtil.isNotEmpty(updateUserList)){
            for (SysUser user : updateUserList) {
                if (null != user.getHireDate()) {
                    redisService.setCacheObject(String.format(UserConstants.HIRE_TIME_CACHE_KEY,
                                    user.getUserId().toString()),
                            DateUtil.formatDateTime(user.getHireDate()));
                }
                if (null!=user.getDepartureDate()){
                    redisService.setCacheObject(String.format(UserConstants.DEPARTURE_TIME_CACHE_KEY,
                                    user.getUserId().toString()),
                            DateUtil.formatDateTime(user.getHireDate()));
                }
            }
            sysUserService.updateBatchFromRemote(updateUserList);
        }
        if (CollectionUtil.isNotEmpty(saveUserDeptList)){
            // 按组合键去重
            saveUserDeptList = new ArrayList<>(saveUserDeptList.stream()
                    .collect(Collectors.toMap(
                            item -> item.getUserId() + "_" + item.getDeptId(), // 组合键
                            item -> item,
                            (oldValue, newValue) -> oldValue // 重复时保留旧值
                    ))
                    .values());
            userDeptMapper.batchUserDept(saveUserDeptList);
        }
        if (CollectionUtil.isNotEmpty(userRoleList)){
            userRoleList = new ArrayList<>(userRoleList.stream()
                    .collect(Collectors.toMap(
                            item -> item.getUserId() + "_" + item.getRoleId(), // 组合键
                            item -> item,
                            (oldValue, newValue) -> oldValue // 重复时保留旧值
                    ))
                    .values());
            sysUserRoleMapper.batchUserRole(userRoleList);
        }
        if (CollectionUtil.isNotEmpty(saveUserPostList)){
            saveUserPostList =  new ArrayList<>(saveUserPostList.stream()
                    .collect(Collectors.toMap(
                            item -> item.getUserId() + "_" + item.getPostId(), // 组合键
                            item -> item,
                            (oldValue, newValue) -> oldValue // 重复时保留旧值
                    ))
                    .values());
            sysUserPostMapper.insert(saveUserPostList);
        }

        return true;
    }

    private String uploadAvatar(String avatarId,String weaverUserId){
        R<FileData> r = weaverFileApi.downloadFileV2(avatarId, weaverUserId, SecurityConstants.INNER);
        if (R.isError(r)) {
            log.error("获取用户【{}】头像【{}】失败,失败原因：{}",weaverUserId,avatarId,r.getMsg());
            return "";
        }
        FileData data = r.getData();
        MultipartFile file = new MockMultipartFile("file", data.getFilename(), data.getContentType(), data.getBytes());
        R<ApiSysFile> fileR = fileApi.uploadFile("/system/avatar/", file,SecurityConstants.INNER);
        if (R.isError(fileR)) throw new ServiceException("上传用户头像失败");
        return "/system/avatar?fileId="+fileR.getData().getFileId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean asyncDept(List<AsyncDeptDto> dto) {
        List<SysDept> deptList = sysDeptService.list();
        List<SysDept> saveDeptList = new ArrayList<>();
        List<SysDept> updateDeptList = new ArrayList<>();
        for (AsyncDeptDto deptDto : dto) {
            Long id = Long.valueOf(deptDto.getId());
            SysDept findDept = deptList.stream().filter(item -> item.getDeptId().equals(id))
                    .findFirst().orElse(null);
            Long parentId = StrUtil.isBlank(deptDto.getParentId()) ? null : Long.parseLong(deptDto.getParentId());
            String ancestors = "";
            if (null!=parentId){
                SysDept parent = deptList.stream().filter(item -> parentId.equals(item.getDeptId()))
                        .findFirst().orElse(null);
                if (null!=parent){
                    String parentAncestors = parent.getAncestors();
                    if (StrUtil.isNotBlank(parentAncestors)){
                        ancestors = parentAncestors+","+id;
                    }
                }
            }else {
                ancestors="id";
            }

            // 部门新增
            if (null == findDept) {
                findDept = new SysDept();
                findDept.setDeptId(id);
                findDept.setParentId(parentId);
                findDept.setDeptName(deptDto.getName());
                findDept.setStatus(deptDto.getStatus()?ENABLE_STATUS:DISABLE_STATUS);
                findDept.setDelFlag(deptDto.getDelete()?DELETE:NO_DELETE);
                findDept.setCenter(SysYesNo.N.getDictValue());
                findDept.setCreateBy(SecurityUtils.getUserId());
                findDept.setWeaverDeptId(id);
                findDept.setAncestors(ancestors);
                saveDeptList.add(findDept);
            }
            // 部门更新
            else {
                findDept.setParentId(parentId);
                findDept.setDeptName(deptDto.getName());
                findDept.setStatus(deptDto.getStatus()?ENABLE_STATUS:DISABLE_STATUS);
                findDept.setDelFlag(deptDto.getDelete()?DELETE:NO_DELETE);
                findDept.setUpdateBy(SecurityUtils.getUserId());
                findDept.setAncestors(ancestors);
                updateDeptList.add(findDept);
            }
        }
        if (CollectionUtil.isNotEmpty(saveDeptList)){
            sysDeptService.saveBatchFromRemote(saveDeptList);
        }
        if (CollectionUtil.isNotEmpty(updateDeptList)){
            sysDeptService.updateBatchFromRemote(updateDeptList);
        }
        return true;
    }

    @Override
    public void asyncUserid() {
        QueryEmployeeDto dto = new QueryEmployeeDto();
        dto.setMultiFieldList(List.of("id","job_num","position"));
        dto.setCurrent(1);
        dto.setPageSize(1000);
        R<RemoteEmployeeData> query = employeeApi.query(dto,SecurityConstants.INNER);
        if (R.FAIL==query.getCode()) throw new ServiceException("获取泛微用户id失败");
        RemoteEmployeeData data = query.getData();
        List<EmployeeData> employees = data.getData();
        List<EmployeeData> updateUserIds = employees.stream().filter(item -> StrUtil.isNotBlank(item.getJob_num()) && StrUtil.isNotBlank(item.getId()))
                .toList();
        Map<String, String> jobNumPositionMap = updateUserIds.stream()
                .filter(item->StrUtil.isNotBlank(item.getPosition()))
                .collect(Collectors.toMap(EmployeeData::getJob_num, EmployeeData::getPosition, (o1, o2) -> o1));
        Set<String> workNumbers = updateUserIds.stream().map(EmployeeData::getJob_num).collect(Collectors.toSet());
        List<SysUser> users = sysUserService.listByWorkNumbers(workNumbers);
        List<SysUserPost> saveUserPosts = new ArrayList<>();
        for (SysUser user : users) {
            String postId = jobNumPositionMap.get(user.getWorkNumber());
            if (StrUtil.isBlank(postId)) continue;
            SysUserPost userPost = new SysUserPost();
            userPost.setPostId(Long.valueOf(postId));
            userPost.setUserId(user.getUserId());
            saveUserPosts.add(userPost);
        }
        sysUserService.updateWeaverUserId(updateUserIds);

        if (CollectionUtil.isNotEmpty(saveUserPosts)){
            sysUserPostMapper.deleteUserPostByWorkNumbers(workNumbers);
            sysUserPostMapper.insert(saveUserPosts);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void asyncPost(){
        R<List<RemoteDeptInfo>> listR = positionApi.listPositionV2(SecurityConstants.INNER);
        if (listR.getCode()==R.FAIL) throw new ServiceException("获取泛微岗位列表失败");
        List<RemoteDeptInfo> data = listR.getData();
        List<SysPost> saveList = new ArrayList<>();
        for (int i = 0; i < data.size(); i++) {
            RemoteDeptInfo info = data.get(i);
            SysPost post = new SysPost();
            post.setPostId(Long.valueOf(info.getId()));
            post.setPostCode(info.getName());
            post.setPostName(info.getName());
            post.setPostSort(i+2);
            post.setStatus(ENABLE_STATUS);
            saveList.add(post);
        }

        if (CollectionUtil.isNotEmpty(saveList)){
            sysPostService.remove(new LambdaQueryWrapper<SysPost>().ne(SysPost::getPostId,1L));
            sysPostService.saveBatch(saveList);
        }
    }


    /**
     * @param remoteDeptList
     * @Description: 处理部门集合，为了构建ancestors
     * @return: List<DeptTree>
     * @author: 王金都
     * @Date: 2025/4/28 9:59
     */
    private List<DeptTree> handleDeptList(List<RemoteDeptInfo> remoteDeptList) {
        DeptTree deptTree = convertToDeptTree(remoteDeptList);
        List<DeptTree> result = new ArrayList<>();
        flattenTreeHelper(deptTree, result);
        return result;
    }

    /**
     * @param remoteDeptInfos
     * @Description: 转树
     * @return: DeptTree
     * @author: 王金都
     * @Date: 2025/4/28 9:57
     */
    private DeptTree convertToDeptTree(List<RemoteDeptInfo> remoteDeptInfos) {
        Map<String, DeptTree> deptTreeMap = new HashMap<>();
        DeptTree root = null;

        // 创建映射
        for (RemoteDeptInfo remoteDeptInfo : remoteDeptInfos) {
            DeptTree deptTree = new DeptTree();
            deptTree.setId(Long.parseLong(remoteDeptInfo.getId()));
            deptTree.setName(remoteDeptInfo.getName());
            deptTree.setParentId(remoteDeptInfo.getParent() != null ? Long.parseLong(remoteDeptInfo.getParent().getId()) : null);
            deptTree.setStatus(remoteDeptInfo.isStatus() ? ENABLE_STATUS : DISABLE_STATUS);
            deptTree.setDelFlag(remoteDeptInfo.isDelete() ? DELETE : NO_DELETE);

            deptTreeMap.put(remoteDeptInfo.getId(), deptTree);
        }

        // 构建树
        for (DeptTree deptTree : deptTreeMap.values()) {
            if (deptTree.getParentId() != null) {
                DeptTree parent = deptTreeMap.get(deptTree.getParentId().toString());
                if (parent != null) {
                    parent.getChildren().add(deptTree);
                }
            } else {
                root = deptTree; // 根节点
            }
        }
        // 设置每个节点的 ancestors 字段
        setAncestors(root, "");
        return root;
    }
}
