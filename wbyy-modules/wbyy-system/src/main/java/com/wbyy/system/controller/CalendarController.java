package com.wbyy.system.controller;

import com.wbyy.common.core.domain.R;
import com.wbyy.common.core.utils.poi.ExcelUtil;
import com.wbyy.common.core.web.controller.BaseController;
import com.wbyy.common.core.web.page.TableDataInfo;
import com.wbyy.common.log.annotation.Log;
import com.wbyy.common.log.enums.BusinessType;
import com.wbyy.common.security.annotation.InnerAuth;
import com.wbyy.common.security.annotation.RequiresPermissions;
import com.wbyy.system.api.domain.ApiCalendar;
import com.wbyy.system.domain.Calendar;
import com.wbyy.system.domain.vo.CalendarVO;
import com.wbyy.system.service.ICalendarService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * 系统日历Controller
 *
 * <AUTHOR>
 * @date 2025-04-18
 */
@RestController
@RequiredArgsConstructor
@Tag(name = "系统日历")
@RequestMapping("/calendar")
public class CalendarController extends BaseController {

    private final ICalendarService calendarService;

    /**
     * 查询系统日历列表
     */
    @Operation(summary = "分页列表")
    @RequiresPermissions("system:calendar:list")
    @GetMapping("/page")
    public TableDataInfo page(Calendar calendar) {
        startPage();
        List<Calendar> list = calendarService.selectCalendarList(calendar);
        return getDataTable(list);
    }

    /**
     * 导出系统日历列表
     */
    @Operation(summary = "导出列表")
    @RequiresPermissions("system:calendar:export")
    @Log(title = "系统日历", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Calendar calendar) {
        List<Calendar> list = calendarService.selectCalendarList(calendar);
        ExcelUtil<Calendar> util = new ExcelUtil<Calendar>(Calendar.class);
        util.exportExcel(response, list, "系统日历数据");
    }

    /**
     * 获取系统日历详细信息
     */
    @Operation(summary = "获取详细信息")
    @RequiresPermissions("system:calendar:query")
    @GetMapping(value = "/{id}")
    public R<Calendar> getInfo(@PathVariable("id") Long id) {
        return R.ok(calendarService.selectCalendarById(id));
    }

    /**
     * 新增系统日历
     */
    @Operation(summary = "新增")
    @RequiresPermissions("system:calendar:add")
    @Log(title = "系统日历", businessType = BusinessType.INSERT)
    @PostMapping
    public R<Boolean> add(@RequestBody @Validated Calendar calendar) {
        return R.ok(calendarService.insertCalendar(calendar));
    }

    /**
     * 修改系统日历
     */
    @Operation(summary = "修改")
    @RequiresPermissions("system:calendar:edit")
    @Log(title = "系统日历", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<Boolean> edit(@RequestBody @Validated Calendar calendar) {
        return R.ok(calendarService.updateCalendar(calendar));
    }

    /**
     * 删除系统日历
     */
    @Operation(summary = "删除")
    @RequiresPermissions("system:calendar:remove")
    @Log(title = "系统日历", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Boolean> remove(@PathVariable Long[] ids) {
        return R.ok(calendarService.deleteCalendarByIds(ids));
    }


    @Operation(summary = "查询节假日信息")
    @GetMapping(value = "/info/{year}/{calendarType}")
    public R<Map<String, CalendarVO>> getByYear(
            @Parameter(description = "年份：2025", required = true)
            @PathVariable("year") String year,
            @Parameter(description = "日历类型）", required = true)
            @PathVariable("calendarType") String calendarType
    ) {
        return R.ok(calendarService.getByYear(year, calendarType));
    }

    @Operation(summary = "下载模板")
    @PostMapping("/import-template")
    public void importTemplate(HttpServletResponse response) throws IOException {
        ExcelUtil<Calendar> util = new ExcelUtil<Calendar>(Calendar.class);
        util.importTemplateExcel(response, "系统日历");
    }

    @Log(title = "系统日历", businessType = BusinessType.IMPORT)
    @Operation(summary = "导入")
    @RequiresPermissions("system:calendar:import")
    @PostMapping("/import-data")
    @Parameters({
            @Parameter(name = "year",description = "年份"),
            @Parameter(name = "calenderType",description = "日历类型"),
    })
    public R<String> importData(
            @RequestPart(name = "file") MultipartFile file,
            String year, String calenderType) throws Exception {
        ExcelUtil<Calendar> util = new ExcelUtil<Calendar>(Calendar.class);
        List<Calendar> calendars = util.importExcel(file.getInputStream());

        String message = calendarService.importData(calendars, year, calenderType);
        return R.ok(message);
    }

    @GetMapping("list")
    @Operation(summary = "按时间范围查询列表")
    @InnerAuth
    public R<List<ApiCalendar>> list(@RequestParam(value = "startDate",required = false)String startDate, @RequestParam(value = "endDate",required = false)String endDate){
        return R.ok(calendarService.list(startDate,endDate));
    }

    @GetMapping("/async")
    @SneakyThrows
    public R<?> async(@RequestParam("year")String year, @RequestParam("calendarType")String calendarType){
        calendarService.async(year,calendarType);
        return R.ok();
    }
}
