package com.wbyy.system.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wbyy.common.core.exception.ServiceException;
import com.wbyy.common.core.utils.DateUtils;
import com.wbyy.common.core.utils.StringUtils;
import com.wbyy.common.security.utils.SecurityUtils;
import com.wbyy.system.api.domain.ApiCalendar;
import com.wbyy.system.domain.Calendar;
import com.wbyy.system.domain.vo.CalendarVO;
import com.wbyy.system.mapper.CalendarMapper;
import com.wbyy.system.service.ICalendarService;
import com.wbyy.system.utils.HolidayUtil;
import com.wbyy.system.utils.vo.HolidayVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.rmi.ServerException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 系统日历Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-04-18
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CalendarServiceImpl extends ServiceImpl<CalendarMapper, Calendar> implements ICalendarService {
    private final CalendarMapper calendarMapper;

    /**
     * 查询系统日历
     *
     * @param id 系统日历主键
     * @return 系统日历
     */
    @Override
    public Calendar selectCalendarById(Long id) {
        return this.getById(id);
    }

    /**
     * 查询系统日历列表
     *
     * @param calendar 系统日历
     * @return 系统日历
     */
    @Override
    public List<Calendar> selectCalendarList(Calendar calendar) {
        LambdaQueryWrapper<Calendar> wrapper = Wrappers.lambdaQuery();
        wrapper.orderByDesc(Calendar::getCreateTime);
        return this.list(wrapper);
    }

    /**
     * 新增系统日历
     *
     * @param calendar 系统日历
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean insertCalendar(Calendar calendar) {
        return this.save(calendar);
    }

    /**
     * 修改系统日历
     *
     * @param calendar 系统日历
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateCalendar(Calendar calendar) {
        return this.updateById(calendar);
    }

    /**
     * 批量删除系统日历
     *
     * @param ids 需要删除的系统日历主键
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteCalendarByIds(Long[] ids) {
        return this.removeBatchByIds(Arrays.asList(ids));
    }

    /**
     * @Description: 根据年获取列表
     * @param year
     * @param calendarType
     * @return: List<Calendar>
     * @author: 王金都
     * @Date: 2025/5/15 9:23
     */
    private List<Calendar> listByYear(String year,String calendarType){
        DateTime yearDate = DateUtil.parse(year, DateUtils.YYYY);

        DateTime startDate = DateUtil.beginOfYear(yearDate);
        DateTime endDate = DateUtil.endOfYear(yearDate);

        LambdaQueryWrapper<Calendar> wrapper = Wrappers.<Calendar>lambdaQuery();
        wrapper.between(Calendar::getStartDate, startDate, endDate)
                .between(Calendar::getStartDate, startDate, endDate)
                .eq(Calendar::getCalendarType, calendarType)
                .orderByAsc(Calendar::getCreateTime);

        return this.list(wrapper);
    }

    @Override
    public Map<String, CalendarVO> getByYear(String year, String calendarType) {
        List<Calendar> list = this.listByYear(year, calendarType);
        if (CollUtil.isEmpty(list)) {
            return null;
        }
        Set<String> daySet = new HashSet<>();
        List<CalendarVO> calendarVOS = new ArrayList<>();
        for (Calendar calendar : list) {
            List<String> days = DateUtils.getDaysByDateInterval(calendar.getStartDate(), calendar.getEndDate());
            for (String day : days) {
                if (daySet.add(day)) {
                    calendarVOS.add(
                            CalendarVO.builder()
                                    .day(day)
                                    .dateType(calendar.getDateType())
                                    .holidayName(calendar.getHolidayName())
                                    .build()
                    );
                }
            }
        }
        return calendarVOS.stream().collect(Collectors.toMap(CalendarVO::getDay, item -> item));
    }

    @Override
    public String importData(List<Calendar> calendars, String year, String calenderType) {
        if (StringUtils.isNull(calendars) || calendars.isEmpty()) {
            throw new ServiceException("导入系统日历数据不能为空！");
        }
        Long operId = SecurityUtils.getUserId();

        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        for (Calendar calendar : calendars) {
            try {
                calendar.setCalendarType(calenderType);
                // 保存数据
                this.save(calendar);

            } catch (Exception e) {
                failureNum++;
                String msg = "<br/>" + failureNum + "、节假日名称：" + calendar.getHolidayName() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
                log.error(msg, e);
            }
        }
        if (failureNum > 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        } else {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }

    @Override
    public List<ApiCalendar> list(String startDate, String endDate){
        LambdaQueryWrapper<Calendar> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.ge(StrUtil.isNotBlank(startDate),Calendar::getStartDate,startDate);
        queryWrapper.le(StrUtil.isNotBlank(endDate),Calendar::getEndDate,endDate);
        queryWrapper.orderByAsc(Calendar::getStartDate);
        List<Calendar> list = this.list(queryWrapper);
        List<ApiCalendar> result = new ArrayList<>();
        for (Calendar day : list) {
            ApiCalendar calendar = new ApiCalendar();
            calendar.setDate(day.getStartDate());
            calendar.setDateType(day.getDateType());
            result.add(calendar);
        }
        return result;
    }

    private static final List<String> WORKDAY_STATUS = List.of("0","3");

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void async(String year,String calendarType) throws IOException {
        List<Calendar> exits = listByYear(year, calendarType);
        if (CollectionUtil.isNotEmpty(exits)) throw new ServerException("已有"+year+"数据");
        ArrayList<HolidayVo> holidayVos = HolidayUtil.getAllHolidayByYear(year);
        List<Calendar> saveList = new ArrayList<>();
        for (HolidayVo vo : holidayVos) {
            Calendar item = new Calendar();
            item.setCalendarType(calendarType);
            item.setHolidayName(vo.getMsg());
            DateTime date = DateUtil.parse(vo.getDate(), "yyyy-MM-dd");
            item.setStartDate(date);
            item.setEndDate(date);
            item.setDateType(WORKDAY_STATUS.contains(vo.getStatus())?2:1);
            saveList.add(item);
        }
        if (CollectionUtil.isEmpty(saveList)) throw new ServiceException("没有可落库的数据");
        this.saveBatch(saveList);
    }


}
