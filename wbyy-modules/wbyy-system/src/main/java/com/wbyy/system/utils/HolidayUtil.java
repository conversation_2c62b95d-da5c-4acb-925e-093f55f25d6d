package com.wbyy.system.utils;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.wbyy.system.utils.vo.HolidayVo;
import lombok.extern.slf4j.Slf4j;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * @author: 王金都
 * @date: 2025/5/15 9:08
 */
@Slf4j
public class HolidayUtil {
    private static String get(String url) throws IOException {
        StringBuilder inputLine = new StringBuilder();
        String read;
        HttpURLConnection urlConnection = (HttpURLConnection) new URL(url).openConnection();
        urlConnection.setReadTimeout(30000);
        urlConnection.setConnectTimeout(30000);
        urlConnection.setRequestProperty("Charset", "UTF-8");
        urlConnection.setRequestProperty("User-Agent", "Mozilla/5.0");
        BufferedReader in = new BufferedReader(new InputStreamReader(urlConnection.getInputStream(), StandardCharsets.UTF_8));
        while ((read = in.readLine()) != null) {
            inputLine.append(read);
        }
        in.close();
        return inputLine.toString();
    }

    public static ArrayList<HolidayVo> getAllHolidayByYear(String year) throws IOException {
        SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd");
        ArrayList<HolidayVo> holidayVoList = new ArrayList<>();
        HashMap<String, HolidayVo> hashMap = new HashMap<>();

        // 查询全年日历包含周末
        String allDayJson = get("https://api.apihubs.cn/holiday/get?size=500&year=" + year);
        ObjectMapper mapper = new ObjectMapper();
        Map allDayMap = mapper.readValue(allDayJson, Map.class);
        Map allDayData = (Map) allDayMap.get("data");
        List allDayDataList = (List) allDayData.get("list");
        allDayDataList.forEach((value) -> {
            HolidayVo holidayVo = new HolidayVo();
            Map value1 = (Map) value;
            String YEAR = value1.get("year").toString();
            String MONTH = value1.get("month").toString().replace(YEAR, "");
            String DAY = value1.get("date").toString().replace(YEAR + MONTH, "");
            holidayVo.setDate(YEAR + "-" + MONTH + "-" + DAY);
            String STATUS = "0";
            String msg = "工作日";
            if ("1".equals(value1.get("weekend").toString())) {
                STATUS = "1";
                msg = "周末";
            }
            holidayVo.setStatus(STATUS);
            holidayVo.setMsg(msg);
            hashMap.put(holidayVo.getDate(), holidayVo);
        });

        // 查询全年节假日、调休
        String holidayJson = get("https://timor.tech/api/holiday/year/" + year);
        Map holidayMap = mapper.readValue(holidayJson, Map.class);
        LinkedHashMap holidayList = (LinkedHashMap) holidayMap.get("holiday");
        holidayList.forEach((key, value) -> {
            HolidayVo holidayVo = new HolidayVo();
            Map value1 = (Map) value;
            String dateTime = value1.get("date").toString();
            holidayVo.setDate(dateTime);
            String STATUS = "2";
            String msg = "法定节假日(" + value1.get("name").toString() + ")";
            if (value.toString().contains("调休") || value.toString().contains("补班")) {
                STATUS = "3";
                msg = "节假日调休补班(" + value1.get("target").toString() + ")";
            }
            holidayVo.setStatus(STATUS);
            holidayVo.setMsg(msg);
            hashMap.replace(holidayVo.getDate(), holidayVo);
        });

        for (String key : hashMap.keySet()) {
            holidayVoList.add(hashMap.get(key));
        }

        // 排序
        holidayVoList.sort((a, b) -> {
            try {
                return sf.parse(a.getDate()).compareTo(sf.parse(b.getDate()));
            } catch (ParseException e) {
                log.error("排序失败",e);
            }
            return 1;
        });

        return holidayVoList;
    }
}
