package com.wbyy.system.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wbyy.common.core.constant.SecurityConstants;
import com.wbyy.common.core.domain.R;
import com.wbyy.system.domain.SysUser;
import com.wbyy.system.domain.Vacation;
import com.wbyy.system.mapper.VacationMapper;
import com.wbyy.system.service.ISysUserService;
import com.wbyy.system.service.IVacationService;
import com.wbyy.weaver.api.AttendApi;
import com.weaver.openapi.pojo.attend.params.AttendVo;
import com.weaver.openapi.pojo.attend.res.vo.AttendResultVo;
import com.weaver.openapi.pojo.attend.res.vo.LeaveItem;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 请假Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-13
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class VacationServiceImpl extends ServiceImpl<VacationMapper, Vacation> implements IVacationService
{
    private final VacationMapper vacationMapper;
    private final AttendApi attendApi;
    private final ISysUserService sysUserService;
    private final TransactionTemplate transactionTemplate;

    /**
     * 查询请假
     * 
     * @param id 请假主键
     * @return 请假
     */
    @Override
    public Vacation selectById(Long id)
    {
        return this.getById(id);
    }

    /**
     * 查询请假列表
     * 
     * @param vacation 请假
     * @return 请假
     */
    @Override
    public List<Vacation> selectList(Vacation vacation)
    {
        LambdaQueryWrapper<Vacation> wrapper = Wrappers.lambdaQuery();
        wrapper.orderByDesc(Vacation::getCreateTime);
        return this.list(wrapper);
    }

    /**
     * 新增请假
     * 
     * @param vacation 请假
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean insert(Vacation vacation)
    {
        return this.save(vacation);
    }

    /**
     * 修改请假
     * 
     * @param vacation 请假
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean update(Vacation vacation)
    {
        return this.updateById(vacation);
    }

    /**
     * 批量删除请假
     * 
     * @param ids 需要删除的请假主键
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByIds(Long[] ids)
    {
        return this.removeBatchByIds(Arrays.asList(ids));
    }

    @Override
    public void asyncAll(String startDate, String endDate) {
        if (StrUtil.isBlank(startDate)||StrUtil.isBlank(endDate)) {
            log.warn("传入的查询时间有空");
        }
        Map<String, SysUser> weaverUserIdMap = sysUserService.list().stream()
                .filter(item -> StrUtil.isNotBlank(item.getWeaverUserId()))
                .collect(Collectors.toMap(SysUser::getWeaverUserId, Function.identity(), (o1, o2) -> o1));
        Set<String> weaverUserIds = weaverUserIdMap.keySet();
        List<AttendResultVo> attends = new ArrayList<>();
        List<List<String>> split = CollectionUtil.split(weaverUserIds, 90);
        for (List<String> strings : split) {
            AttendVo vo = new AttendVo();
            vo.setStartDate(startDate);
            vo.setEndDate(endDate);
            vo.setUserIds(strings.stream().map(Long::valueOf).toList());
            R<List<AttendResultVo>> leaveV2R = attendApi.getLeaveV2(vo, SecurityConstants.INNER);
            if (R.isSuccess(leaveV2R)) {
                log.error("获取用户请假数据失败:{}",leaveV2R.getMsg());
            }
            List<AttendResultVo> thisAttends = leaveV2R.getData();
            if (CollectionUtil.isNotEmpty(thisAttends)) {
                attends.addAll(thisAttends);
            }
        }
        if (CollectionUtil.isEmpty(attends)) {
            log.warn("获取的用户请假数据为空");
            return;
        }
        List<Vacation> saveList = new ArrayList<>();
        List<String> checkKeys = new ArrayList<>();
        for (AttendResultVo attend : attends) {
            Long weaverUserId = attend.getUserId();
            if (null == weaverUserId) {continue;}
            String weaverUserIdStr = weaverUserId.toString();
            SysUser user = weaverUserIdMap.get(weaverUserIdStr);
            if (null == user) {
                log.warn("根据泛微用户id【{}】没有找到用户",weaverUserIdStr);
                continue;
            }
            Long userId = user.getUserId();
            List<LeaveItem> leaveItems = attend.getLeaveItems();
            if (CollectionUtil.isEmpty(leaveItems)) {continue;}
            for (LeaveItem leave : leaveItems) {
                String start = leave.getStart();
                String end = leave.getEnd();
                String name = leave.getName();
                // user_id-name-start_time-end_time
                String checkKey = userId+StrUtil.DASHED+name+StrUtil.DASHED+start+StrUtil.DASHED+end;
                Vacation vacation = new Vacation();
                vacation.setVacationId(leave.getVacationId());
                vacation.setUserId(userId);
                vacation.setName(name);
                vacation.setStartTime(new Date(Long.parseLong(start)));
                vacation.setEndTime(new Date(Long.parseLong(end)));
                vacation.setTimeLength(new BigDecimal(leave.getTimeLength()));
                vacation.setCheckKey(checkKey);
                saveList.add(vacation);
                checkKeys.add(checkKey);
            }
        }
        transactionTemplate.execute(status -> {
            if(CollectionUtil.isNotEmpty(checkKeys)){
                this.deleteByCheckKeys(checkKeys);
            }
            if (CollectionUtil.isNotEmpty(saveList)) {
                this.saveBatch(saveList);
            }
            return null;
        });
    }

    /**
     * @Description: 根据checkKey删除
       * @param checkKeys
     * @return: void
     * @author: 王金都
     * @Date: 2025/6/13 16:18
     */
    private void deleteByCheckKeys(List<String> checkKeys) {
        if (CollectionUtil.isEmpty(checkKeys)) {return;}
        LambdaQueryWrapper<Vacation> wrapper = Wrappers.lambdaQuery();
        wrapper.in(Vacation::getCheckKey, checkKeys);
        this.remove(wrapper);
    }

    @Override
    @XxlJob(value = "asyncPreDayVacationJobHandler")
    public void asyncPreDay() {
        Date now = new Date();
        String date = DateUtil.formatDate(DateUtil.offsetDay(now, -1));
        this.asyncAll(date,date);
    }

    @Override
    public List<Vacation> listUserVacation(Long userId, Date startDate, Date endDate) {
        return this.baseMapper.listUserVacation(userId, startDate, endDate);
    }

    @Override
    public List<Vacation> listUsersVacation(List<Long> userIds, Date startDate, Date endDate) {
        if (CollectionUtil.isEmpty(userIds)) return List.of();
        return this.baseMapper.listUsersVacation(userIds,startDate,endDate);
    }

}
