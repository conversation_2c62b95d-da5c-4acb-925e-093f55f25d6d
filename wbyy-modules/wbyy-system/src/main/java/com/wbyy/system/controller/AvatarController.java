package com.wbyy.system.controller;

import com.wbyy.common.core.constant.SecurityConstants;
import com.wbyy.common.core.domain.R;
import com.wbyy.common.core.exception.ServiceException;
import com.wbyy.system.api.FileApi;
import com.wbyy.system.api.domain.ApiDownloadFile;
import com.wbyy.system.api.domain.ApiSysFile;
import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;

/**
 * @author: 王金都
 * @date: 2025/6/3 10:00
 */
@RestController
@RequestMapping("/avatar")
public class AvatarController {

    @Autowired
    private FileApi fileApi;

    @GetMapping
    public void getFile(@RequestParam("fileId")Long fileId, HttpServletResponse response) {
        R<ApiDownloadFile> fileR = fileApi.downloadFile(fileId, SecurityConstants.INNER);
        if (R.isError(fileR)) {
            throw new ServiceException("获取头像失败");
        }
        ApiDownloadFile downloadFile = fileR.getData();
        response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode(downloadFile.getFileName(), StandardCharsets.UTF_8));
        response.addHeader("Content-Length", "" + downloadFile.getStream().length);
        response.setContentType(downloadFile.getType());
        byte[] data = downloadFile.getStream();
        try (ServletOutputStream outputStream = response.getOutputStream()) {
            outputStream.write(data);
            outputStream.flush();
            response.flushBuffer();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

    }
}
