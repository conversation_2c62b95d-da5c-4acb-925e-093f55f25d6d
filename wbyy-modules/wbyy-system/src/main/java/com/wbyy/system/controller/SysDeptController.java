package com.wbyy.system.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.wbyy.common.core.constant.UserConstants;
import com.wbyy.common.core.domain.R;
import com.wbyy.common.core.enums.SysYesNo;
import com.wbyy.common.core.utils.StringUtils;
import com.wbyy.common.core.utils.TreeUtil;
import com.wbyy.common.core.utils.bean.BeanUtils;
import com.wbyy.common.core.web.controller.BaseController;
import com.wbyy.common.core.web.domain.AjaxResult;
import com.wbyy.common.log.annotation.Log;
import com.wbyy.common.log.enums.BusinessType;
import com.wbyy.common.security.annotation.InnerAuth;
import com.wbyy.common.security.annotation.RequiresPermissions;
import com.wbyy.common.security.utils.SecurityUtils;
import com.wbyy.system.api.domain.ApiDept;
import com.wbyy.system.domain.SysDept;
import com.wbyy.system.domain.vo.TreeSelect;
import com.wbyy.system.model.ManageDeptDto;
import com.wbyy.system.model.UserCenterDeptVo;
import com.wbyy.system.service.ISysDeptService;
import com.wbyy.system.service.ISysRoleService;
import io.swagger.v3.oas.annotations.Operation;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import static com.wbyy.common.core.constant.Constants.NO_DELETE;
import static com.wbyy.common.core.constant.Constants.Y;

/**
 * 部门信息
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/dept")
public class SysDeptController extends BaseController {
    @Autowired
    private ISysDeptService deptService;
    @Autowired
    private ISysRoleService sysRoleService;

    /**
     * 获取部门列表
     */
    @RequiresPermissions("system:dept:list")
    @GetMapping("/list")
    public AjaxResult list(SysDept dept) {
        List<SysDept> depts = deptService.selectDeptList(dept);
        return success(depts);
    }

    /**
     * 查询部门列表（排除节点）
     */
    @RequiresPermissions("system:dept:list")
    @GetMapping("/list/exclude/{deptId}")
    public AjaxResult excludeChild(@PathVariable(value = "deptId", required = false) Long deptId) {
        List<SysDept> depts = deptService.selectDeptList(new SysDept());
        depts.removeIf(d -> d.getDeptId().longValue() == deptId || ArrayUtils.contains(StringUtils.split(d.getAncestors(), ","), deptId + ""));
        return success(depts);
    }

    /**
     * 根据部门编号获取详细信息
     */
    @RequiresPermissions("system:dept:query")
    @GetMapping(value = "/{deptId}")
    @InnerAuth
    public AjaxResult getInfo(@PathVariable Long deptId) {
        deptService.checkDeptDataScope(deptId);
        return success(deptService.selectDeptById(deptId));
    }

    /**
     * 新增部门
     */
    @RequiresPermissions("system:dept:add")
    @Log(title = "部门管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody SysDept dept) {
        if (!deptService.checkDeptNameUnique(dept)) {
            return error("新增部门'" + dept.getDeptName() + "'失败，部门名称已存在");
        }
        if (!deptService.checkDeptCodeUnique(dept)) {
            return error("新增部门'" + dept.getDeptName() + "'失败，部门编码已存在");
        }
        dept.setCreateBy(SecurityUtils.getUserId());
        return toAjax(deptService.insertDept(dept));
    }

    /**
     * 修改部门
     */
    @RequiresPermissions("system:dept:edit")
    @Log(title = "部门管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody SysDept dept) {
        Long deptId = dept.getDeptId();
        deptService.checkDeptDataScope(deptId);
        if (!deptService.checkDeptNameUnique(dept)) {
            return error("修改部门'" + dept.getDeptName() + "'失败，部门名称已存在");
        } else if (dept.getParentId().equals(deptId)) {
            return error("修改部门'" + dept.getDeptName() + "'失败，上级部门不能是自己");
        } else if (UserConstants.DEPT_DISABLE.equals(dept.getStatus()) && deptService.selectNormalChildrenDeptById(deptId) > 0) {
            return error("该部门包含未停用的子部门！");
        } else if (!deptService.checkDeptCodeUnique(dept)) {
            return error("修改部门'" + dept.getDeptName() + "'失败，部门编码已存在");
        }
        dept.setUpdateBy(SecurityUtils.getUserId());
        return toAjax(deptService.updateDept(dept));
    }

    /**
     * 删除部门
     */
    @RequiresPermissions("system:dept:remove")
    @Log(title = "部门管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{deptId}")
    public AjaxResult remove(@PathVariable Long deptId) {
        if (deptService.hasChildByDeptId(deptId)) {
            return warn("存在下级部门,不允许删除");
        }
        if (deptService.checkDeptExistUser(deptId)) {
            return warn("部门存在用户,不允许删除");
        }
        deptService.checkDeptDataScope(deptId);
        return toAjax(deptService.deleteDeptById(deptId));
    }

    private static final List<Long> INCLUDE_ID_SET = Arrays.asList(1123796992247931219L, 1123796992247931254L, 1123796992247930917L, 1123796996459012713L);

    @RequiresPermissions("system:dept:list")
    @Operation(summary = "查询中心（部门）接口")
    @GetMapping("/center")
    public R<List<TreeSelect>> center(boolean hasChildren) {
        SysDept param = new SysDept();
        param.setStatus(0);
        List<SysDept> list = deptService.selectDeptList(param);
        List<SysDept> filterList = list.stream().filter(item -> SysYesNo.Y.getDictValue().equals(item.getCenter())).toList();
        if (CollectionUtil.isEmpty(filterList)) return R.ok();

        // 查询所有子集
        List<Long> centerIds = filterList.stream().map(SysDept::getDeptId).toList();
        if (hasChildren) {
            List<SysDept> centerAndChildren = deptService.selectAllChildrenByDeptIds(centerIds);

            List<SysDept> sysDepts = TreeUtil.buildTree(JSONArray.parse(JSON.toJSONString(centerAndChildren)), "deptId", "parentId", "children", SysDept.class);
            return R.ok(sysDepts.stream().map(TreeSelect::new).collect(Collectors.toList()));
        }
        List<SysDept> sysDepts = TreeUtil.buildTree(JSONArray.parse(JSON.toJSONString(filterList)), "deptId", "parentId", "children", SysDept.class);
        return R.ok(sysDepts.stream().map(TreeSelect::new).collect(Collectors.toList()));
    }

    /**
     * @Description: 获取中心及中心下部门列表
     * @param
     * @return: R<List<SysDept>>
     * @author: 王金都
     * @Date: 2025/6/11 11:35
     */
    @GetMapping("/center-list")
    @InnerAuth
    public R<List<ApiDept>> centerList(){
        SysDept param = new SysDept();
        param.setStatus(0);
        List<SysDept> list = deptService.selectDeptList(param);
        List<SysDept> filterList = list.stream().filter(item -> SysYesNo.Y.getDictValue().equals(item.getCenter())).toList();
        if (CollectionUtil.isEmpty(filterList)) return R.ok();
        List<Long> centerIds = filterList.stream().map(SysDept::getDeptId).toList();
        List<SysDept> deptList = deptService.selectAllChildrenByDeptIds(centerIds);
        List<ApiDept> result = new ArrayList<>();
        for (SysDept sysDept : deptList) {
            ApiDept  apiDept = new ApiDept();
            BeanUtils.copyProperties(sysDept,apiDept);
            result.add(apiDept);
        }
        return R.ok(result);
    }

    @RequiresPermissions("system:dept:list")
    @Operation(summary = "根据Id查询中心")
    @GetMapping("/get-center-by-id")
    @InnerAuth
    public R<ApiDept> getCenterById(@RequestParam("deptId") Long deptId) {
        SysDept one = deptService.getOne(new LambdaQueryWrapper<SysDept>()
                .eq(SysDept::getDelFlag, NO_DELETE)
                .eq(SysDept::getDeptId, deptId)
                .eq(SysDept::getCenter, Y), false);
        ApiDept result = new ApiDept();
        if (null != one) {
            BeanUtils.copyProperties(one, result);
        }
        return R.ok(result);
    }

    @Operation(summary = "根据用户Id查询中心")
    @GetMapping("/get-user-center-dept")
    public R<UserCenterDeptVo> getUserCenterDept(@RequestParam("userId") Long userId) {
        return R.ok(deptService.getUserCenterDept(userId));
    }

    @GetMapping("/manage-center-dept")
    public R<List<TreeSelect>> manageCenterDept(){
        SysDept param = new SysDept();
        param.setStatus(0);
        List<SysDept> list = deptService.selectDeptList(param);
        List<SysDept> filterList = list.stream().filter(item -> SysYesNo.Y.getDictValue().equals(item.getCenter())).toList();
        if (CollectionUtil.isEmpty(filterList)) return R.ok();
        Set<Long> centerIds = filterList.stream().map(SysDept::getDeptId).collect(Collectors.toSet());
        ManageDeptDto dto = new ManageDeptDto();
        dto.setDeptIds(centerIds);
        List<SysDept> sysDepts = TreeUtil.buildTree(JSONArray.parse(JSON.toJSONString(deptService.listManageDept(dto))), "deptId", "parentId", "children", SysDept.class);
        return R.ok(sysDepts.stream().map(TreeSelect::new).collect(Collectors.toList()));
    }
    @GetMapping("/select-by-ancestors/{deptId}")
    @InnerAuth
    public R<List<SysDept>> selectByAncestors(@PathVariable("deptId") Long deptId){
        if (deptId == null) return R.ok();

        SysDept param = new SysDept();
        param.setStatus(0);
        param.setAncestors(deptId+"");
        List<SysDept> list = deptService.selectDeptList(param);
        return R.ok(list);
    }

    @GetMapping("/find-by-deptIds")
    @InnerAuth
    public R<List<SysDept>> findByDeptIds(@RequestParam Set<Long> deptIds){
        if (CollUtil.isEmpty(deptIds)) return R.ok();
        return R.ok(deptService.listByDeptIds(deptIds));
    }
}
