package com.wbyy.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wbyy.system.api.domain.ApiUser;
import com.wbyy.system.domain.SysUser;
import com.wbyy.system.model.UpdateQwUserIdDto;
import com.wbyy.weaver.api.model.EmployeeData;

import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * 用户 业务层
 *
 * <AUTHOR>
 */
public interface ISysUserService extends IService<SysUser> {
    /**
     * 根据条件分页查询用户列表
     *
     * @param user 用户信息
     * @return 用户信息集合信息
     */
    public List<SysUser> selectUserList(SysUser user);

    /**
     * 根据条件分页查询已分配用户角色列表
     *
     * @param user 用户信息
     * @return 用户信息集合信息
     */
    public List<SysUser> selectAllocatedList(SysUser user);

    /**
     * 根据条件分页查询未分配用户角色列表
     *
     * @param user 用户信息
     * @return 用户信息集合信息
     */
    public List<SysUser> selectUnallocatedList(SysUser user);

    /**
     * 通过用户名查询用户
     *
     * @param userName 用户名
     * @return 用户对象信息
     */
    public SysUser selectUserByUserName(String userName);

    SysUser selectUser(String value);

    /**
     * 通过用户ID查询用户
     *
     * @param userId 用户ID
     * @return 用户对象信息
     */
    public SysUser selectUserById(Long userId);
    public SysUser selectUserByIdByApplication(Long userId,String applicationCode);

    /**
     * 根据用户ID查询用户所属角色组
     *
     * @param userName 用户名
     * @return 结果
     */
    public String selectUserRoleGroup(String userName);

    /**
     * 根据用户ID查询用户所属岗位组
     *
     * @param userName 用户名
     * @return 结果
     */
    public String selectUserPostGroup(String userName);

    /**
     * 校验用户名称是否唯一
     *
     * @param user 用户信息
     * @return 结果
     */
    public boolean checkUserNameUnique(SysUser user);

    /**
     * 校验手机号码是否唯一
     *
     * @param user 用户信息
     * @return 结果
     */
    public boolean checkPhoneUnique(SysUser user);

    /**
     * 校验email是否唯一
     *
     * @param user 用户信息
     * @return 结果
     */
    public boolean checkEmailUnique(SysUser user);

    /**
     * 校验用户是否允许操作
     *
     * @param user 用户信息
     */
    public void checkUserAllowed(SysUser user);

    /**
     * 校验用户是否有数据权限
     *
     * @param userId 用户id
     */
    public void checkUserDataScope(Long userId);

    /**
     * 新增用户信息
     *
     * @param user 用户信息
     * @return 结果
     */
    public int insertUser(SysUser user);

    /**
     * 注册用户信息
     *
     * @param user 用户信息
     * @return 结果
     */
    public boolean registerUser(SysUser user);

    /**
     * 修改用户信息
     *
     * @param user 用户信息
     * @return 结果
     */
    public int updateUser(SysUser user);

    /**
     * 用户授权角色
     *
     * @param userId  用户ID
     * @param roleIds 角色组
     */
    public void insertUserAuth(Long userId, Long[] roleIds);

    /**
     * 修改用户状态
     *
     * @param user 用户信息
     * @return 结果
     */
    public int updateUserStatus(SysUser user);

    /**
     * 修改用户基本信息
     *
     * @param user 用户信息
     * @return 结果
     */
    public boolean updateUserProfile(SysUser user);

    /**
     * 修改用户头像
     *
     * @param userName 用户名
     * @param avatar   头像地址
     * @return 结果
     */
    public boolean updateUserAvatar(String userName, String avatar);

    /**
     * 重置用户密码
     *
     * @param user 用户信息
     * @return 结果
     */
    public int resetPwd(SysUser user);

    /**
     * 重置用户密码
     *
     * @param userName 用户名
     * @param password 密码
     * @return 结果
     */
    public int resetUserPwd(String userName, String password);

    /**
     * 通过用户ID删除用户
     *
     * @param userId 用户ID
     * @return 结果
     */
    public int deleteUserById(Long userId);

    /**
     * 批量删除用户信息
     *
     * @param userIds 需要删除的用户ID
     * @return 结果
     */
    public int deleteUserByIds(Long[] userIds);

    /**
     * 导入用户数据
     *
     * @param userList        用户数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operId          操作用户
     * @return 结果
     */
    public String importUser(List<SysUser> userList, Boolean isUpdateSupport, Long operId);

    void deleteFromRemoteByUserIdSet(Set<Long> userIdSet);

    void saveBatchFromRemote(List<SysUser> list);

    void updateBatchFromRemote(List<SysUser> list);

    void recordlogin(Long userId, String loginIp, Date loginDate);

    List<SysUser> listByWorkNumbers(Set<String> workNums);

    /**
     * 根据用户 IDS 查询用户信息
     *
     * @param userIds 用户ID 集合
     * @return 用户信息
     */
    List<ApiUser> getUserInfo(List<Long> userIds);

    void updateWeaverUserId(List<EmployeeData> employees);

    void updateQwUserId(List<UpdateQwUserIdDto> qwUserIds);

    /**
     * @param userIds
     * @Description: 用户信息集合
     * @return: List<SysUser>
     * @author: 王金都
     * @Date: 2025/6/11 14:20
     */
    List<SysUser> infoList(List<Long> userIds);

    /**
     * 通过企微用户id查询用户
     *
     * @param qwUserId 企微用户id
     * @return 用户对象信息
     */
    SysUser selectUserByQwUserId(String qwUserId);

    boolean asyncUserHireData();

    /**
     * 通过部门id查询其下的所有用户
     *
     * @param deptId 部门id
     * @return 用户对象信息
     */
    List<ApiUser> getAllUserByDept(Long deptId, String keyword);

    List<SysUser> pageAllUserByDept(Long deptId, String keyword);

    SysUser getByWorkNumber(String workNumber);
}
