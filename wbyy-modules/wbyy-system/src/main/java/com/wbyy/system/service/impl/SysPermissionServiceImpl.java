package com.wbyy.system.service.impl;

import java.util.*;
import java.util.stream.Collectors;

import com.wbyy.system.domain.SysDept;
import com.wbyy.system.domain.SysPost;
import com.wbyy.system.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import com.wbyy.system.domain.SysRole;
import com.wbyy.system.domain.SysUser;

/**
 * 用户权限处理
 * 
 * <AUTHOR>
 */
@Service
public class SysPermissionServiceImpl implements ISysPermissionService
{
    @Autowired
    private ISysRoleService roleService;

    @Autowired
    private ISysMenuService menuService;

    @Autowired
    private ISysDeptService deptService;

    @Autowired
    private ISysPostService postService;

    /**
     * 获取角色数据权限
     * 
     * @param userId 用户Id
     * @return 角色权限信息
     */
    @Override
    public Set<String> getRolePermission(SysUser user)
    {
        Set<String> roles = new HashSet<String>();
        // 管理员拥有所有权限
        if (null!=user.getUserId()&&1L==user.getUserId())
        {
            roles.add("admin");
        }
        else
        {
            roles.addAll(roleService.selectRolePermissionByUserId(user.getUserId()));
        }
        return roles;
    }

    @Override
    public Set<String> getRolePermissionByApplicationCode(SysUser user, String applicationCode) {
        Set<String> roles = new HashSet<String>();
        // 管理员拥有所有权限
        if (null!=user.getUserId()&&1L==user.getUserId())
        {
            roles.add("admin");
        }
        else
        {
            roles.addAll(roleService.selectRolePermissionByUserIdByApplication(user.getUserId(),applicationCode));
        }
        return roles;
    }

    /**
     * 获取菜单数据权限
     * 
     * @param userId 用户Id
     * @return 菜单权限信息
     */
    @Override
    public Set<String> getMenuPermission(SysUser user)
    {
        Set<String> perms = new HashSet<String>();
        // 管理员拥有所有权限
        if (null!=user.getUserId()&&1L==user.getUserId())
        {
            perms.add("*:*:*");
        }
        else
        {
            List<SysRole> roles = user.getRoles();
            if (!CollectionUtils.isEmpty(roles))
            {
                // 多角色设置permissions属性，以便数据权限匹配权限
                for (SysRole role : roles)
                {
                    Set<String> rolePerms = menuService.selectMenuPermsByRoleId(role.getRoleId());
                    role.setPermissions(rolePerms);
                    perms.addAll(rolePerms);
                }
            }
            else
            {
                perms.addAll(menuService.selectMenuPermsByUserId(user.getUserId()));
            }
        }
        return perms;
    }

    @Override
    public Set<String> getMenuPermissionByApplication(SysUser user, String applicationCode) {
        Set<String> perms = new HashSet<String>();
        // 管理员拥有所有权限
        if (null!=user.getUserId()&&1L==user.getUserId())
        {
            perms.add("*:*:*");
        }
        else
        {
            List<SysRole> roles = user.getRoles();
            if (!CollectionUtils.isEmpty(roles))
            {
                // 多角色设置permissions属性，以便数据权限匹配权限
                for (SysRole role : roles)
                {
                    Set<String> rolePerms = menuService.selectMenuPermsByRoleIdByApplicationCode(role.getRoleId(),applicationCode);
                    role.setPermissions(rolePerms);
                    perms.addAll(rolePerms);
                }
            }
            else
            {
                perms.addAll(menuService.selectMenuPermsByUserIdByApplicationCode(user.getUserId(),applicationCode));
            }
        }
        return perms;
    }

    @Override
    public Set<Long> getDeptPermission(SysUser user) {
        return deptService.selectDeptPermissionByUserId(user.getUserId());
    }

    @Override
    public Set<Long> getSonDeptPermission(Set<Long> depts) {
        return deptService.selectSonDeptPermission(depts);
    }

    @Override
    public String getDeptFullName(SysUser user) {
        if (null == user.getDeptId()) return "";
        return deptService.selectAllParentByDeptId(user.getDeptId()).stream().map(SysDept::getDeptName).filter(Objects::nonNull).collect(Collectors.joining("/"));
    }

    @Override
    public List<SysPost> getPosts(SysUser user) {
        return postService.selectPostListByUserIds(Collections.singletonList(user.getUserId()));
    }
}
