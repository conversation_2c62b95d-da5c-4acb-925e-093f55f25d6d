package com.wbyy.system.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.List;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.wbyy.common.core.domain.R;
import com.wbyy.common.log.annotation.Log;
import com.wbyy.common.log.enums.BusinessType;
import com.wbyy.common.security.annotation.RequiresPermissions;
import com.wbyy.system.domain.Application;
import com.wbyy.system.service.IApplicationService;
import com.wbyy.common.core.web.controller.BaseController;
import com.wbyy.common.core.utils.poi.ExcelUtil;
import com.wbyy.common.core.web.page.TableDataInfo;

/**
 * 应用管理Controller
 * 
 * <AUTHOR>
 * @date 2025-04-11
 */
@RestController
@Tag(name = "应用管理")
@RequestMapping("/application")
@RequiredArgsConstructor
public class ApplicationController extends BaseController
{
    private final IApplicationService applicationService;

    /**
     * 查询应用管理列表
     */
    @Operation(summary = "分页列表")
    @RequiresPermissions("system:application:list")
    @GetMapping("/page")
    public TableDataInfo page(Application application)
    {
        startPage();
        List<Application> list = applicationService.selectApplicationList(application);
        return getDataTable(list);
    }

    /**
     * 导出应用管理列表
     */
    @Operation(summary = "导出列表")
    @RequiresPermissions("system:application:export")
    @Log(title = "应用管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Application application)
    {
        List<Application> list = applicationService.selectApplicationList(application);
        ExcelUtil<Application> util = new ExcelUtil<Application>(Application.class);
        util.exportExcel(response, list, "应用管理数据");
    }

    /**
     * 获取应用管理详细信息
     */
    @Operation(summary = "获取详细信息")
    @RequiresPermissions("system:application:query")
    @GetMapping(value = "/{id}")
    public R<Application> getInfo(@PathVariable("id") Long id)
    {
        return R.ok(applicationService.selectApplicationById(id));
    }

    /**
     * 新增应用管理
     */
    @Operation(summary = "新增")
    @RequiresPermissions("system:application:add")
    @Log(title = "应用管理", businessType = BusinessType.INSERT)
    @PostMapping
    public R<Boolean> add(@RequestBody @Validated Application application)
    {
        return R.ok(applicationService.insertApplication(application));
    }

    /**
     * 修改应用管理
     */
    @Operation(summary = "修改")
    @RequiresPermissions("system:application:edit")
    @Log(title = "应用管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<Boolean> edit(@RequestBody @Validated Application application)
    {
        return R.ok(applicationService.updateApplication(application));
    }

    /**
     * 删除应用管理
     */
    @Operation(summary = "删除")
    @RequiresPermissions("system:application:remove")
    @Log(title = "应用管理", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public R<Boolean> remove(@PathVariable Long[] ids)
    {
        return R.ok(applicationService.deleteApplicationByIds(ids));
    }

    /**
     * 查询应用管理列表
     */
    @Operation(summary = "查询所有应用")
    @RequiresPermissions("system:application:list")
    @GetMapping("list")
    public R<List<Application>> list(){
        List<Application> list = applicationService.selectApplicationList(new Application());
        return R.ok(list);
    }
}
