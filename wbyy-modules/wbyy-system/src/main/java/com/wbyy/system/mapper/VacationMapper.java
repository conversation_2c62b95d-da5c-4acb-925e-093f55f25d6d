package com.wbyy.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wbyy.system.domain.Vacation;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 请假Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-13
 */
public interface VacationMapper extends BaseMapper<Vacation> {
    List<Vacation> listUserVacation(@Param("userId") Long userId,@Param("startDate") Date startDate,@Param("endDate") Date endDate);

    List<Vacation> listUsersVacation(@Param("userIds")List<Long> userIds,@Param("startDate") Date startDate,@Param("endDate") Date endDate);
}
