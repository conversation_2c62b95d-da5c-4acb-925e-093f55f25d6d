package com.wbyy.system.domain;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.wbyy.common.core.annotation.Excel;
import com.wbyy.orm.core.domain.BaseEntity;

/**
 * 系统日历对象 sys_calendar
 * 
 * <AUTHOR>
 * @date 2025-04-18
 */
@Data
@Schema(description = "系统日历实体类")
@EqualsAndHashCode(callSuper = true)
@TableName("sys_calendar")
public class Calendar extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** id */
    @Schema(description = "id")
    private Long id;

    /** 日历类型 */
    @Schema(description = "日历类型", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "日历类型不能为空")
    private String calendarType;

    /** 节假日名称 */
    @Excel(name = "节假日名称")
    @Schema(description = "节假日名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "节假日名称不能为空")
    private String holidayName;

    /** 开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "开始时间", width = 30, dateFormat = "yyyy-MM-dd")
    @Schema(description = "开始时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "开始时间不能为空")
    private Date startDate;

    /** 结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "结束时间", width = 30, dateFormat = "yyyy-MM-dd")
    @Schema(description = "结束时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "结束时间不能为空")
    private Date endDate;

    /** 设置类型（1：休息日；2：工作日） */
    @Excel(name = "设置类型", readConverterExp = "1=休息日, 2=工作日")
    @Schema(description = "设置类型（1：休息日；2：工作日）", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "设置类型不能为空")
    private Integer dateType;

    /** 删除标志（0代表存在 1代表删除） */
    @Schema(description = "删除标志（0代表存在 1代表删除）")
    private Integer delFlag;


    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("calendarType", getCalendarType())
            .append("holidayName", getHolidayName())
            .append("startDate", getStartDate())
            .append("endDate", getEndDate())
            .append("dateType", getDateType())
            .append("delFlag", getDelFlag())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
