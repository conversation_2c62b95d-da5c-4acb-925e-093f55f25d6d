package com.wbyy.file.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.StrUtil;
import com.aliyun.oss.OSS;
import com.aliyun.oss.model.OSSObject;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wbyy.common.core.constant.HttpMagConstants;
import com.wbyy.common.core.exception.ServiceException;
import com.wbyy.common.core.utils.StringUtils;
import com.wbyy.file.config.OSSConfig;
import com.wbyy.file.domain.CommonFile;
import com.wbyy.file.mapper.CommonFileMapper;
import com.wbyy.file.utils.FileUploadUtils;
import com.wbyy.system.api.domain.ApiDownloadFile;
import com.wbyy.system.api.domain.ApiSysFile;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.springframework.context.annotation.Primary;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.net.URLConnection;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * OSS 文件存储
 *
 * <AUTHOR>
 */
@Primary
@Service
@RequiredArgsConstructor
@Slf4j
public class OSSSysFileServiceImpl extends ServiceImpl<CommonFileMapper, CommonFile> implements ISysFileService {
    private final OSSConfig ossConfig;

    private final OSS ossClient;

    private final CommonFileMapper commonFileMapper;

    /**
     * 文件上传接口
     *
     * @param file 上传的文件
     * @return 访问地址
     * @throws Exception
     */
    @Deprecated
    @Override
    public String uploadFile(MultipartFile file) throws Exception {
        return "";
    }

    /**
     * 上传文件
     *
     * @param path            存储路径
     * @param file            文件流
     * @param applicationCode 来源
     * @return 文件信息
     */
    @Override
    public ApiSysFile uploadFile(String path, MultipartFile file, String applicationCode) {
        if (StringUtils.isBlank(path)) throw new ServiceException(HttpMagConstants.PARAMS_NOT_NULL);
        return this.doUploadFile(path, file, applicationCode);
    }

    private ApiSysFile doUploadFile(String path, MultipartFile file, String applicationCode) {
        // 校验文件大小
        this.checkFileSize(file);
        try {
            if (path.startsWith("/")) {
                path = path.substring(1);
            }
            if (path.endsWith("/")) {
                path = path.substring(0, path.length() - 1);
            }
            String md5Name = FileUploadUtils.getMD5(file.getInputStream());
            String extension = FilenameUtils.getExtension(file.getOriginalFilename());
            String objectName = md5Name + "." + extension;
            ossClient.putObject(ossConfig.getBucketName(), path + "/" + objectName, file.getInputStream());
            CommonFile commonFile = CommonFile.builder()
                    .fileName(file.getOriginalFilename())
                    .filePath(path + "/" + objectName)
                    .fileSize(file.getSize())
                    .fileSuffix(extension)
                    .appName(applicationCode)
                    .build();
            commonFileMapper.insert(commonFile);
            return ApiSysFile.builder()
                    .fileId(commonFile.getId())
                    .name(file.getOriginalFilename())
                    .fileSuffix(extension)
                    .fileSize(file.getSize())
                    .build();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 校验上传文件的大小
     * @param file 上传文件
     */
    private void checkFileSize(MultipartFile file){
        // M -> B
        long m = 1024 * 1024;
        if (null != ossConfig.getFileSizeLimit() && ossConfig.getFileSizeLimit() * m < file.getSize()) {
            throw new ServiceException(StrUtil.format(HttpMagConstants.UPLOAD_FILE_TOO_LARGE, ossConfig.getFileSizeLimit()));
        }
    }

    /**
     * 批量上传文件
     *
     * @param path            存储路径
     * @param files           文件流
     * @param applicationCode 来源
     * @return 文件信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<ApiSysFile> uploadBatchFile(String path, List<MultipartFile> files, String applicationCode) {
        if (StringUtils.isBlank(path) && CollectionUtil.isEmpty(files))
            throw new ServiceException(HttpMagConstants.PARAMS_NOT_NULL);
        List<ApiSysFile> sysFiles = new ArrayList<>();
        files.forEach(file -> {
            sysFiles.add(this.doUploadFile(path, file, applicationCode));
        });
        return sysFiles;
    }

    /**
     * 获取文件可访问路径
     *
     * @param fileId 文件id
     * @return 可访问路径
     */
    @Override
    public String getFilePath(Long fileId) {
        if (null == fileId) throw new ServiceException(HttpMagConstants.PARAMS_NOT_NULL);
        CommonFile byId = this.getById(fileId);
        if (byId == null) throw new ServiceException(HttpMagConstants.NO_DATA);
        // 短时间有效的签名URL
        Date expiration = new Date(System.currentTimeMillis() + ossConfig.getExpireTime() * 60 * 1000);
        URL signedUrl = ossClient.generatePresignedUrl(ossConfig.getBucketName(), byId.getFilePath(), expiration);
        return this.customFullPath(signedUrl.toString());
    }


    /**
     * 构建全路径，使用自定义url替换
     *
     * @param path oss路径
     * @return 自定义路径
     */
    private String customFullPath(String path) {
        return StringUtils.isNotBlank(path) ? path.replace(ossConfig.getBucketName() + "." + ossConfig.getEndpoint(), ossConfig.getCustomUrl()) : "";
    }

    /**
     * 下载文件
     *
     * @param fileId   文件id
     * @param response 返回
     */
    @Override
    public ApiDownloadFile downloadFile(Long fileId, HttpServletResponse response) {
        if (null == fileId) throw new ServiceException(HttpMagConstants.PARAMS_NOT_NULL);
        CommonFile byId = this.getById(fileId);
        if (byId == null) throw new ServiceException(HttpMagConstants.NO_DATA);
        OSSObject ossObject = ossClient.getObject(ossConfig.getBucketName(), byId.getFilePath());
        try (InputStream contentStream = ossObject.getObjectContent()) {
            ApiDownloadFile apiDownloadFile = new ApiDownloadFile();
            apiDownloadFile.setFileName(byId.getFileName());
            // 通过MIME类型字符串创建MediaType对象
            apiDownloadFile.setType(detectMediaType(Paths.get(byId.getFileName())).toString());
            apiDownloadFile.setStream(contentStream.readAllBytes());
            return apiDownloadFile;
        } catch (IOException e) {
            log.error("文件下载失败!", e);
            throw new RuntimeException("文件下载失败!");
        }
    }

    private MediaType detectMediaType(Path path) throws IOException {
        // 优先级1：NIO标准探测
        String mimeType = Files.probeContentType(path);

        // 优先级2：文件名后缀推测
        if (mimeType == null) {
            mimeType = URLConnection.guessContentTypeFromName(path.toString());
        }

        // 最终回退方案
        return MediaType.parseMediaType(
                Optional.ofNullable(mimeType)
                        .orElse("application/octet-stream")
        );
    }


    /**
     * 多文件下载zip
     *
     * @param fileIds 多文件id
     */
    @Override
    public ApiDownloadFile downloadZip(List<Long> fileIds, HttpServletResponse response) {
        if (CollectionUtil.isEmpty(fileIds)) throw new ServiceException(HttpMagConstants.PARAMS_NOT_NULL);
        long time = DateTime.now().getTime();
        try (ByteArrayOutputStream byteOut = new ByteArrayOutputStream();
             ZipOutputStream zipOut = new ZipOutputStream(byteOut)) {
            for (CommonFile commonFile : this.listByIds(fileIds)) {
                try (OSSObject ossObject = ossClient.getObject(
                        ossConfig.getBucketName(),
                        commonFile.getFilePath())) {
                    zipOut.putNextEntry(new ZipEntry(commonFile.getFileName()));
                    ossObject.getObjectContent().transferTo(zipOut);
                    zipOut.closeEntry();
                }
            }
            zipOut.finish();
            ApiDownloadFile apiDownloadFile = new ApiDownloadFile();
            apiDownloadFile.setFileName(time + ".zip");
            apiDownloadFile.setType("application/zip");
            apiDownloadFile.setStream(byteOut.toByteArray());
            return apiDownloadFile;
        } catch (IOException e) {
            log.error("文件下载失败!", e);
            throw new RuntimeException("文件下载失败!");
        }
    }

    /**
     * 根据文件id，获取文件信息
     *
     * @param fileId 文件id
     * @return 文件信息
     */
    @Override
    public ApiSysFile getFileById(Long fileId) {
        if (null == fileId) throw new ServiceException(HttpMagConstants.PARAMS_NOT_NULL);
        CommonFile byId = this.getById(fileId);
        if (byId == null) throw new ServiceException(HttpMagConstants.NO_DATA);
        return ApiSysFile.builder().fileId(fileId)
                .name(byId.getFileName())
                .fileSuffix(byId.getFileSuffix())
                .fileSize(byId.getFileSize())
                .build();
    }

    /**
     * 根据文件id，获取文件信息
     *
     * @param fileIds 文件id
     * @return 文件信息
     */
    @Override
    public List<ApiSysFile> getFilesByIdList(List<Long> fileIds) {
        if (CollectionUtil.isEmpty(fileIds)) throw new ServiceException(HttpMagConstants.PARAMS_NOT_NULL);
        List<ApiSysFile> sysFiles = new ArrayList<>();
        this.listByIds(fileIds).forEach(byId -> {
            sysFiles.add(ApiSysFile.builder().fileId(byId.getId())
                    .name(byId.getFileName())
                    .fileSuffix(byId.getFileSuffix())
                    .fileSize(byId.getFileSize())
                    .build());
        });
        return sysFiles;
    }
}
