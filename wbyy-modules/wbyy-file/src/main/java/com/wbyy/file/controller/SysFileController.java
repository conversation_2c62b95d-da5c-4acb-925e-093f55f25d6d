package com.wbyy.file.controller;

import com.wbyy.common.core.constant.SecurityConstants;
import com.wbyy.common.core.domain.R;
import com.wbyy.common.core.utils.file.FileUtils;
import com.wbyy.common.core.web.controller.BaseController;
import com.wbyy.common.security.annotation.InnerAuth;
import com.wbyy.file.service.ISysFileService;
import com.wbyy.system.api.domain.ApiDownloadFile;
import com.wbyy.system.api.domain.ApiSysFile;
import jakarta.servlet.http.HttpServletResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 文件请求处理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("file")
public class SysFileController extends BaseController {
    private static final Logger log = LoggerFactory.getLogger(SysFileController.class);

    @Autowired
    private ISysFileService sysFileService;

    /**
     * 文件上传请求
     */
    @PostMapping("upload")
    @InnerAuth
    public R<ApiSysFile> upload(MultipartFile file) {
        try {
            // 上传并返回访问地址
            String url = sysFileService.uploadFile(file);
            ApiSysFile apiSysFile = new ApiSysFile();
            apiSysFile.setName(FileUtils.getName(url));
            apiSysFile.setUrl(url);
            return R.ok(apiSysFile);
        } catch (Exception e) {
            log.error("上传文件失败", e);
            return R.fail(e.getMessage());
        }
    }

    /**
     * 文件上传请求
     */
    @PostMapping("uploadFile")
    @InnerAuth
    public R<ApiSysFile> uploadFile(String path, MultipartFile file) {
        try {
            // 按应用过滤菜单
            String applicationCode = getRequest().getHeader(SecurityConstants.APPLICATION_CODE);
            ApiSysFile apiSysFile = sysFileService.uploadFile(path, file, applicationCode);
            return R.ok(apiSysFile);
        } catch (Exception e) {
            log.error("上传文件失败", e);
            return R.fail(e.getMessage());
        }
    }

    /**
     * 文件上传请求
     */
    @PostMapping("uploadBatchFile")
    @InnerAuth
    public R<List<ApiSysFile>> uploadBatchFile(String path, List<MultipartFile> files) {
        try {
            // 按应用过滤菜单
            String applicationCode = getRequest().getHeader(SecurityConstants.APPLICATION_CODE);
            List<ApiSysFile> apiSysFiles = sysFileService.uploadBatchFile(path, files, applicationCode);
            return R.ok(apiSysFiles);
        } catch (Exception e) {
            log.error("上传文件失败", e);
            return R.fail(e.getMessage());
        }
    }

    /**
     * 获取文件访问路径
     */
    @GetMapping("getFilePath")
    @InnerAuth
    public R<String> getFilePath(@RequestParam Long fileId) {
        try {
            return R.ok(sysFileService.getFilePath(fileId));
        } catch (Exception e) {
            log.error("获取文件访问路径失败", e);
            return R.fail(e.getMessage());
        }
    }

    /**
     * 文件下载
     */
    @GetMapping("downloadFile")
    @InnerAuth
    public R<ApiDownloadFile> downloadFile(@RequestParam Long fileId, HttpServletResponse response) {
        try {
            return R.ok(sysFileService.downloadFile(fileId, response));
        } catch (Exception e) {
            log.error("文件下载失败", e);
            return R.fail(e.getMessage());
        }
    }

    /**
     * 多文件下载zip
     */
    @GetMapping("downloadZip")
    @InnerAuth
    public R<ApiDownloadFile> downloadZip(@RequestParam List<Long> fileIds, HttpServletResponse response) {
        try {
            return R.ok(sysFileService.downloadZip(fileIds, response));
        } catch (Exception e) {
            log.error("文件下载失败", e);
            return R.fail(e.getMessage());
        }
    }

    @GetMapping("getFileById")
    @InnerAuth
    public R<ApiSysFile> getFileById(@RequestParam Long fileId){
        return R.ok(sysFileService.getFileById(fileId));
    }

    @GetMapping("getFilesByIdList")
    @InnerAuth
    public R<List<ApiSysFile>> getFilesByIdList(@RequestParam List<Long> fileIds){
        return R.ok(sysFileService.getFilesByIdList(fileIds));
    }
}