package com.wbyy.file.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.wbyy.orm.core.domain.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.*;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;


/**
 * 公共文件表
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@Accessors(chain = true)
@TableName("common_file")
@SuperBuilder
@AllArgsConstructor
public class CommonFile extends BaseEntity {

    /**
     * 主键id
     */
    @Schema(description = "主键id（新增：不传；修改：必传）")
    @TableId
    private Long id;


    /**
     * 文件名
     */
    @Schema(description = "文件名", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "文件名不能为空")
    private String fileName;

    /**
     * 文件地址
     */
    @Schema(description = "文件地址", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "文件地址不能为空")
    private String filePath;

    /**
     * 文件大小
     */
    @Schema(description = "文件大小", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "文件大小不能为空")
    private Long fileSize;

    /**
     * 文件后缀
     */
    @Schema(description = "文件后缀", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "文件后缀不能为空")
    private String fileSuffix;

    /**
     * 来源
     */
    @Schema(description = "来源", requiredMode = Schema.RequiredMode.REQUIRED)
    private String appName;

    /**
     * 删除标志（0代表存在 1代表删除）
     */
    @Schema(description = "删除标志（0代表存在 1代表删除）")
    @TableLogic
    private Integer delFlag;
}
