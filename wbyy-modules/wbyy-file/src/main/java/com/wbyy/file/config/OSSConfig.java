package com.wbyy.file.config;

import com.aliyun.oss.ClientBuilderConfiguration;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.common.comm.Protocol;
import com.wbyy.common.core.utils.SpringUtils;
import jakarta.annotation.PreDestroy;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * oss 配置信息
 *
 * <AUTHOR>
 */
@Configuration
@ConfigurationProperties(prefix = "oss")
@Data
public class OSSConfig {
    /**
     * 服务地址
     */
    private String endpoint;

    /**
     * 映射地址
     */
    private String customUrl;

    /**
     * 用户名
     */
    private String accessKey;

    /**
     * 密码
     */
    private String secretKey;

    /**
     * 存储桶名称
     */
    private String bucketName;

    /**
     * 实际时间，单位分钟
     */
    private Integer expireTime;

    private Integer fileSizeLimit;

    @Bean
    public OSS getOSS() {
        // 创建 OSSClient 实例
        ClientBuilderConfiguration clientBuilderConfiguration = new ClientBuilderConfiguration();
        clientBuilderConfiguration.setProtocol(Protocol.HTTPS); // 全局生效
        return new OSSClientBuilder().build(this.endpoint, this.accessKey, this.secretKey,clientBuilderConfiguration);
    }

    @PreDestroy
    public void destroy() {
        SpringUtils.getBean(OSS.class).shutdown();
    }
}
