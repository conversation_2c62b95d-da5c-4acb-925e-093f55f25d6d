<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wbyy.pms.modules.configuration.basicparameter.roledept.mapper.ProjectRoleDeptMapper">


    <select id="list" resultType="com.wbyy.pms.modules.configuration.basicparameter.roledept.domain.ProjectRoleDept">
        select
            prd.id,prd.project_role_id projectRoleId,
            pr.name projectRoleName,pr.name_en projectRoleNameEn,
            prd.dept_id deptId,pr.disable projectRoleDisable,
            prd.create_name_by createNameBy,
            prd.create_time createTime,
        prd.sort
        from project_role_dept prd
            left join project_role pr on prd.project_role_id=pr.id
        where prd.del_flag=0 and pr.del_flag=0
        <if test="deptId != null and deptId != ''">
           and prd.dept_id=#{deptId}
        </if>
        <if test="projectRoleDisable != null">
            and pr.disable = #{projectRoleDisable}
        </if>
        order by prd.sort asc
    </select>

    <select id="getProjectPmRole" resultType="com.wbyy.pms.modules.configuration.basicparameter.roledept.domain.ProjectRoleDept">
        select prd.id,
               prd.project_role_id as projectRoleId,
               prd.dept_id as deptId
        from project_role_dept prd
                 left join project_role pr on prd.project_role_id = pr.id
        where prd.del_flag=0 and pr.del_flag=0 and pr.disable=0 and pr.name_en='PM'
        and prd.dept_id=#{deptId} limit 1
    </select>

    <select id="listByRoleIds" resultType="com.wbyy.pms.modules.configuration.basicparameter.roledept.domain.ProjectRoleDept">
        select id, project_role_id, dept_id, del_flag, create_by, create_time, update_by, update_time, create_name_by,
        update_name_by from project_role_dept where del_flag = 0 and project_role_id in
        <foreach collection="roleIds" open="(" close=")" item="roleId" separator=",">
                #{roleId}
            </foreach>
    </select>
</mapper>