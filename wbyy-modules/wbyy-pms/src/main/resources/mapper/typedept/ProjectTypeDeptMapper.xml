<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wbyy.pms.modules.configuration.basicparameter.typedept.mapper.ProjectTypeDeptMapper">

    <select id="list" resultType="com.wbyy.pms.modules.configuration.basicparameter.typedept.domain.ProjectTypeDept">
        select ptd.id, ptd.dept_id deptId, ptd.project_type_id projectTypeId, ptd.create_time createTime, ptd.create_name_by createNameBy,
        pt.name projectTypeName,pt.disable projectTypeDisable
        from project_type_dept ptd left join project_type pt on
        ptd.project_type_id=pt.id
        where ptd.del_flag=0 and pt.del_flag=0
        <if test="deptId != null">
            and ptd.dept_id=#{deptId}
        </if>
        <if test="projectTypeDisable != null">
            and pt.disable=#{projectTypeDisable}
        </if>
    </select>

    <select id="listByTypeIds" resultType="com.wbyy.pms.modules.configuration.basicparameter.typedept.domain.ProjectTypeDept">
        select * from project_type_dept where del_flag=0 and project_type_id in
            <foreach collection="typeIds" open="(" close=")" separator="," item="typeId">
                #{typeId}
            </foreach>
    </select>
</mapper>