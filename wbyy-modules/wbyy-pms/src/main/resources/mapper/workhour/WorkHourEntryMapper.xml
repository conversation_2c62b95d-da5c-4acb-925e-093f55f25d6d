<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wbyy.pms.modules.workhour.entry.mapper.WorkHourEntryMapper">

    <select id="idsByUserIdByYearAndWeek" resultType="java.lang.Long">
        select id from work_hour_entry where create_by=#{userId} and year_number=#{yearNumber} and week_number=#{weekNumber} and del_flag=0
    </select>
</mapper>