<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wbyy.pms.modules.workhour.detail.mapper.WorkHourDetailMapper">

    <select id="selectByPlanIdsAndUserIdsAndStatus" resultType="com.wbyy.pms.modules.workhour.detail.domain.WorkHourDetail">
        SELECT
            whd.work_hour,
            whd.create_by,
            whd.plan_id,
            whd.fill_date
        FROM
            work_hour_detail whd
        where whd.del_flag = 0
            <if test="statusList != null and statusList.size() > 0">
                and whd.`status` in
                <foreach collection="statusList" item="status" open="(" separator="," close=")">
                    #{status}
                </foreach>
            </if>

            <if test="userIds != null and userIds.size() > 0">
                AND whd.create_by IN
                <foreach collection="userIds" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            <if test="planIds != null and planIds.size() > 0">
                AND whd.plan_id IN
                <foreach collection="planIds" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
    </select>

    <select id="selectListByEntryIdsByUserId" resultType="com.wbyy.pms.modules.workhour.detail.domain.WorkHourDetail">
        select id, entry_id, work_hour, status, year_number, week_number, fill_date, description, real_name,
        work_number, reject_reason, del_flag, create_by, create_name_by, create_time, update_by, update_name_by,
        update_time from work_hour_detail
        where entry_id in
        <foreach collection="entryIds" item="entryId" open="(" close=")" separator=",">
                #{entryId}
            </foreach>
        and create_by = #{userId}
        and del_flag=0
        order by fill_date
    </select>

    <select id="countByYearNumberByWeekNumberByUserId" resultType="java.lang.Long">
        select count(*) from work_hour_detail d LEFT JOIN work_hour_entry e on d.entry_id=e.id
        where d.create_by = #{userId}
          and d.year_number = #{yearNumber}
          and d.week_number = #{weekNumber}
          and d.del_flag=0
          and e.del_flag=0
    </select>

    <update id="tempStore">
        update work_hour_detail set work_hour=#{dto.workHour},
                                    status=0,
                                    reject_reason=null,
                                    description=#{dto.description},
            over_time=#{dto.overTime}
        where create_by=#{dto.createBy} and year_number=#{dto.yearNumber} and week_number=#{dto.weekNumber}
        and entry_id=#{dto.entryId} and fill_date=#{dto.fillDate} and del_flag=0
    </update>

    <select id="selectListByFillDateRange" resultType="com.wbyy.pms.modules.workhour.detail.domain.WorkHourDetail">
        select d.id,
               d.work_hour,
               d.status,
               d.year_number,
               d.week_number,
               d.fill_date,
               d.description,
               d.real_name,
               d.work_number,
               d.create_by,
               d.dept_id,
               e.project_id,
               e.project_name,
               e.project_number,
               e.plan_id,
               e.entry_name
        from work_hour_detail d
        left join work_hour_entry e on d.entry_id = e.id
        where d.fill_date &gt;= #{startDate}
          and d.fill_date &lt;= #{endDate}
          and d.status in (1,4)
          and e.id is not null
          and d.del_flag = 0
          and e.del_flag = 0
    </select>

    <select id="selectListByFillDateAndUserId" resultType="com.wbyy.pms.modules.workhour.detail.domain.WorkHourDetail">
        SELECT
            whd.id,
            whd.work_hour,
            whd.description,
            whd.reject_reason,
            whd.status,
            whd.plan_id,
            whd.over_time,
            whd.fill_date,
            whe.project_id,
            whe.project_name,
            whe.project_number,
            whe.entry_name
        FROM
            `work_hour_detail` whd
                join work_hour_entry whe on whe.id = whd.entry_id and whe.del_flag = 0
        WHERE
            whd.del_flag = 0
            and whd.fill_date = DATE(#{theDay})
            and whd.create_by = #{userId}
    </select>

    <select id="selectRejectAndWaitApproval"
            resultType="com.wbyy.pms.modules.workhour.detail.domain.WorkHourDetail">
        select d.id,
               d.entry_id,
               d.plan_id,
               d.work_hour,
               d.status,
               d.year_number,
               d.week_number,
               d.fill_date,
               d.create_by
        from work_hour_detail d
                 LEFT JOIN work_hour_entry e on d.entry_id = e.id
        where d.create_by = #{userId}
          and d.status in (2,3)
          and d.year_number = #{yearNumber}
          and d.week_number = #{weekNumber}
          and d.del_flag = 0
          and e.del_flag = 0
    </select>

    <select id="selectFilled" resultType="com.wbyy.pms.modules.workhour.detail.domain.WorkHourDetail">
        select d.id,
               d.entry_id,
               d.plan_id,
               d.work_hour,
               d.over_time,
               d.status,
               d.year_number,
               d.week_number,
               d.fill_date,
               d.description,
               d.real_name,
               d.work_number,
               d.dept_id,
               d.reject_reason,
               d.create_by,
               e.project_id projectId,
            e.project_name projectName,
            e.project_number projectNumber,
            e.entry_name entryName
        from work_hour_detail d
                 LEFT JOIN work_hour_entry e on d.entry_id = e.id
        where d.create_by = #{userId}
          and d.fill_date = #{fillDate}
          and d.year_number = #{yearNumber}
          and d.week_number = #{weekNumber}
          and d.del_flag = 0
          and e.del_flag = 0
    </select>

    <select id="selectFillPlanHour" resultType="com.wbyy.pms.modules.workhour.detail.domain.vo.FillPlanHourVo">
        select
               d.plan_id as planId,
               sum(d.work_hour) as workHour
        from work_hour_detail d
                 LEFT JOIN work_hour_entry e on d.entry_id = e.id
        where d.create_by = #{userId}
            and d.plan_id in
            <foreach collection="planIds" separator="," open="(" close=")" item="planId">
                #{planId}
            </foreach>
          and d.status in (0,1,2,4)
          and d.del_flag = 0
          and e.del_flag = 0
        group by d.plan_id
    </select>

    <select id="selectLatestDayWorkHours" resultType="com.wbyy.pms.modules.workhour.detail.domain.WorkHourDetail">
        SELECT
        whd.id,
        whd.work_hour,
        whd.description,
        whd.STATUS,
        whd.plan_id,
        whd.fill_date,
        whe.project_name,
        whe.project_number,
        whe.entry_name,
        whe.project_id
        FROM
        `work_hour_detail` whd
        JOIN work_hour_entry whe ON whe.id = whd.entry_id
        AND whe.del_flag = 0
        WHERE
        whd.del_flag = 0
        AND whd.fill_date = ( SELECT fill_date FROM work_hour_detail WHERE del_flag = 0 AND fill_date &lt; DATE(#{theDay})
        AND create_by = #{userId} ORDER BY fill_date DESC LIMIT 1 )
        AND whd.create_by = #{userId}
    </select>

    <select id="selectByUserIdByProjectIdByTimeRange"
            resultType="com.wbyy.pms.modules.workhour.detail.domain.WorkHourDetail">
        select whd.id, whd.entry_id, whd.plan_id, whd.work_hour, whd.over_time, whd.status, whd.year_number,
        whd.week_number, whd.fill_date, whd.description, whd.real_name, whd.work_number, whd.dept_id, whd.reject_reason,
        whd.del_flag, whd.create_by, whd.create_name_by, whd.create_time, whd.update_by, whd.update_name_by,
        whd.update_time,whe.project_id as projectId, whe.project_name as projectName, whe.project_number as projectNumber,
        whe.entry_name as entryName,whe.plan_id as planId
        from work_hour_detail whd
        left join work_hour_entry whe on whd.entry_id=whe.id
        where whd.create_by = #{userId}
        and whd.fill_date &gt;= #{startTime}
        and whd.fill_date &lt;= #{endTime}
        and whd.status in (1,4)
        <if test="null!=projectId">
            and whe.project_id = #{projectId}
        </if>
        and whe.del_flag = 0
        and whd.del_flag = 0
    </select>

    <select id="findByUserIdsAndTime" resultType="com.wbyy.pms.modules.workhour.detail.domain.WorkHourDetail">
        SELECT whd.fill_date,
               whd.create_by,
               whd.work_number,
               sum(whd.work_hour) as work_hour
        FROM
        work_hour_detail whd
        JOIN work_hour_entry whe ON whd.entry_id = whe.id
        AND whe.del_flag = 0
        WHERE whd.del_flag = 0
        and whd.status in (1 , 4)
        <!--            过滤公共工时-->
        <if test="projectId != null and projectType == 1">
            and whe.project_id = #{projectId}
        </if>
        and whd.fill_date &gt;= #{startDate}
        and whd.fill_date &lt;= #{endDate}
        and whd.create_by in
        <foreach collection="userIds" item="userId" open="(" close=")" separator=",">
            #{userId}
        </foreach>
        group by whd.fill_date, whd.create_by
    </select>
</mapper>