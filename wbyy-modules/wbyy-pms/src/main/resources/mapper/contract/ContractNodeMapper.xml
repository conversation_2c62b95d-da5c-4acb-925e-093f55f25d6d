<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wbyy.pms.modules.contract.contractnode.mapper.ContractNodeMapper">

    <update id="clearPlanDateTimeByIds">
        update contract_node set plan_data_time=null where id in 
            <foreach collection="ids" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
    </update>
</mapper>