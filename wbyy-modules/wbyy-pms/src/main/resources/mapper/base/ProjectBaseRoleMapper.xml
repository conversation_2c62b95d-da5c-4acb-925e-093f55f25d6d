<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wbyy.pms.modules.project.baserole.mapper.ProjectBaseRoleMapper">

    <select id="listPreSave" resultType="com.wbyy.pms.modules.project.baserole.domain.ProjectBaseRole">
        select
            pr.id as roleId,
            prd.id as roleDeptId,
            pr.name,
            pr.name_en as nameEn,
            pr.description,
            prd.sort
        from project_role_dept prd
            left join project_role pr on prd.project_role_id = pr.id
        where
            prd.dept_id=#{deptId}
          and prd.del_flag=0
          and pr.del_flag=0
          and pr.disable=0
    </select>

    <select id="ListUserProjectPermCode" resultType="java.lang.String">
        SELECT DISTINCT prp.perm_code
        FROM project_role_perm prp
                 LEFT JOIN project_base_role pbr on prp.project_role_id=pbr.role_id
                 LEFT JOIN project_team_person ptp on pbr.role_dept_id=ptp.project_role_id
        WHERE prp.del_flag=0 and pbr.del_flag=0 and ptp.del_flag=0
        and pbr.project_id=#{projectId} and ptp.project_id=#{projectId} and ptp.user_id=#{userId}
    </select>
</mapper>