<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wbyy.pms.modules.project.planuser.mapper.ProjectPlanSnapshotUserMapper">

    <resultMap id="findListResultMap" type="com.wbyy.pms.modules.project.planuser.domain.vo.ProjectPlanSnapshotUserVO">
        <result property="projectId"  column="project_id"  />
        <result property="projectPlanId"  column="project_plan_id"  />
        <result property="id"  column="user_id"  />
        <result property="userId"  column="user_id"  />
        <result property="userName"  column="user_name"  />
        <result property="planConstructionPeriod"  column="plan_construction_period"  />
        <result property="workHour"  column="work_hour"  />
        <collection property="projectRoleList" javaType="java.util.List" resultMap="projectRoleListMap">
        </collection>
    </resultMap>
    <resultMap id="projectRoleListMap" type="com.wbyy.pms.modules.project.planuser.domain.vo.ProjectRoleListVO">
        <result property="projectRoleId" column="project_role_id" />
        <result property="projectRoleName" column="project_role_name" />
    </resultMap>

    <select id="findList" resultMap="findListResultMap">
        select
            t1.project_id,
            t1.user_id,
            t1.real_name as user_name,
            t2.work_hour,
            t4.plan_construction_period,
            t2.project_plan_id,
            t3.role_id as project_role_id,
            t3.NAME AS project_role_name,
            t3.role_id
        FROM project_team_person t1
        LEFT JOIN project_plan_snapshot_user t2 ON t1.user_id = t2.user_id
            AND t2.project_plan_id = #{projectPlanId}
            and t2.project_id = #{projectId}
            and t2.del_flag = 0
        LEFT JOIN project_base_role t3 ON t3.project_id = t1.project_id
            and t3.role_dept_id = t1.project_role_id
        left join project_plan_snapshot t4 on t4.project_id = t1.project_id
            AND t4.id = #{projectPlanId}
            and t4.project_id = #{projectId}
            and t4.del_flag = 0
        WHERE
            t1.project_id = #{projectId}
          AND t1.del_flag = 0
        order by t3.sort asc , t2.work_hour desc

    </select>
</mapper>