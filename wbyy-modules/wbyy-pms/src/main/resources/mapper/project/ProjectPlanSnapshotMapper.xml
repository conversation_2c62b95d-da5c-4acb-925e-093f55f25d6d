<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wbyy.pms.modules.project.plan.mapper.ProjectPlanSnapshotMapper">

    <select id="selectAllParentNodeById"
            resultType="com.wbyy.pms.modules.project.plan.domain.ProjectPlanSnapshot">
        WITH RECURSIVE project_plan_snapshot_cte AS (
            SELECT id,
                   parent_id,
                   project_id,
                   NAME,
                   sort,
                   wbs,
                   plan_construction_period,
                   plan_start_date,
                   plan_end_date
            FROM project_plan_snapshot
            WHERE id = #{id} AND del_flag = 0

            UNION ALL

            SELECT r.id,
                   r.parent_id,
                   r.project_id,
                   r.NAME,
                   r.sort,
                   r.wbs,
                   r.plan_construction_period,
                   r.plan_start_date,
                   r.plan_end_date
            FROM project_plan_snapshot r
                     JOIN project_plan_snapshot_cte c ON r.id = c.parent_id
            where r.project_id = #{projectId} AND r.del_flag = 0
        )
        SELECT * FROM project_plan_snapshot_cte
        ORDER BY
            sort desc
    </select>

    <select id="selectAllChildrenNodeById"
            resultType="com.wbyy.pms.modules.project.plan.domain.ProjectPlanSnapshot">
        WITH RECURSIVE project_plan_snapshot_cte AS (
            SELECT id,
                   parent_id,
                   project_id,
                   NAME,
                   sort,
                   wbs,
                   plan_construction_period,
                   plan_start_date,
                   plan_end_date
            FROM project_plan_snapshot
            WHERE parent_id = #{id} AND del_flag = 0

            UNION ALL

            SELECT r.id,
                   r.parent_id,
                   r.project_id,
                   r.NAME,
                   r.sort,
                   r.wbs,
                   r.plan_construction_period,
                   r.plan_start_date,
                   r.plan_end_date
            FROM project_plan_snapshot r
                     JOIN project_plan_snapshot_cte c ON c.id = r.parent_id
            where r.project_id = #{projectId} AND r.del_flag = 0
        )
        SELECT * FROM project_plan_snapshot_cte
        ORDER BY
            sort desc

    </select>

    <select id="findPlanAndProjectInfoByPlanIds" resultType="com.wbyy.pms.modules.project.plan.domain.vo.PlanAndProjectInfoVO">
        SELECT
            pps.id as planId,
            pps.`name` as planName,
            pps.project_id,
            pb.number,
            pb.`name` as projectName
        FROM
            project_plan_snapshot pps
                JOIN project_base pb ON pps.project_id = pb.id and pb.del_flag = 0
        where pps.del_flag = 0
        and pps.id in
        <foreach collection="planIds" open="(" close=")" item="id" separator=",">
            #{id}
        </foreach>
    </select>
</mapper>