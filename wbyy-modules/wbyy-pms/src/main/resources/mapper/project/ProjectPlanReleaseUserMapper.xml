<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wbyy.pms.modules.project.planuser.mapper.ProjectPlanReleaseUserMapper">

    <select id="findFinishedProjectWorkHour" resultType="com.wbyy.pms.modules.project.planrelate.domain.vo.FinishedProjectWorkHourVO">
        SELECT ppru.project_id,
               sum(ppru.work_hour) as workHour
        FROM project_plan_release_user ppru
        WHERE
        EXISTS (
        SELECT 1
        FROM project_plan_release ppr
        WHERE
        ppr.project_id IN
        <foreach collection="projectIds" open="(" close=")" item="projectId" separator=",">
            #{projectId}
        </foreach>
        AND ppr.reality_end_date IS NOT NULL
        AND ppr.reality_end_date BETWEEN #{startTime}
            AND #{endTime}
        AND ppru.project_plan_id = ppr.id
        )
        GROUP BY ppru.project_id;
    </select>

    <select id="listUserFinishPlanByProjectIds"
            resultType="com.wbyy.pms.modules.project.planuser.domain.ProjectPlanReleaseUser">
        SELECT ppru.project_id, ppru.project_plan_id, ppru.user_id,ppru.work_hour
        FROM project_plan_release_user ppru
        WHERE
        ppru.user_id in
        <foreach collection="userIds" separator="," open="(" close=")" item="userId">
            #{userId}
        </foreach>
        and EXISTS (
        SELECT 1
        FROM project_plan_release ppr
        WHERE
        ppr.project_id IN
        <foreach collection="projectIds" open="(" close=")" item="projectId" separator=",">
            #{projectId}
        </foreach>
        AND ppr.reality_end_date IS NOT NULL
        AND ppr.reality_end_date BETWEEN #{startTime}
        AND #{endTime}
        AND ppru.project_plan_id = ppr.id
        )
    </select>

    <select id="selectByProjectPlanIdsByUserIds"
            resultType="com.wbyy.pms.modules.project.planuser.domain.ProjectPlanReleaseUser">
        select
            max(id) as id,
            max(project_id) as projectId,
            project_plan_id, user_id,
            max(work_number) workNumber,
            max(real_name) realName,
            sum(work_hour) workHour
        from project_plan_release_user
        where
            project_plan_id in
            <foreach collection="projectPlanIds" open="(" close=")" item="projectPlanId" separator=",">
                #{projectPlanId}
            </foreach>
            and user_id in
            <foreach collection="userIds" open="(" close=")" separator="," item="userId">
                #{userId}
            </foreach>
        group by project_plan_id,user_id
    </select>

    <select id="selectByPlanIds" resultType="com.wbyy.pms.modules.project.planuser.domain.ProjectPlanReleaseUser">
        select id, project_id, project_plan_id, user_id, work_number, real_name, work_hour, create_by, create_name_by,
        create_time, update_by, update_name_by, update_time
        from project_plan_release_user
        where project_plan_id in
        <foreach collection="planIds" open="(" close=")" separator="," item="planId">
            #{planId}
        </foreach>
    </select>
</mapper>