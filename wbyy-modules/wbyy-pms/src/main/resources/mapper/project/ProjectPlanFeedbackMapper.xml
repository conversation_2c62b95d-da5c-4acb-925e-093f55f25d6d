<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wbyy.pms.modules.project.planfeedback.mapper.ProjectPlanFeedbackMapper">

    <select id="listCurrentByProjectIds"
            resultType="com.wbyy.pms.modules.project.planfeedback.domain.ProjectPlanFeedback">
        WITH RankedFeedbacks AS (SELECT id, project_id, project_plan_id, start_time, end_time, reality_progress,
        delay_reason, progress_note, del_flag,
        create_by, create_name_by, create_time, update_by, update_name_by, update_time,
        ROW_NUMBER() OVER (PARTITION BY project_plan_id ORDER BY create_time DESC) AS rn
        FROM
        project_plan_feedback
        WHERE project_id IN
        <foreach collection="projectIds" separator="," open="(" close=")" item="projectId">
            #{projectId}
        </foreach>
        and del_flag=0
        )
        SELECT id, project_id, project_plan_id, start_time, end_time, reality_progress, delay_reason, progress_note,
        del_flag,
        create_by, create_name_by, create_time, update_by, update_name_by, update_time
        FROM
        RankedFeedbacks
        WHERE
        rn = 1
    </select>
</mapper>