package com.wbyy.pms.modules.configuration.dict.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wbyy.common.core.constant.CacheConstants;
import com.wbyy.common.core.exception.ServiceException;
import com.wbyy.common.core.utils.TreeUtil;
import com.wbyy.common.redis.service.CommonCacheService;
import com.wbyy.log.annotation.OperationLog;
import com.wbyy.log.constant.DefaultOperationType;
import com.wbyy.pms.modules.configuration.dict.domain.DictData;
import com.wbyy.pms.modules.configuration.dict.domain.DictType;
import com.wbyy.pms.modules.configuration.dict.mapper.DictTypeMapper;
import com.wbyy.pms.modules.configuration.dict.service.IDictDataService;
import com.wbyy.pms.modules.configuration.dict.service.IDictTypeService;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 字典类型Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-04-17
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DictTypeServiceImpl extends ServiceImpl<DictTypeMapper, DictType> implements IDictTypeService {
    private final DictTypeMapper dictTypeMapper;
    private final IDictDataService dictDataService;
    private final CommonCacheService<DictData> commonCacheService;

    /**
     * 项目启动时，初始化字典到缓存
     */
    @PostConstruct
    public void init() {
        loadingDictCache();
    }

    /**
     * 查询字典类型
     *
     * @param dictId 字典类型主键
     * @return 字典类型
     */
    @Override
    public DictType selectSysDictTypeByDictId(Long dictId) {
        return this.getById(dictId);
    }

    /**
     * 查询字典类型列表
     *
     * @param dictType 字典类型
     * @return 字典类型
     */
    @Override
    public List<DictType> selectSysDictTypeList(DictType dictType) {
        LambdaQueryWrapper<DictType> wrapper = Wrappers.lambdaQuery();
        wrapper.orderByDesc(DictType::getCreateTime);
        return this.list(wrapper);
    }

    /**
     * 新增字典类型
     *
     * @param dictType 字典类型
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @OperationLog(name = DefaultOperationType.ADD,
            originalDataMethod = "selectSysDictTypeByDictId",
            objectIdField = "dictId",
            objectNameField = "dictName",
            objectClazz = DictType.class,
            type = "字典类型")
    public boolean insertSysDictType(DictType dictType) {
        boolean save = this.save(dictType);
        if (save) {
            commonCacheService.setDictCache(CacheConstants.PMS_DICT_KEY, dictType.getDictType(), null);
        }
        return save;
    }

    /**
     * 修改字典类型
     *
     * @param dictType 字典类型
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @OperationLog(name = DefaultOperationType.UPDATE,
            originalDataMethod = "selectSysDictTypeByDictId",
            objectIdField = "dictId",
            objectClazz = DictType.class,
            type = "字典类型")
    public boolean updateSysDictType(DictType dictType) {
        boolean isOk = this.updateById(dictType);
        if (isOk) {
            List<DictData> dictDatas =
                    dictDataService.selectDictDataByType(dictType.getDictType());
            commonCacheService.setDictCache(CacheConstants.PMS_DICT_KEY, dictType.getDictType(), dictDatas);

        }
        return isOk;
    }

    /**
     * 批量删除字典类型
     *
     * @param dictIds 需要删除的字典类型主键
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @OperationLog(name = DefaultOperationType.DELETE,
            type = "字典类型",objectClazz = DictType.class)
    public boolean deleteSysDictTypeByDictIds(Long[] dictIds) {
        for (Long dictId : dictIds) {
            DictType dictType = this.getById(dictId);
            if (dictDataService.countDictDataByType(dictType.getDictType()) > 0) {
                throw new ServiceException(String.format("%1$s已分配,不能删除", dictType.getDictName()));
            }
            this.removeById(dictId);
            commonCacheService.removeDictCache(CacheConstants.PMS_DICT_KEY, dictType.getDictType());
        }

        return true;
    }

    @Override
    public void resetDictCache() {
        clearDictCache();
        loadingDictCache();
    }

    @Override
    public void loadingDictCache() {
        LambdaQueryWrapper<DictData> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(DictData::getStatus, "0");
        Map<String, List<DictData>> dictDataMap = dictDataService.list(wrapper)
                .stream().collect(Collectors.groupingBy(DictData::getDictType));

        for (Map.Entry<String, List<DictData>> entry : dictDataMap.entrySet()) {
            commonCacheService.setDictCache(CacheConstants.PMS_DICT_KEY, entry.getKey(),
                    entry.getValue().stream()
                            .sorted(Comparator.comparing(DictData::getDictSort))
                            .collect(Collectors.toList()));
        }
    }

    @Override
    public void clearDictCache() {
        commonCacheService.clearDictCache(CacheConstants.PMS_DICT_KEY);
    }

    @Override
    public List<DictType> selectDictTypeAll() {
        LambdaQueryWrapper<DictType> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(DictType::getStatus, "0")
                .orderByDesc(DictType::getCreateTime);
        return this.list(wrapper);
    }


    @Override
    public boolean checkDictTypeUnique(DictType dictType) {

        LambdaQueryWrapper<DictType> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(DictType::getDictType, dictType.getDictType());
        if (dictType.getDictId() != null) {
            wrapper.ne(DictType::getDictId, dictType.getDictId());
        }
        return this.count(wrapper) == 0;
    }

    @Override
    public Map<String, List<DictData>> selectDictDataByTypes(String dictTypes) {
        Optional.ofNullable(dictTypes).orElseThrow(() -> new ServiceException("字典类型不能为空"));
        String[] split = dictTypes.split(",|，");

        Map<String, List<DictData>> result = new HashMap<>();

        for (String dictType : split) {
            List<DictData> dictCache = commonCacheService.getDictCache(CacheConstants.PMS_DICT_KEY, dictType, DictData.class);
            if (CollUtil.isEmpty(dictCache)) {
                LambdaQueryWrapper<DictData> wrapper = Wrappers.lambdaQuery();
                wrapper.eq(DictData::getStatus, "0")
                        .eq(DictData::getDictType, dictType)
                        .orderByAsc(DictData::getParentDictCode, DictData::getDictSort);
                List<DictData> list = dictDataService.list(wrapper);
                if (CollUtil.isNotEmpty(list)) {
                    JSONArray jsonArray = JSONArray.parseArray(JSON.toJSONString(list));
                    JSONArray objects = TreeUtil.buildTree(jsonArray, "dictCode", "parentDictCode", "children");
                    list = JSONArray.parseArray(objects.toJSONString(), DictData.class);
                    commonCacheService.setDictCache(CacheConstants.PMS_DICT_KEY, dictType, list);
                    result.put(dictType, list);
                }
            } else {
                result.put(dictType, dictCache);
            }
        }

        return result;
    }
}
