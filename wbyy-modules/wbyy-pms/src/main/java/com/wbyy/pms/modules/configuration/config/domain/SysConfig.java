package com.wbyy.pms.modules.configuration.config.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.wbyy.common.core.annotation.Excel;
import com.wbyy.orm.core.domain.BaseAuditEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serial;

/**
 * 系统参数对象 sys_config
 *
 * <AUTHOR>
 * @date 2025-07-02
 */
@Data
@SuperBuilder
@NoArgsConstructor
@Accessors(chain = true)
@TableName(value = "sys_config", autoResultMap = true)
@Schema(description = "系统参数实体类")
@EqualsAndHashCode(callSuper = true)
public class SysConfig extends BaseAuditEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @Schema(description = "主键id（新增：不传；修改：必传）")
    private Long id;

    /**
     * 参数名称
     */
    @Excel(name = "参数名称")
    @Schema(description = "参数名称")
    @Size(max = 100, message = "参数名称不能超过 100 个字符")
    @NotNull(message = "参数名称不能为空")
    private String configName;

    /**
     * 参数键名
     */
    @Excel(name = "参数键名")
    @Schema(description = "参数键名")
    @Size(max = 100, message = "参数键名不能超过 100 个字符")
    @NotNull(message = "参数键名不能为空")
    private String configKey;

    /**
     * 参数键值
     */
    @Excel(name = "参数键值")
    @Schema(description = "参数键值")
    @Size(max = 100, message = "参数键值不能超过 100 个字符")
    @NotNull(message = "参数键值不能为空")
    private String configValue;

    /**
     * 系统内置(Y是,N否)
     */
    @Excel(name = "系统内置(Y是,N否)")
    @Schema(description = "系统内置(Y是,N否)")
    @Size(max = 1, message = "系统内置不能超过 1 个字符")
    @NotNull(message = "系统内置不能为空")
    private String configType;

    /**
     * 状态(启用,禁用)
     * 正常	0
     * 停用	1
     */
    @Excel(name = "状态(启用,禁用)")
    @Schema(description = "状态(启用,禁用) - 字典 ： sys_normal_disable")
    @Size(max = 1, message = "状态(启用,禁用)不能超过 1 个字符")
    @NotNull(message = "状态不能为空")
    private String status;

    /**
     * 备注
     */
    @Excel(name = "备注")
    @Schema(description = "备注")
    @Size(max = 255, message = "备注不能超过 255 个字符")
    private String remark;

    @Schema(description = "搜索值")
    @TableField(exist = false)
    private String searchValue;

    /**
     * 删除标志（0代表存在 1代表删除）
     */
    @Schema(description = "删除标志（0代表存在 1代表删除）", requiredMode = Schema.RequiredMode.REQUIRED)
    @TableLogic
    private Integer delFlag;


    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("configName", getConfigName())
                .append("configKey", getConfigKey())
                .append("configValue", getConfigValue())
                .append("configType", getConfigType())
                .append("status", getStatus())
                .append("remark", getRemark())
                .append("delFlag", getDelFlag())
                .append("createBy", getCreateBy())
                .append("createNameBy", getCreateNameBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateNameBy", getUpdateNameBy())
                .append("updateTime", getUpdateTime())
                .toString();
    }
}
