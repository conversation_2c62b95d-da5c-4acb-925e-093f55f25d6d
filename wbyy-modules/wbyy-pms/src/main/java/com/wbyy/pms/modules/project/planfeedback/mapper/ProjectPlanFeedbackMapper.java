package com.wbyy.pms.modules.project.planfeedback.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wbyy.pms.modules.project.planfeedback.domain.ProjectPlanFeedback;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 项目任务反馈Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-05-13
 */
public interface ProjectPlanFeedbackMapper extends BaseMapper<ProjectPlanFeedback> {
    List<ProjectPlanFeedback> listCurrentByProjectIds(@Param("projectIds") List<Long> projectIds);
}
