package com.wbyy.pms.modules.contract.base.controller;

import com.wbyy.pms.modules.contract.base.domain.dto.ContractBaseDTO;
import com.wbyy.pms.modules.contract.base.domain.vo.BaseTotalVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.List;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.wbyy.common.core.domain.R;
import com.wbyy.common.log.annotation.Log;
import com.wbyy.common.log.enums.BusinessType;
import com.wbyy.common.security.annotation.RequiresPermissions;
import com.wbyy.pms.modules.contract.base.domain.ContractBase;
import com.wbyy.pms.modules.contract.base.service.IContractBaseService;
import com.wbyy.common.core.web.controller.BaseController;
import com.wbyy.common.core.utils.poi.ExcelUtil;
import com.wbyy.common.core.web.page.TableDataInfo;

/**
 * 合同基本信息Controller
 * 
 * <AUTHOR>
 * @date 2025-06-25
 */
@RestController
@RequiredArgsConstructor
@Tag(name = "合同基本信息")
@RequestMapping("/contract/base")
public class ContractBaseController extends BaseController
{

    private final IContractBaseService contractBaseService;

    @Operation(summary = "分页列表")
    @RequiresPermissions("contract:base:list")
    @GetMapping("/page")
    public TableDataInfo<ContractBase> page(@Validated @ParameterObject ContractBaseDTO dto)
    {
        List<ContractBase> list = contractBaseService.selectList(dto);
        return getDataTable(list);
    }

    @Operation(summary = "分页列表的总计")
    @RequiresPermissions("contract:base:list")
    @GetMapping("/base-total")
    public R<BaseTotalVO> baseTotal(@Validated @ParameterObject ContractBaseDTO dto)
    {
        return R.ok(contractBaseService.baseTotal(dto));
    }

//    @Operation(summary = "导出列表")
//    @RequiresPermissions("contract:base:export")
//    @Log(title = "合同基本信息", businessType = BusinessType.EXPORT)
//    @PostMapping("/export")
//    public void export(HttpServletResponse response, ContractBase contractBase)
//    {
//        List<ContractBase> list = contractBaseService.selectList(contractBase);
//        ExcelUtil<ContractBase> util = new ExcelUtil<ContractBase>(ContractBase.class);
//        util.exportExcel(response, list, "合同基本信息数据");
//    }

    @Operation(summary = "获取详细信息")
    @RequiresPermissions("contract:base:query")
    @GetMapping(value = "/{id}")
    public R<ContractBase> getInfo(@PathVariable("id") Long id)
    {
        return R.ok(contractBaseService.selectById(id));
    }
}
