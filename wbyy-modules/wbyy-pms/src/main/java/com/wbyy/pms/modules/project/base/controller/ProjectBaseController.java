package com.wbyy.pms.modules.project.base.controller;

import com.wbyy.common.core.domain.R;
import com.wbyy.common.core.web.controller.BaseController;
import com.wbyy.common.core.web.page.TableDataInfo;
import com.wbyy.common.log.annotation.Log;
import com.wbyy.common.log.enums.BusinessType;
import com.wbyy.common.security.annotation.RequiresPermissions;
import com.wbyy.common.security.utils.SecurityUtils;
import com.wbyy.pms.common.annotaion.ProjectAuthCheck;
import com.wbyy.pms.common.annotaion.ProjectDataCheck;
import com.wbyy.pms.modules.project.base.domain.ProjectBase;
import com.wbyy.pms.modules.project.base.domain.dto.ProjectBaseSaveDto;
import com.wbyy.pms.modules.project.base.domain.dto.ProjectByConditionDTO;
import com.wbyy.pms.modules.project.base.domain.vo.DutyProjectVO;
import com.wbyy.pms.modules.project.base.service.IProjectBaseService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 项目基础信息Controller
 *
 * <AUTHOR>
 * @date 2025-05-06
 */
@RestController
@Tag(name = "项目基础信息")
@RequestMapping("/project/base")
public class ProjectBaseController extends BaseController {

    @Autowired
    private IProjectBaseService projectBaseService;

    @Operation(summary = "查询列表")
    @RequiresPermissions("pms:project-base:list")
    @GetMapping("/page")
    public TableDataInfo<ProjectBase> page(@RequestParam(required = false, name = "searchValue") String searchValue) {
        List<ProjectBase> list = projectBaseService.selectList(searchValue);
        return getDataTable(list);
    }

//    @Operation(summary = "导出列表")
//    @RequiresPermissions("pms:project-base:export")
//    @Log(title = "项目基础信息", businessType = BusinessType.EXPORT)
//    @PostMapping("/export")
//    public void export(HttpServletResponse response, ProjectBase projectBase)
//    {
//        List<ProjectBase> list = projectBaseService.selectList(projectBase);
//        ExcelUtil<ProjectBase> util = new ExcelUtil<ProjectBase>(ProjectBase.class);
//        util.exportExcel(response, list, "项目基础信息数据");
//    }

    @Operation(summary = "获取详细信息")
    @RequiresPermissions("pms:project-base:query")
    @GetMapping(value = "/{id}")
    @ProjectDataCheck(projectId = "#id")
    public R<ProjectBase> getInfo(@PathVariable("id") Long id) {
        return R.ok(projectBaseService.selectById(id));
    }

    @Operation(summary = "获取详细信息")
    @RequiresPermissions("pms:project-base:query")
    @GetMapping(value = "/info")
    public R<ProjectBase> getInfoByNumber(@RequestParam("number") String number) {
        return R.ok(projectBaseService.getInfoByNumber(number));
    }

    @Operation(summary = "新增")
    @RequiresPermissions("pms:project-base:add")
    @Log(title = "项目基础信息", businessType = BusinessType.INSERT)
    @PostMapping
    public R<Boolean> add(@RequestBody @Validated ProjectBaseSaveDto dto) {
        projectBaseService.insert(dto);
        return R.ok(true);
    }

    @Operation(summary = "修改")
    @ProjectAuthCheck(projectId = "#projectBase.id", permCode = "pms:project-base:edit")
    @Log(title = "项目基础信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<Boolean> edit(@RequestBody ProjectBase projectBase) {
        projectBaseService.update(projectBase);
        return R.ok(true);
    }

    @Operation(summary = "删除")
    @RequiresPermissions("pms:project-base:remove")
    @Log(title = "项目基础信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Boolean> remove(@PathVariable Long[] ids, @RequestParam("reason") String reason) {
        projectBaseService.deleteByIds(ids, reason);
        return R.ok(true);
    }

    @Operation(summary = "获取同一'集团合同编号'的项目集合，去除本身")
    @RequiresPermissions("pms:project-base:query")
    @GetMapping(value = "/listSameCompanyNumberProjects")
    public R<List<ProjectBase>> listSameCompanyNumberProjects(@RequestParam("id") Long id) {
        return R.ok(projectBaseService.listSameGroupNumberProjects(id));
    }

    @Operation(summary = "查询参与项目,用作过滤条件")
    @RequiresPermissions("pms:project-base:list")
    @GetMapping("/duty-project")
    public TableDataInfo<DutyProjectVO> pageDutyProject(@RequestParam(required = false, name = "searchValue") String searchValue, @RequestParam(required = false) Boolean isEffective) {
        startPage();
        List<DutyProjectVO> list = projectBaseService.selectDutyProject(searchValue, isEffective, SecurityUtils.getUserId());
        return getDataTable(list);
    }

    @Operation(summary = "查询参与项目,用作过滤条件-不分页")
    @RequiresPermissions("pms:project-base:list")
    @GetMapping("/user-project")
    public R<List<DutyProjectVO>> userProject(@RequestParam(required = false, name = "searchValue") String searchValue, @RequestParam(required = false) Boolean isEffective) {
        List<DutyProjectVO> list = projectBaseService.selectUserProject(searchValue, isEffective, SecurityUtils.getUserId());
        return R.ok(list);
    }

    @Operation(summary = "查询参与项目卡片列表")
    @RequiresPermissions("pms:project-base:list")
    @GetMapping("/duty-project-detail")
    public R<List<ProjectBase>> pageDutyProjectDetail() {
        return R.ok(projectBaseService.selectDutyProjectDetail(true, SecurityUtils.getUserId()));
    }

    @Operation(summary = "根据公司合同编码获取项目信息")
    @RequiresPermissions("pms:project-base:query")
    @GetMapping(value = "/get-by-company-contract-number")
    public R<ProjectBase> getByCompanyContractNumber(@RequestParam("companyContractNumber") String companyContractNumber){
        return R.ok(projectBaseService.getByCompanyContractNumber(companyContractNumber));
    }


    @Operation(summary = "项目下拉列表，受组织、任务、时间影响")
    @RequiresPermissions("pms:project-base:list")
    @GetMapping("/project-by-condition")
    public TableDataInfo<DutyProjectVO> projectByCondition(@Validated @ParameterObject ProjectByConditionDTO dto) {
        startPage();
        return getDataTable(projectBaseService.projectByCondition(dto));
    }
}
