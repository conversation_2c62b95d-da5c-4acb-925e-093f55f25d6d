package com.wbyy.pms.common.utils;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import com.wbyy.common.core.constant.Constants;
import com.wbyy.pms.common.constant.ProjectConst;
import com.wbyy.pms.common.utils.vo.ProjectCycleVo;
import com.wbyy.pms.common.utils.vo.ProjectPlanFinishVo;
import com.wbyy.pms.common.utils.vo.ProjectProgressVo;
import com.wbyy.pms.modules.project.plan.domain.ProjectPlanRelease;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

/**
 * @author: 王金都
 * @date: 2025/5/14 17:17
 */
public class ProjectUtil {


    /**
     * @param projectStatus 项目状态
     * @param projectPlans  项目计划
     * @param compareDate   比较日期
     * @Description: 获取红绿灯
     * @return: Integer
     * @author: 王金都
     * @Date: 2025/5/14 17:40
     */
    public static Integer getLight(Integer projectStatus, List<ProjectPlanRelease> projectPlans, Date compareDate) {
        // 项目状态是【2:已终止、3:已暂停、4:已完结】时亮灰色灯
        if (ProjectConst.GREY_STATUS.contains(projectStatus)) {
            return ProjectConst.GREY_LIGHT;
        }
        // 没有计划默认绿灯
        if (CollectionUtil.isEmpty(projectPlans)) return ProjectConst.GREEN_LIGHT;
        // 红灯与绿灯
        // 绿灯：项目内未完成的任务都不延期
        // 红灯：项目内有延期的未完成任务
        // 有延期
        return projectPlans.stream()
                // 未完成的任务
                .filter(item -> !ProjectConst.PROGRESS_100.equals(item.getRealityProgress()))
                // 过滤掉计划结束时间为空的
                .filter(item -> null != item.getPlanEndDate())
                // 有延期
                .anyMatch(item -> DateUtil.compare(compareDate, item.getPlanEndDate()) > 0)
                ? ProjectConst.RED_LIGHT : ProjectConst.GREEN_LIGHT;
    }

    /**
     * @param projectStatus 项目状态
     * @param projectPlans  项目计划
     * @Description: 获取红绿灯
     * @return: Integer
     * @author: 王金都
     * @Date: 2025/5/14 17:29
     */
    public static Integer getLight(Integer projectStatus, List<ProjectPlanRelease> projectPlans) {
        return getLight(projectStatus, projectPlans, DateUtil.beginOfDay(new Date()));
    }

    /**
     * @param projectPlans 项目计划
     * @param compareDate  比较日期
     * @param checkLeaf    是否只计算叶子
     * @param project      是否是项目的所有计划
     * @Description: 获取计划进度，实际进度
     * @return: ProjectProgressVo
     * @author: 王金都
     * @Date: 2025/5/15 16:12
     */
    public static ProjectProgressVo getProgress(List<ProjectPlanRelease> projectPlans, Date compareDate, Boolean checkLeaf, Boolean project) {
        // 没有计划默认都是0
        if (CollectionUtil.isEmpty(projectPlans))
            return new ProjectProgressVo(ProjectConst.PROGRESS_0, ProjectConst.PROGRESS_0);
        //  所有（叶子任务的计划工期）累加的和 也是 总工期
        BigDecimal leafTotalConstructionPeriod = BigDecimal.ZERO;
        // 所有（叶子任务的计划工期 × 叶子任务的实际进度）累加的和
        BigDecimal leafTotalResidueWork = BigDecimal.ZERO;
        // 所有（叶子任务的计划工期 × 叶子任务的计划进度）累加的和
        BigDecimal leafTotalPlanValue = BigDecimal.ZERO;
        // 总工期,最外层最小开始时间与最大结束时间天数差
        BigDecimal totalConstructionPeriod;
        if (project) {
            // 总工期 10
            totalConstructionPeriod = getTotalConstructionPeriod(projectPlans);
        } else {
            // 最小开始时间
            Date minPlanStartDate = getPlanStartDate(projectPlans);
            // 最大结束时间
            Date maxPlanEndDate = getPlanEndDate(projectPlans);
            // 总工期
            totalConstructionPeriod = getConstructionPeriod(minPlanStartDate, maxPlanEndDate);
        }
        for (ProjectPlanRelease plan : projectPlans) {
            if (checkLeaf) {
                if (ProjectConst.NO_LEAF.equals(plan.getLeafFlag())) continue;
            }
            // 计划工期
            BigDecimal planConstructionPeriod = BigDecimal.valueOf(Optional.ofNullable(plan.getPlanConstructionPeriod()).orElse(0));
            leafTotalConstructionPeriod = leafTotalConstructionPeriod.add(planConstructionPeriod);
            // 实际进度
            BigDecimal realityProgress = BigDecimal.valueOf(Optional.ofNullable(plan.getRealityProgress()).orElse(ProjectConst.PROGRESS_0));
            // 计划进度
            BigDecimal planProgress = getPlanProgress(compareDate, totalConstructionPeriod, plan.getPlanStartDate(), plan.getPlanEndDate());
            // 叶子任务的计划工期 × 叶子任务的实际进度
            BigDecimal residueWork = planConstructionPeriod.multiply(realityProgress.divide(ProjectConst.VALUE_100, 2, RoundingMode.HALF_UP));
            leafTotalResidueWork = leafTotalResidueWork.add(residueWork);
            // 叶子任务的计划工期 × 叶子任务的计划进度
            BigDecimal planValue = planConstructionPeriod.multiply(planProgress.divide(ProjectConst.VALUE_100, 2, RoundingMode.HALF_UP));
            leafTotalPlanValue = leafTotalPlanValue.add(planValue);
        }

        int realityProgress = 0;
        int planProgress = 0;
        if (BigDecimal.ZERO.compareTo(leafTotalConstructionPeriod) != 0) {
            // 实际进度，所有（叶子任务的计划工期 × 叶子任务的实际进度）累加的和 / 所有（叶子任务的计划工期）累加的和
            realityProgress = leafTotalResidueWork.divide(leafTotalConstructionPeriod, 2, RoundingMode.HALF_UP)
                    .multiply(ProjectConst.VALUE_100).intValue();
            // 计划进度，所有（叶子任务的计划工期 × 叶子任务的计划进度）累加的和 / 所有（叶子任务的计划工期）累加的和
            planProgress = leafTotalPlanValue.divide(leafTotalConstructionPeriod, 2, RoundingMode.HALF_UP)
                    .multiply(ProjectConst.VALUE_100).intValue();
        }

        return new ProjectProgressVo(realityProgress, planProgress);
    }

    /**
     * @param projectPlans 项目计划
     * @param checkLeaf    是否只计算叶子
     * @param project      是否是项目的所有计划
     * @Description: 获取计划进度和实际进度
     * @return: ProjectProgressVo
     * @author: 王金都
     * @Date: 2025/5/15 16:10
     */
    public static ProjectProgressVo getProgress(List<ProjectPlanRelease> projectPlans, Boolean checkLeaf, Boolean project) {
        Date now = DateUtil.beginOfDay(new Date());
        return getProgress(projectPlans, now, checkLeaf, project);
    }

    /**
     * @param projectPlans 任务列表
     * @Description: 获取最外层任务
     * @return: List<ProjectPlanRelease>
     * @author: 王金都
     * @Date: 2025/5/16 11:06
     */
    public static List<ProjectPlanRelease> getParentPlans(List<ProjectPlanRelease> projectPlans) {
        return projectPlans.stream().filter(item -> Constants.DEFAULT_PARENT_ID.equals(item.getParentId()))
                .toList();
    }

    /**
     * @param projectPlans 项目计划
     * @Description: 获取总工期
     * @return: BigDecimal
     * @author: 王金都
     * @Date: 2025/5/16 10:48
     */
    public static BigDecimal getTotalConstructionPeriod(List<ProjectPlanRelease> projectPlans) {
        List<ProjectPlanRelease> parentPlans = getParentPlans(projectPlans);
        // 最小开始时间
        Date minPlanStartDate = getPlanStartDate(parentPlans);
        // 最大结束时间
        Date maxPlanEndDate = getPlanEndDate(parentPlans);
        // 总工期
        return getConstructionPeriod(minPlanStartDate, maxPlanEndDate);
    }

    /**
     * @param startDate
     * @param endDate
     * @Description: 获取工期
     * @return: BigDecimal
     * @author: 王金都
     * @Date: 2025/5/16 10:37
     */
    public static BigDecimal getConstructionPeriod(Date startDate, Date endDate) {
        BigDecimal constructionPeriod = BigDecimal.ZERO;
        if (null != startDate && null != endDate) {
            constructionPeriod = BigDecimal.valueOf(DateUtil.betweenDay(startDate, endDate, true) + 1L);
        }
        return constructionPeriod;
    }

    /**
     * @param totalConstructionPeriod 总工期
     * @param planStartDate           计划开始时间
     * @param planEndDate             计划结束时间
     * @Description: 获取任务计划进度
     * @return: BigDecimal
     * @author: 王金都
     * @Date: 2025/5/16 10:32
     */
    public static Integer getPlanProgress(BigDecimal totalConstructionPeriod, Date planStartDate, Date planEndDate) {
        Date now = new Date();
        return getPlanProgress(now, totalConstructionPeriod, planStartDate, planEndDate).intValue();
    }

    /**
     * @param compareDate             比较日期
     * @param totalConstructionPeriod 总工期
     * @param planStartDate           计划开始时间
     * @param planEndDate             计划结束时间
     * @Description: 获取任务计划进度
     * @return: BigDecimal
     * @author: 王金都
     * @Date: 2025/5/16 10:31
     */
    public static BigDecimal getPlanProgress(Date compareDate, BigDecimal totalConstructionPeriod, Date planStartDate, Date planEndDate) {
        // 默认今天在计划开始时间前：叶子任务的计划进度 = 0
        BigDecimal planProgress = BigDecimal.ZERO;
        // 今天是计划结束时间或者计划结束时间后：叶子任务的计划进度 = 100%
        if (DateUtil.compare(compareDate, planEndDate) >= 0) {
            planProgress = ProjectConst.VALUE_100;
        }
        // 今天在计划开始时间和计划结束中间时：叶子任务的计划进度 =（今天 - 任务的开始时间）/（叶子任务工期）（即编制的计划工期，自然日）
        else if (DateUtil.compare(compareDate, planStartDate) >= 0 && DateUtil.compare(compareDate, planEndDate) < 0) {
            if (BigDecimal.ZERO.compareTo(totalConstructionPeriod) != 0) {
                planProgress = BigDecimal.valueOf(DateUtil.betweenDay(compareDate, planStartDate, true) + 1L)
                        .divide((BigDecimal.valueOf(DateUtil.betweenDay(planStartDate, planEndDate, true) + 1L)), 2, RoundingMode.HALF_UP)
                        .multiply(ProjectConst.VALUE_100);
            }
        }
        return planProgress;
    }

    /**
     * @param projectPlans
     * @Description: 获取最小计划开始时间
     * @return: Date
     * @author: 王金都
     * @Date: 2025/5/16 10:25
     */
    public static Date getPlanStartDate(List<ProjectPlanRelease> projectPlans) {
        List<Date> ascStartDates = new ArrayList<>(projectPlans.stream().map(ProjectPlanRelease::getPlanStartDate).filter(Objects::nonNull).toList());
        Date projectPlanStartDate = null;
        if (CollectionUtil.isNotEmpty(ascStartDates)) {
            ascStartDates.sort(null);
            projectPlanStartDate = ascStartDates.get(0);
        }
        return projectPlanStartDate;
    }

    /**
     * @param projectPlans
     * @Description: 获取最大计划结束时间
     * @return: Date
     * @author: 王金都
     * @Date: 2025/5/16 10:27
     */
    public static Date getPlanEndDate(List<ProjectPlanRelease> projectPlans) {
        ArrayList<Date> descEndDates = new ArrayList<>(projectPlans.stream().map(ProjectPlanRelease::getPlanEndDate).filter(Objects::nonNull).toList());
        Date projectPlanEndDate = null;
        if (CollectionUtil.isNotEmpty(descEndDates)) {
            descEndDates.sort(Comparator.reverseOrder());
            projectPlanEndDate = descEndDates.get(0);
        }
        return projectPlanEndDate;
    }

    /**
     * @param projectPlans 项目计划
     * @param minStartDate 项目的开始时间
     * @param maxEndDate   项目的结束时间
     * @Description: 获取项目周期
     * @return: ProjectCycleVo
     * @author: 王金都
     * @Date: 2025/5/16 15:26
     */
    public static ProjectCycleVo getCycle(List<ProjectPlanRelease> projectPlans, Date minStartDate, Date maxEndDate) {
        ProjectCycleVo vo = new ProjectCycleVo();
        // 计划周期
        int planCycle = Math.toIntExact(null == minStartDate || null == maxEndDate ? 0 : DateUtil.betweenDay(minStartDate, maxEndDate, true)+1);
        vo.setPlanCycle(planCycle);
        // 实际周期： 计划中任务最早实际开始时间到（所有任务都完成）任务最晚实际结束时间，有未完成任务就取当天
        Integer realCycle = getRealCycle(projectPlans);
        vo.setRealCycle(realCycle);
        // 百分比
        vo.setPercent(planCycle == 0 ?
                BigDecimal.ZERO :
                BigDecimal.valueOf(realCycle).divide(BigDecimal.valueOf(planCycle), 2, RoundingMode.HALF_UP)
                        .multiply(ProjectConst.VALUE_100));
        // 起止
        vo.setStartEnd((null == minStartDate ? "无" : DateUtil.format(minStartDate, "yy/MM/dd")) + "~" + (null == maxEndDate ? "无" : DateUtil.format(maxEndDate, "yy/MM/dd")));
        // 剩余或超出
        int diff = planCycle - realCycle;
        vo.setResidue(diff >= 0 ? "剩余" + diff + "天" : "超出" + Math.abs(diff) + "天");
        return vo;
    }

    /**
     * @param projectPlans 项目计划
     * @Description: 获取实际周期
     * @return: Integer
     * @author: 王金都
     * @Date: 2025/5/16 15:08
     */
    private static Integer getRealCycle(List<ProjectPlanRelease> projectPlans) {
        if (CollectionUtil.isEmpty(projectPlans)) return 0;
        // 是否有未完成的任务
        List<ProjectPlanRelease> leafPlans = projectPlans.stream().filter(item -> ProjectConst.LEAF.equals(item.getLeafFlag()))
                .toList();
        // 任务都没有反馈实际开始时间
        List<Date> feedbackRealitySatrtDates = new java.util.ArrayList<>(leafPlans.stream().map(ProjectPlanRelease::getRealityStartDate)
                .filter(Objects::nonNull).toList());
        if (CollectionUtil.isEmpty(feedbackRealitySatrtDates)) {
            // 没有反馈过，实际进度是0
            return 0;
        }
        feedbackRealitySatrtDates.sort(null);
        Date minRealityStartDate = feedbackRealitySatrtDates.get(0);
        // 已完成的任务的实际结束时间
        List<Date> finishWorkRealityEndDates = new java.util.ArrayList<>(leafPlans.stream().filter(item -> ProjectConst.PROGRESS_100.equals(item.getRealityProgress()))
                .map(ProjectPlanRelease::getRealityEndDate)
                .toList());
        // 最晚实际结束时间，默认去当前时间，任务有未完成任务
        Date lastDate ;
        // 所有任务均已完成
        if (CollectionUtil.isNotEmpty(finishWorkRealityEndDates) && finishWorkRealityEndDates.size() == leafPlans.size()) {
            finishWorkRealityEndDates.sort(Comparator.reverseOrder());
            lastDate = finishWorkRealityEndDates.get(0);
        }else {
            lastDate = new Date();
        }
        // 实际开始与结束之间的工作日即实际周期
        return Math.toIntExact(DateUtil.betweenDay(minRealityStartDate,lastDate,true)+1);
    }

    /**
     * @param projectPlans
     * @Description: 任务完成情况
     * @return: ProjectPlanFinishVo
     * @author: 王金都
     * @Date: 2025/5/16 17:23
     */
    public static ProjectPlanFinishVo projectFinish(List<ProjectPlanRelease> projectPlans) {
        // 总任务数据
        int totalCountValue = projectPlans.size();
        if (totalCountValue == 0) {
            return ProjectPlanFinishVo.getZero();
        }
        BigDecimal totalCount = new BigDecimal(totalCountValue);
        Date now = new Date();
        ProjectPlanFinishVo vo = new ProjectPlanFinishVo();
        // 完成的数据
        List<ProjectPlanRelease> finishPlans = projectPlans.stream()
                .filter(item -> ProjectConst.PROGRESS_100.equals(item.getRealityProgress()))
                .toList();
        // 未完成的数据
        List<ProjectPlanRelease> unfinishPlans = projectPlans.stream()
                .filter(item -> !ProjectConst.PROGRESS_100.equals(item.getRealityProgress()))
                .toList();
        // 正常完成数
        int normalFinishCount = Math.toIntExact(finishPlans.stream()
                .filter(item -> DateUtil.compare(item.getRealityEndDate(), item.getPlanEndDate()) <= 0)
                .count());
        vo.setNormalFinishCount(normalFinishCount);
        // 正常完成百分比
        vo.setNormalFinishPercent(new BigDecimal(normalFinishCount)
                .divide(totalCount, 2, RoundingMode.HALF_UP)
                .multiply(ProjectConst.VALUE_100).intValue());
        // 延误完成数
        int delayFinishCount = Math.toIntExact(finishPlans.stream()
                .filter(item -> DateUtil.compare(item.getRealityEndDate(), item.getPlanEndDate()) > 0)
                .count());
        vo.setDelayFinishCount(delayFinishCount);
        // 延误完成百分比
        vo.setDelayFinishPercent(new BigDecimal(delayFinishCount)
                .divide(totalCount, 2, RoundingMode.HALF_UP)
                .multiply(ProjectConst.VALUE_100).intValue());
        // 正常进行数
        int normalConductCount = Math.toIntExact(unfinishPlans.stream()
                .filter(item -> DateUtil.compare(now, item.getPlanEndDate()) <= 0)
                .count());
        vo.setNormalConductCount(normalConductCount);
        // 正常进行百分比
        vo.setNormalConductPercent(new BigDecimal(normalConductCount)
                .divide(totalCount, 2, RoundingMode.HALF_UP)
                .multiply(ProjectConst.VALUE_100).intValue());
        // 即将到期数
        int DueSoonCount = Math.toIntExact(unfinishPlans.stream()
                .filter(item -> DateUtil.compare(now, item.getPlanEndDate()) <= 0
                        // 3L 天定义常量
                        && DateUtil.betweenDay(now, item.getPlanEndDate(), true) <= ProjectConst.DUE_LIMIT)
                .count());
        vo.setDueSoonCount(DueSoonCount);
        // 即将到期百分比
        vo.setDueSoonPercent(new BigDecimal(DueSoonCount)
                .divide(totalCount, 2, RoundingMode.HALF_UP)
                .multiply(ProjectConst.VALUE_100).intValue());
        // 已延误数
        int delayCount = Math.toIntExact(unfinishPlans.stream()
                .filter(item -> DateUtil.compare(now, item.getPlanEndDate()) > 0)
                .count());
        vo.setDelayCount(delayCount);
        // 已延误百分比
        vo.setDelayPercent(new BigDecimal(delayCount)
                .divide(totalCount, 2, RoundingMode.HALF_UP)
                .multiply(ProjectConst.VALUE_100).intValue());
        // 未开始任务数
        int notStartCount = Math.toIntExact(projectPlans.stream()
                .filter(item -> DateUtil.compare(now, item.getPlanStartDate()) < 0)
                .count());
        vo.setNotStartCount(notStartCount);
        // 未开始任务百分比
        vo.setNotStartPercent(new BigDecimal(notStartCount)
                .divide(totalCount, 2, RoundingMode.HALF_UP)
                .multiply(ProjectConst.VALUE_100).intValue());
        return vo;
    }

}
