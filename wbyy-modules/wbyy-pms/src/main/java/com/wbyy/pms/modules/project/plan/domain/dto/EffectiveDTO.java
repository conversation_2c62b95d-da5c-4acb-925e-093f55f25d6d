package com.wbyy.pms.modules.project.plan.domain.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

@Data
@Schema(description = "编制DTO")
public class EffectiveDTO implements Serializable {
    @Serial
    private static final long serialVersionUID = 6418892928943868176L;

    @Schema(description = "项目ID")
    @NotNull(message = "项目ID不能为空")
    private Long projectId;

    @Schema(description = "计划变更原因")
    @Size(max = 100, message = "计划变更原因不能超过 100 个字符")
    private String description;
}
