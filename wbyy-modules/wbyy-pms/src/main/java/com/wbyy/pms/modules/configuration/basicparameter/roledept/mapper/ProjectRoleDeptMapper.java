package com.wbyy.pms.modules.configuration.basicparameter.roledept.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wbyy.pms.modules.configuration.basicparameter.roledept.domain.ProjectRoleDept;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 项目角色与部门关联Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-04-22
 */
public interface ProjectRoleDeptMapper extends BaseMapper<ProjectRoleDept> {
    List<ProjectRoleDept> list(@Param("deptId")Long deptId,@Param("projectRoleDisable")Integer projectRoleDisable);

    ProjectRoleDept getProjectPmRole(@Param("deptId") Long deptId);

    List<ProjectRoleDept> listByRoleIds(@Param("roleIds")List<Long> roleIds);
}
