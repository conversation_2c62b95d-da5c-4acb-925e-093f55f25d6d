package com.wbyy.pms.modules.workhour.approval.domain.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/6  10:17
 * @description 分页查询审核数据
 */
@Data
public class PageApprovalDTO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "姓名、工号模糊搜索")
    private String searchValue;

    @Schema(description = "项目id")
    private Long projectId;

    @Schema(description = "项目ids")
    private String projectIds;
    @Schema(description = "项目id列表", hidden = true)
    private List<Long> projectIdList;

    @Schema(description = "true已审核，false待审核")
    @NotNull(message = "审核状态不能为空")
    private Boolean hasApproval;

    @Schema(description = "true我提交的")
    private Boolean isMineFill;

    @Schema(description = "true我审核的")
    private Boolean isMineApproval;
}
