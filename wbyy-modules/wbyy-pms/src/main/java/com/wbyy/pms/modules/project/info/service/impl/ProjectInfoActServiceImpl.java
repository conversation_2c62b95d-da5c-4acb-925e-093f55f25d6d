package com.wbyy.pms.modules.project.info.service.impl;

import java.util.List;
import java.util.Arrays;

import cn.hutool.core.util.StrUtil;
import com.wbyy.common.core.utils.bean.BeanUtils;
import com.wbyy.pms.modules.project.base.domain.ProjectBase;
import com.wbyy.pms.modules.project.info.domain.ProjectInfo;
import com.wbyy.pms.modules.project.info.mapper.ProjectInfoActMapper;
import com.wbyy.pms.modules.project.info.service.IProjectInfoService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import com.wbyy.pms.modules.project.info.domain.ProjectInfoAct;
import com.wbyy.pms.modules.project.info.service.IProjectInfoActService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.transaction.annotation.Transactional;

import static com.wbyy.pms.common.constant.ProjectConst.BENAO_CN;
import static com.wbyy.pms.common.constant.ProjectConst.WBYY_CN;

/**
 * 项目信息-分析测试中心Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-05-06
 */
@Slf4j
@Service("projectInfoAtcService")
@RequiredArgsConstructor
public class ProjectInfoActServiceImpl extends ServiceImpl<ProjectInfoActMapper, ProjectInfoAct> implements IProjectInfoActService, IProjectInfoService
{
    private final ProjectInfoActMapper projectInfoActMapper;

    /**
     * 查询项目信息-分析测试中心
     * 
     * @param id 项目信息-分析测试中心主键
     * @return 项目信息-分析测试中心
     */
    @Override
    public ProjectInfoAct selectById(Long id)
    {
        return this.getById(id);
    }

    /**
     * 查询项目信息-分析测试中心列表
     * 
     * @param projectInfoAct 项目信息-分析测试中心
     * @return 项目信息-分析测试中心
     */
    @Override
    public List<ProjectInfoAct> selectList(ProjectInfoAct projectInfoAct)
    {
        LambdaQueryWrapper<ProjectInfoAct> wrapper = Wrappers.lambdaQuery();
        wrapper.orderByDesc(ProjectInfoAct::getCreateTime);
        return this.list(wrapper);
    }

    /**
     * 新增项目信息-分析测试中心
     * 
     * @param projectInfoAct 项目信息-分析测试中心
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean insert(ProjectInfoAct projectInfoAct)
    {
        return this.save(projectInfoAct);
    }

    /**
     * 修改项目信息-分析测试中心
     * 
     * @param projectInfoAct 项目信息-分析测试中心
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean update(ProjectInfoAct projectInfoAct)
    {
        return this.updateById(projectInfoAct);
    }

    /**
     * 批量删除项目信息-分析测试中心
     * 
     * @param ids 需要删除的项目信息-分析测试中心主键
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByIds(Long[] ids)
    {
        return this.removeBatchByIds(Arrays.asList(ids));
    }

    @Override
    public void save(ProjectInfo dto) {
        ProjectInfoAct model = new ProjectInfoAct();
        BeanUtils.copyProperties(dto,model);
        if (StrUtil.isBlank(model.getClinicalInstitution())){
            model.setClinicalInstitution(WBYY_CN);
        }
        if (StrUtil.isBlank(model.getStatisticalAnalysisUnit())){
            model.setStatisticalAnalysisUnit(BENAO_CN);
        }
        this.save(model);
    }

    @Override
    public Object update(ProjectBase dto) {
        if (null==dto) return null;
        ProjectInfoAct model = dto.getProjectInfoAct();
        if (null==model) return null;
        model.setId(dto.getId());
        this.saveOrUpdate(model);
        return this.getById(model.getId());
    }

    @Override
    public ProjectBase detail(ProjectBase dto) {
        ProjectInfoAct byId = this.getById(dto.getId());
        if (null!=byId){
            dto.setProjectInfoAct(byId);
        }
        return dto;
    }

    @Override
    public void delete(List<Long> ids) {
        this.removeByIds(ids);
    }
}
