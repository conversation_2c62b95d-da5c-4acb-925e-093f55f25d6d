package com.wbyy.pms.modules.configuration.basicparameter.role.service.impl;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.wbyy.common.core.exception.ServiceException;
import com.wbyy.common.core.utils.StringUtils;
import com.wbyy.common.redis.annotation.RedisLock;
import com.wbyy.common.redis.helper.SimpleLockHelper;
import com.wbyy.log.annotation.OperationLog;
import com.wbyy.log.constant.DefaultOperationType;
import com.wbyy.pms.common.constant.ProjectRoleConst;
import com.wbyy.biz.common.drag.service.impl.BaseVxeServiceImpl;
import com.wbyy.pms.modules.configuration.basicparameter.roledept.domain.ProjectRoleDept;
import com.wbyy.pms.modules.configuration.basicparameter.roledept.mapper.ProjectRoleDeptMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.wbyy.pms.modules.configuration.basicparameter.role.mapper.ProjectRoleMapper;
import com.wbyy.pms.modules.configuration.basicparameter.role.domain.ProjectRole;
import com.wbyy.pms.modules.configuration.basicparameter.role.service.IProjectRoleService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;

import static com.wbyy.common.core.constant.Constants.DISABLE_STATUS;
import static com.wbyy.common.core.constant.Constants.NO_DELETE;
import static com.wbyy.pms.common.constant.lockkey.LockKeyOfProjectConst.LOCK_PROJECT_ROLE;

/**
 * 项目角色Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-04-22
 */
@Slf4j
@Service
public class ProjectRoleServiceImpl extends BaseVxeServiceImpl<ProjectRoleMapper, ProjectRole> implements IProjectRoleService
{
    @Autowired
    private TransactionTemplate transactionTemplate;
    @Autowired
    private SimpleLockHelper simpleLockHelper;
    @Autowired
    private ProjectRoleDeptMapper projectRoleDeptMapper;

    @Override
    protected void dragBefore(ProjectRole entity) {
        super.dragBefore(entity);
        log.debug("ProjectRoleServiceImpl drag before...");
    }

    @Override
    protected void dragAfter(List<ProjectRole> list) {
        super.dragAfter(list);
        log.debug("ProjectRoleServiceImpl drag after...");
    }

    /**
     * 查询项目角色
     * 
     * @param id 项目角色主键
     * @return 项目角色
     */
    @Override
    public ProjectRole selectProjectRoleById(Long id)
    {
        return this.getById(id);
    }

    /**
     * 查询项目角色列表
     * 
     * @param projectRole 项目角色
     * @return 项目角色
     */
    @Override
    public List<ProjectRole> selectProjectRoleList(ProjectRole projectRole)
    {
        LambdaQueryWrapper<ProjectRole> wrapper = Wrappers.lambdaQuery();
        if (null!=projectRole&&null!=projectRole.getDisable()){
            wrapper.eq(ProjectRole::getDisable,projectRole.getDisable());
        }
        wrapper.orderByAsc(ProjectRole::getSort);
        return this.list(wrapper);
    }


    private ProjectRole getPm(){
        LambdaQueryWrapper<ProjectRole> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjectRole::getDelFlag,NO_DELETE);
        queryWrapper.eq(ProjectRole::getNameEn,ProjectRoleConst.PM_ROLE_EN);
        return this.getOne(queryWrapper,false);
    }

    /**
     * 新增项目角色
     * 
     * @param projectRole 项目角色
     * @return 结果
     */
    @Override
    @OperationLog(name = DefaultOperationType.ADD,type = "基础参数",objectClazz = ProjectRole.class)
    @Transactional(rollbackFor = Exception.class)
    @RedisLock(prefix = LOCK_PROJECT_ROLE)
    public ProjectRole insertProjectRole(ProjectRole projectRole)
    {
        // 项目经理校验
        if (ProjectRoleConst.PM_ROLE_EN.equals(projectRole.getNameEn())){
            ProjectRole pm = this.getPm();
            if (null!=pm) throw new ServiceException("已存在项目经理角色");
        }
        // 状态默认值
        projectRole.setDisable(DISABLE_STATUS);
        // 名称校验
        projectRole.setName(StringUtils.trimAndValidation(projectRole.getName(),"项目角色名称不得为空"));
        projectRole.setNameEn(StringUtils.trim(projectRole.getNameEn()));
        ProjectRole byName = this.getByName(projectRole.getName());
        if (null!=byName) throw new ServiceException("此项目角色名称已存在");
        // 不是插入操作
        if (null==projectRole.getTargetObjectId()){
            if (null==projectRole.getSort()){
                Integer sort = this.maxSort();
                projectRole.setSort(sort+1);
            }
            this.save(projectRole);
        }
        // 是插入操作
        else {
            // 被插入数据
            ProjectRole insertedData = this.getById(projectRole.getTargetObjectId());
            if (null==insertedData) throw new ServiceException("被插入数据不存在");
            Integer insertedDataSort = insertedData.getSort();
            // 获取大于这个序号的数据
            List<ProjectRole> listByGeSort = this.listByGeSort(insertedDataSort);
            List<ProjectRole> sortedList = new ArrayList<>();
            // 往上插
            if (projectRole.getInsertUp()){
                sortedList.add(projectRole);
                sortedList.add(insertedData);
            }
            // 往下插
            else {
                sortedList.add(insertedData);
                sortedList.add(projectRole);
            }
            sortedList.addAll(listByGeSort);
            int sort = insertedDataSort;
            for (ProjectRole role : sortedList) {
                role.setSort(sort);
                sort++;
            }
            this.saveOrUpdateBatch(sortedList);
        }
        return projectRole;
    }

    /**
     * 修改项目角色
     * 
     * @param projectRole 项目角色
     * @return 结果
     */
    @Override
    public ProjectRole updateProjectRole(ProjectRole projectRole)
    {
        return simpleLockHelper.execute(LOCK_PROJECT_ROLE,5,20,TimeUnit.SECONDS,()->{
            // 名称校验
            if (StrUtil.isNotBlank(projectRole.getName())){
                projectRole.setName(StringUtils.trimAndValidation(projectRole.getName(),"项目角色名称不得为空"));
                ProjectRole byName = this.getByName(projectRole.getName());
                if (null!=byName&&!byName.getId().equals(projectRole.getId())){
                    throw new ServiceException("此项目角色名称已存在");
                }
            }
            if (StrUtil.isNotBlank(projectRole.getNameEn())){
                // 项目经理校验
                if (ProjectRoleConst.PM_ROLE_EN.equals(projectRole.getNameEn())){
                    ProjectRole pm = this.getPm();
                    if (null!=pm&&!projectRole.getId().equals(pm.getId())){
                        throw new ServiceException("项目经理角色已存在");
                    }
                }
                projectRole.setNameEn(StringUtils.trim(projectRole.getNameEn()));
            }
            this.updateById(projectRole);
            return projectRole;
        });
    }

    /**
     * @Description: 根据角色名称获取数据
     * @param name
     * @return: ProjectRole
     * @author: 王金都
     * @Date: 2025/5/9 10:07
     */
    private ProjectRole getByName(String name){
        LambdaQueryWrapper<ProjectRole> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjectRole::getName,name);
        queryWrapper.eq(ProjectRole::getDelFlag,NO_DELETE);
        return this.getOne(queryWrapper,false);
    }

    /**
     * 批量删除项目角色
     * 
     * @param ids 需要删除的项目角色主键
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteProjectRoleByIds(Long[] ids)
    {
        if (null!=ids&&ids.length>0){
            // 校验数据关联
            List<ProjectRoleDept> roleDepts = projectRoleDeptMapper.listByRoleIds(Arrays.asList(ids));
            if (CollectionUtil.isNotEmpty(roleDepts)) throw new ServiceException("角色已关联组织，不可删除");
            return this.removeBatchByIds(Arrays.asList(ids));
        }else return false;
    }

    /**
     * 删除项目角色信息
     * 
     * @param id 项目角色主键
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteProjectRoleById(Long id)
    {
        return this.removeById(id);
    }

    @Override
    public Integer maxSort() {
        Integer maxSort = this.baseMapper.getMaxSort();
        if (null==maxSort){
            maxSort=0;
        }
        return maxSort;
    }

    @Override
    public Map<Long, String> findMapByIds(Collection<Long> ids) {
        if (CollUtil.isEmpty(ids)){
            return Map.of();
        }
        LambdaQueryWrapper<ProjectRole> wrapper = Wrappers.lambdaQuery();
        wrapper.select(ProjectRole::getId,ProjectRole::getName)
                .in(ProjectRole::getId, ids);
        List<ProjectRole> list = this.list(wrapper);
        if (CollUtil.isNotEmpty(list)){
            return list.stream().collect(Collectors.toMap(ProjectRole::getId, ProjectRole::getName));
        }
        return Map.of();
    }

    /**
     * 获取大于某序号的数据
     * @param sort
     * @return
     */
    private List<ProjectRole> listByGeSort(Integer sort){
        LambdaQueryWrapper<ProjectRole> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.gt(ProjectRole::getSort,sort);
        queryWrapper.orderByAsc(ProjectRole::getSort);
        return this.list(queryWrapper);
    }
}
