package com.wbyy.pms.modules.configuration.roleperm.domain.vo;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Set;

/**
 * @author: 王金都
 * @date: 2025/4/29 15:17
 */
@Data
@Schema(description = "项目角色权限绑定关系实体类")
public class RolePermVo {
    @Schema(description = "项目角色id")
    private Long projectRoleId;
    @Schema(description = "权限字集合")
    private Set<String> permCodes;
}
