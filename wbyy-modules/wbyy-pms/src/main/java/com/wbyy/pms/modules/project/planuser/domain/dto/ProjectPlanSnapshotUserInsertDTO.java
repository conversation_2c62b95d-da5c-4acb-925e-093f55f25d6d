package com.wbyy.pms.modules.project.planuser.domain.dto;

import com.wbyy.pms.modules.project.planuser.domain.ProjectPlanSnapshotUser;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

@Data
@Builder
@Schema(description = "新增责任人DTO")
public class ProjectPlanSnapshotUserInsertDTO implements Serializable {


    @Serial
    private static final long serialVersionUID = -595258211414865213L;
    /**
     * 关联项目id
     */
    @Schema(description = "项目id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "项目id不能为空")
    private Long projectId;

    /**
     * 关联计划id
     */
    @Schema(description = "计划id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "计划id不能为空")
    private Long projectPlanId;

    @ArraySchema(
            arraySchema = @Schema(description = "责任信息"),
            schema = @Schema(implementation = ProjectPlanSnapshotUser.class)
    )
    @Valid
    private List<ProjectPlanSnapshotUser> users;
}
