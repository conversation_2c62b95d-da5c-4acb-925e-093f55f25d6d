package com.wbyy.pms.modules.project.planfront.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.wbyy.common.core.annotation.Excel;
import com.wbyy.orm.core.domain.BaseAuditEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serial;
import java.util.Date;
import java.util.List;

/**
 * 项目前置计划 - 编制对象 project_plan_front_snapshot
 *
 * <AUTHOR>
 * @date 2025-05-13
 */
@Data
@TableName("project_plan_front_snapshot")
@SuperBuilder
@Accessors(chain = true)
@NoArgsConstructor
@Schema(description = "项目前置计划 - 编制实体类")
@EqualsAndHashCode(callSuper = true)
public class ProjectPlanFrontSnapshot extends BaseAuditEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @Schema(description = "主键id（新增：不传；修改：必传）")
    private Long id;

    /**
     * 项目id
     */
    @Excel(name = "项目id")
    @Schema(description = "项目id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "项目id不能为空")
    private Long projectId;

    /**
     * 项目计划id
     */
    @Excel(name = "项目计划id")
    @Schema(description = "项目计划id", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long projectPlanId;

    /**
     * 前置任务ID
     */
    @Excel(name = "前置任务ID")
    @Schema(description = "前置任务ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long frontPlanId;

    /**
     * 前置任务序号
     */
    @Schema(description = "前置任务序号")
    private Integer frontPlanSort;

    /**
     * 删除标志（0代表存在 1代表删除）
     */
    @Schema(description = "删除标志（0代表存在 1代表删除）")
    @TableLogic
    private Integer delFlag;

    @TableField(exist = false)
    @Schema(description = "责任人名称")
    private String frontPlanUserNames;

    @TableField(exist = false)
    @Schema(description = "计划名称")
    private String frontPlanName;

    /**
     * 计划结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Schema(description = "计划结束时间")
    @TableField(exist = false)
    private Date frontPlanPlanEndDate;


    @JsonFormat(pattern = "yyyy-MM-dd")
    @Schema(description = "计划开始时间")
    @TableField(exist = false)
    private Date frontPlanPlanStartDate;

    @TableField(exist = false)
    private List<ProjectPlanFrontSnapshot> children;

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("projectId", getProjectId())
                .append("projectPlanId", getProjectPlanId())
                .append("frontPlanId", getFrontPlanId())
                .append("frontPlanSort", getFrontPlanSort())
                .append("delFlag", getDelFlag())
                .append("createBy", getCreateBy())
                .append("createNameBy", getCreateNameBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateNameBy", getUpdateNameBy())
                .append("updateTime", getUpdateTime())
                .toString();
    }
}
