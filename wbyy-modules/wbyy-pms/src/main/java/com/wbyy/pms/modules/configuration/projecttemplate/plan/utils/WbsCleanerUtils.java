package com.wbyy.pms.modules.configuration.projecttemplate.plan.utils;

import com.wbyy.biz.common.drag.domain.BaseVxeHierarchyWbsEntity;

import java.util.*;


/**
 * 清洗 WBS 工具类
 *
 * <AUTHOR>
 */
public class WbsCleanerUtils {
    public static < T extends  BaseVxeHierarchyWbsEntity> void cleanWbs(List<T> nodes) {
        if (nodes == null || nodes.isEmpty()) return;

        // 构建父子关系映射
        Map<Long, List<BaseVxeHierarchyWbsEntity>> parentChildrenMap = new HashMap<>();
        Map<Long, BaseVxeHierarchyWbsEntity> idMap = new HashMap<>();

        for (BaseVxeHierarchyWbsEntity node : nodes) {
            idMap.put(node.getId(), node);
            parentChildrenMap.computeIfAbsent(node.getParentId(), k -> new ArrayList<>()).add(node);
        }

        // 根节点判定并排序
        List<BaseVxeHierarchyWbsEntity> roots = parentChildrenMap.getOrDefault(0L, new ArrayList<>())
                .stream()
                .sorted(Comparator.comparingInt(BaseVxeHierarchyWbsEntity::getSort))
                .toList();

        // 找到第一个正确的根节点（根据问题描述应为parentId=0的节点）
//        BaseVxeHierarchyWbsEntity firstCorrectRoot = roots.stream()
//                .filter(node -> node.getWbs().matches("^\\d+$")) // 根节点WBS应为纯数字
//                .findFirst()
//                .orElseThrow(() -> new IllegalArgumentException("No valid root node"));

        // 重新生成所有根节点WBS
        int baseNumber = 1;
        for (int i = 0; i < roots.size(); i++) {
            BaseVxeHierarchyWbsEntity root = roots.get(i);
            root.setWbs(String.valueOf(baseNumber + i));
        }

        // 递归生成子节点WBS
        roots.forEach(root -> generateChildWbs(root, parentChildrenMap));
    }

    private static void generateChildWbs(BaseVxeHierarchyWbsEntity parent,
                                  Map<Long, List<BaseVxeHierarchyWbsEntity>> parentChildrenMap) {
        List<BaseVxeHierarchyWbsEntity> children = parentChildrenMap.getOrDefault(parent.getId(), new ArrayList<>())
                .stream()
                .sorted(Comparator.comparingInt(BaseVxeHierarchyWbsEntity::getSort))
                .toList();

        int counter = 1;
        for (BaseVxeHierarchyWbsEntity child : children) {
            child.setWbs(parent.getWbs() + "." + counter);
            generateChildWbs(child, parentChildrenMap);
            counter++;
        }
    }
}

