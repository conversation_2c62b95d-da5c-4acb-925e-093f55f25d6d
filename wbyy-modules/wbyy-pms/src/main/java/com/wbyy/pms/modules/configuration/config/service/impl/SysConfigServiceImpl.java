package com.wbyy.pms.modules.configuration.config.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wbyy.common.core.exception.ServiceException;
import com.wbyy.common.redis.service.RedisService;
import com.wbyy.pms.modules.configuration.config.domain.SysConfig;
import com.wbyy.pms.modules.configuration.config.mapper.SysConfigMapper;
import com.wbyy.pms.modules.configuration.config.service.ISysConfigService;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;

import static com.wbyy.common.core.constant.CacheConstants.PMS_SYS_CONFIG_KEY;

/**
 * 系统参数Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-02
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SysConfigServiceImpl extends ServiceImpl<SysConfigMapper, SysConfig> implements ISysConfigService {

    private final SysConfigMapper sysConfigMapper;
    private final RedisService redisService;


    /**
     * 项目启动时，初始化参数到缓存
     */
    @PostConstruct
    public void init() {
        log.debug("[系统参数]加载参数缓存");
        loadingConfigCache();
    }

    /**
     * 查询系统参数
     *
     * @param id 系统参数主键
     * @return 系统参数
     */
    @Override
    public SysConfig selectById(Long id) {
        return this.getById(id);
    }

    /**
     * 查询系统参数列表
     *
     * @param sysConfig 系统参数
     * @return 系统参数
     */
    @Override
    public List<SysConfig> selectList(SysConfig sysConfig) {
        LambdaQueryWrapper<SysConfig> wrapper = Wrappers.lambdaQuery();
        if (StrUtil.isNotEmpty(sysConfig.getSearchValue())) {
            wrapper.and(w ->
                    w.like(SysConfig::getConfigName, sysConfig.getSearchValue())
                            .or().like(SysConfig::getConfigKey, sysConfig.getSearchValue())
            );
        }
        wrapper.orderByDesc(SysConfig::getCreateTime);
        return this.list(wrapper);
    }

    /**
     * 新增系统参数
     *
     * @param sysConfig 系统参数
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean insert(SysConfig sysConfig) {
        checkData(sysConfig);
        boolean saved = this.save(sysConfig);
        if (saved) {
            //  添加缓存
            redisService.setCacheObject(getCacheKey(sysConfig.getConfigKey()), sysConfig.getConfigValue());
        }
        return saved;
    }

    /**
     * 参数校验
     *
     * @param sysConfig 系统参数
     * <AUTHOR>
     * @date 2025/7/2 10:03
     */
    private void checkData(SysConfig sysConfig) {
        LambdaQueryWrapper<SysConfig> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(SysConfig::getConfigKey, sysConfig.getConfigKey());
        if (sysConfig.getId() != null) {
            wrapper.ne(SysConfig::getId, sysConfig.getId());
        }
        if (this.count(wrapper) > 0) {
            throw new ServiceException("参数名称已存在");
        }
    }

    /**
     * 修改系统参数
     *
     * @param sysConfig 系统参数
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean update(SysConfig sysConfig) {
        // 数据校验
        checkData(sysConfig);
        boolean updated = this.updateById(sysConfig);
        if (updated) {
            /*
             *      * 正常	0
             *      * 停用	1
             */
            if ("0".equals(sysConfig.getStatus())) {
                // 正常
                //  添加缓存
                redisService.setCacheObject(getCacheKey(sysConfig.getConfigKey()), sysConfig.getConfigValue());
            } else {
                // 禁用
                // 删除缓存
                redisService.deleteObject(getCacheKey(sysConfig.getConfigKey()));
            }
        }
        return updated;
    }

    /**
     * 批量删除系统参数
     *
     * @param ids 需要删除的系统参数主键
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByIds(Long[] ids) {
        if (ids != null) {
            // 删除缓存
            for (Long id : ids) {
                SysConfig sysConfig = this.selectById(id);
                redisService.deleteObject(getCacheKey(sysConfig.getConfigKey()));
                this.removeBatchByIds(List.of(id));
            }
        }
        return true;
    }

    @Override
    public SysConfig selectCacheConfigByKey(String configKey) {
        LambdaQueryWrapper<SysConfig> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(SysConfig::getConfigKey, configKey)
                .eq(SysConfig::getStatus, "0");
        SysConfig one = this.getOne(wrapper, false);
        if (one == null) {
            throw new ServiceException("参数不存在或未启用，请联系管理员");
        }
        one.setId(null);
        return one;
    }

    @Override
    public String selectConfigValueCacheConfigByKey(String configKey) {
        SysConfig sysConfig = selectCacheConfigByKey(configKey);
        return sysConfig.getConfigValue();
    }

    @Override
    public void resetConfigCache() {
        clearConfigCache();
        loadingConfigCache();
    }


    @Override
    public void clearConfigCache() {
        Collection<String> keys = redisService.keys(PMS_SYS_CONFIG_KEY + "*");
        redisService.deleteObject(keys);
    }

    /**
     * 加载参数缓存数据
     */
    @Override
    public void loadingConfigCache() {
        List<SysConfig> configsList = this.list();
        for (SysConfig config : configsList) {
            redisService.setCacheObject(getCacheKey(config.getConfigKey()), config.getConfigValue());
        }
    }

    /**
     * 设置cache key
     *
     * @param configKey 参数键
     * @return 缓存键key
     */
    private String getCacheKey(String configKey) {
        return PMS_SYS_CONFIG_KEY + configKey;
    }
}
