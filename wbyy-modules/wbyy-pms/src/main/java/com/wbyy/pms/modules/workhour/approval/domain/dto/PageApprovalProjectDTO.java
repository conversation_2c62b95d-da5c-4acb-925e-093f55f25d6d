package com.wbyy.pms.modules.workhour.approval.domain.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/6/11  8:38
 * @description 查询工时审批涉及的项目
 */
@Data
public class PageApprovalProjectDTO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "项目编号、名称模糊搜索")
    private String searchValue;

    @Schema(description = "true已审核，false待审核")
    @NotNull(message = "审核状态不能为空")
    private Boolean hasApproval;

    @Schema(description = "true我提交的")
    private Boolean isMineFill;

    @Schema(description = "true我审核的")
    private Boolean isMineApproval;
}
