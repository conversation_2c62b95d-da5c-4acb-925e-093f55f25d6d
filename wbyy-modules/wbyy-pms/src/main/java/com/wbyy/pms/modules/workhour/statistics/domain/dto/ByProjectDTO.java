package com.wbyy.pms.modules.workhour.statistics.domain.dto;

import com.wbyy.system.api.model.ApiBaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/12  16:03
 * @description 工时按项目统计
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ByProjectDTO extends ApiBaseEntity {

    @Schema(description = "年")
    @NotBlank(message = "年份不能为空")
    private String yearNumber;

    @Schema(description = "月")
    @NotNull(message = "月份不能为空")
    private Integer monthNumber;

    private List<Long> projectIds;
}
