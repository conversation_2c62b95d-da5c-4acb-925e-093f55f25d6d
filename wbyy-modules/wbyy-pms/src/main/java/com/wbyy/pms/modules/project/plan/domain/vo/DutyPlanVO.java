package com.wbyy.pms.modules.project.plan.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.wbyy.common.core.annotation.Excel;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/5/26  17:43
 * @description 参与任务返回值
 */
@Data
public class DutyPlanVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "主键id(同project_plan_snapshot表id)（新增：不传；修改：必传）")
    private Long id;

    @Schema(description = "所属项目id")
    private Long projectId;

    @Schema(description = "任务名称")
    private String name;

    @Schema(description = "项目名称")
    private String projectName;

    /**
     * 计划开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "计划开始时间", width = 30, dateFormat = "yyyy-MM-dd")
    @Schema(description = "计划开始时间")
    private Date planStartDate;

    /**
     * 计划结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "计划结束时间", width = 30, dateFormat = "yyyy-MM-dd")
    @Schema(description = "计划结束时间")
    private Date planEndDate;

    @JsonFormat(pattern = "yyyy-MM-dd")
    @Schema(description = "实际开始时间")
    private Date realityStartDate;

    @JsonFormat(pattern = "yyyy-MM-dd")
    @Schema(description = "实际结束时间")
    private Date realityEndDate;

    @Schema(description = "项目编码")
    private String number;

    /** 集团合同编号 */
    @Schema(description = "集团合同编号")
    private String groupContractNumber;

    @Schema(description = "项目经理")
    private String projectManager;

    @Schema(description = "任务状态", implementation = StatusVO.class)
    private StatusVO status;

    @Schema(description = "前置任务标志，1已完成 0未完成 空：没有前置任务")
    private Integer frontTaskFlag;
}
