package com.wbyy.pms.modules.configuration.oacallbacklog.domain;

import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.wbyy.common.core.annotation.Excel;
import com.wbyy.orm.core.domain.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serial;

/**
 * OA回调接口日志对象 oa_callback_log
 *
 * <AUTHOR>
 * @date 2025-06-25
 */
@Data
@SuperBuilder
@NoArgsConstructor
@Accessors(chain = true)
@TableName(value = "oa_callback_log", autoResultMap = true)
@Schema(description = "OA回调接口日志实体类")
@EqualsAndHashCode(callSuper = true)
public class OaCallbackLog extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @Schema(description = "主键id（新增：不传；修改：必传）")
    private Long id;

    /**
     * 类路径
     */
    @Excel(name = "类路径")
    @Schema(description = "类路径")
    @Size(max = 255, message = "类路径不能超过 255 个字符")
    private String className;

    /**
     * 入参
     */
    @Excel(name = "入参")
    @Schema(description = "入参")
    private String methodParams;

    /**
     * 返回结果
     */
    @Excel(name = "返回结果")
    @Schema(description = "返回结果")
    private String res;

    @Schema(description = "耗时")
    private Long consumeTime;

    /**
     * 异常信息
     */
    @Excel(name = "异常信息")
    @Schema(description = "异常信息")
    private String errorMsg;

    /**
     * 删除标志（0代表存在 1代表删除）
     */
    @Schema(description = "删除标志（0代表存在 1代表删除）", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "异常信息不能为空")
    @TableLogic
    private Integer delFlag;


    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("className", getClassName())
                .append("params", getParams())
                .append("res", getRes())
                .append("errorMsg", getErrorMsg())
                .append("delFlag", getDelFlag())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .toString();
    }
}
