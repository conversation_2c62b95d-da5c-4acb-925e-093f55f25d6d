package com.wbyy.pms.modules.project.planrelate.service;

import java.util.List;
import com.baomidou.mybatisplus.extension.service.IService;
import com.wbyy.pms.modules.project.planrelate.domain.ProjectPlanVersionRelate;

/**
 * 项目计划关联-版本Service接口
 *
 * <AUTHOR>
 * @date 2025-05-13
 */
public interface IProjectPlanVersionRelateService  extends IService<ProjectPlanVersionRelate> {
    /**
     * 查询项目计划关联-版本
     *
     * @param id 项目计划关联-版本主键
     * @return 项目计划关联-版本
     */
    ProjectPlanVersionRelate selectById(Long id);

    /**
     * 查询项目计划关联-版本列表
     *
     * @param projectPlanVersionRelate 项目计划关联-版本
     * @return 项目计划关联-版本集合
     */
    List<ProjectPlanVersionRelate> selectList(ProjectPlanVersionRelate projectPlanVersionRelate);

    /**
     * 新增项目计划关联-版本
     *
     * @param projectPlanVersionRelate 项目计划关联-版本
     * @return 结果
     */
    boolean insert(ProjectPlanVersionRelate projectPlanVersionRelate);

    /**
     * 修改项目计划关联-版本
     *
     * @param projectPlanVersionRelate 项目计划关联-版本
     * @return 结果
     */
    boolean update(ProjectPlanVersionRelate projectPlanVersionRelate);

    /**
     * 批量删除项目计划关联-版本
     *
     * @param ids 需要删除的项目计划关联-版本主键集合
     * @return 结果
     */
    boolean deleteByIds(Long[] ids);

}
