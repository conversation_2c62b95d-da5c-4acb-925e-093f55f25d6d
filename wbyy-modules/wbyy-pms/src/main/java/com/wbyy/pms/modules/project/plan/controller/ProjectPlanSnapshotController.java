package com.wbyy.pms.modules.project.plan.controller;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson2.JSON;
import com.wbyy.common.core.domain.R;
import com.wbyy.common.core.utils.TreeUtil;
import com.wbyy.common.core.utils.bean.BeanUtils;
import com.wbyy.common.core.web.controller.BaseController;
import com.wbyy.common.core.web.page.TableDataInfo;
import com.wbyy.common.log.annotation.Log;
import com.wbyy.common.log.enums.BusinessType;
import com.wbyy.common.redis.annotation.RedisLock;
import com.wbyy.common.security.annotation.RequiresPermissions;
import com.wbyy.pms.common.annotaion.ProjectAuthCheck;
import com.wbyy.biz.common.drag.dto.VxeDragDTO;
import com.wbyy.biz.common.drag.dto.VxeUpOrDownGradeDTO;
import com.wbyy.pms.common.utils.vo.ProjectProgressVo;
import com.wbyy.pms.modules.project.plan.domain.ProjectPlanSnapshot;
import com.wbyy.pms.modules.project.plan.domain.dto.EffectiveDTO;
import com.wbyy.pms.modules.project.plan.service.IProjectPlanSnapshotService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Set;

import static com.wbyy.pms.common.constant.lockkey.LockKeyOfProjectConst.LOCK_PROJECT_PLAN;
import static com.wbyy.pms.common.constant.lockkey.LockKeyOfProjectConst.LOCK_PROJECT_PLAN_DEL;

/**
 * 项目计划 - 编制Controller
 *
 * <AUTHOR>
 * @date 2025-05-13
 */
@RestController
@RequiredArgsConstructor
@Tag(name = "项目计划 - 编制")
@Slf4j
@RequestMapping("/project/plan/snapshot")
public class ProjectPlanSnapshotController extends BaseController {

    private final IProjectPlanSnapshotService projectPlanSnapshotService;

    @Operation(summary = "分页列表")
    @RequiresPermissions("project:plan-snapshot:list")
    @GetMapping("/page")
    public TableDataInfo<ProjectPlanSnapshot> page(@ParameterObject ProjectPlanSnapshot projectPlanSnapshot) {
        startPage();
        List<ProjectPlanSnapshot> list = projectPlanSnapshotService.selectList(projectPlanSnapshot);
        return getDataTable(list);
    }

    @Operation(summary = "列表(tree)")
    @RequiresPermissions("project:plan-snapshot:list")
    @GetMapping("/list")
    public R<List<ProjectPlanSnapshot>> list(@ParameterObject ProjectPlanSnapshot projectPlanSnapshot) {
        List<ProjectPlanSnapshot> list = projectPlanSnapshotService.selectList(projectPlanSnapshot);
        List<ProjectPlanSnapshot> tree = TreeUtil.buildTree(JSON.parseArray(JSON.toJSONString(list)), ProjectPlanSnapshot.class);
        return R.ok(tree);
    }


//    @Operation(summary = "导出列表")
//    @RequiresPermissions("project:plan:export")
//    @Log(title = "项目计划 - 编制", businessType = BusinessType.EXPORT)
//    @PostMapping("/export")
//    public void export(HttpServletResponse response, ProjectPlanSnapshot projectPlanSnapshot) {
//        List<ProjectPlanSnapshot> list = projectPlanSnapshotService.selectList(projectPlanSnapshot);
//        ExcelUtil<ProjectPlanSnapshot> util = new ExcelUtil<ProjectPlanSnapshot>(ProjectPlanSnapshot.class);
//        util.exportExcel(response, list, "项目计划 - 编制数据");
//    }

    @Operation(summary = "获取详细信息")
    @RequiresPermissions("project:plan:query")
    @GetMapping(value = "/{id}")
    public R<ProjectPlanSnapshot> getInfo(@PathVariable("id") Long id) {
        return R.ok(projectPlanSnapshotService.selectById(id));
    }

    @Operation(summary = "新增")
    @ProjectAuthCheck(projectId = "#projectPlanSnapshot.projectId",permCode = "project:plan:add")
    @Log(title = "项目计划 - 编制", businessType = BusinessType.INSERT)
    @RedisLock(prefix = LOCK_PROJECT_PLAN, key = "#projectPlanSnapshot.projectId")
    @PostMapping
    public R<ProjectPlanSnapshot> add(@RequestBody @Valid ProjectPlanSnapshot projectPlanSnapshot) {
        Set<Long> updateIds = new LinkedHashSet<>();
        ProjectPlanSnapshot item = projectPlanSnapshotService.saveNew(projectPlanSnapshot, updateIds);
        item.setUpdateIds(updateIds);
        return R.ok(item);
    }

    @Operation(summary = "修改")
    @ProjectAuthCheck(projectId = "#projectPlanSnapshot.projectId",permCode = "project:plan:edit")
    @Log(title = "项目计划 - 编制", businessType = BusinessType.UPDATE)
    @PutMapping
    @RedisLock(prefix = LOCK_PROJECT_PLAN, key = "#projectPlanSnapshot.projectId")
    public R<ProjectPlanSnapshot> edit(@RequestBody @Validated ProjectPlanSnapshot projectPlanSnapshot) {
        return R.ok(projectPlanSnapshotService.update(projectPlanSnapshot));
    }

    @Operation(summary = "删除")
    @RequiresPermissions("project:plan:remove")
    @Log(title = "项目计划 - 编制", businessType = BusinessType.DELETE)
    @RedisLock(prefix = LOCK_PROJECT_PLAN_DEL)
    @DeleteMapping("/{ids}")
    public R<Boolean> remove(@PathVariable Long[] ids) {
        projectPlanSnapshotService.deleteByIds(Arrays.asList(ids));
        return R.ok(true);
    }

    @Operation(summary = "拖拽")
    @ProjectAuthCheck(projectId = "#vxeDragDTO.projectId",permCode ="basic:plan:drag")
    @Log(title = "项目模板计划", businessType = BusinessType.DRAG)
    @PostMapping("/drag")
    @RedisLock(prefix = LOCK_PROJECT_PLAN, key = "#vxeDragDTO.projectId")
    public R<ProjectPlanSnapshot> drag(@RequestBody VxeDragDTO vxeDragDTO) {
        ProjectPlanSnapshot entity = new ProjectPlanSnapshot();
        BeanUtils.copyProperties(vxeDragDTO, entity);
        return R.ok( projectPlanSnapshotService.doDrag(entity));

    }

    @Operation(summary = "升/降级")
    @ProjectAuthCheck(projectId = "#vxeUpOrDownGradeDTO.projectId",permCode ="basic:plan:upOrDownGrade")
    @Log(title = "项目模板计划", businessType = BusinessType.UP_OR_DOWN_GRADE)
    @RedisLock(prefix = LOCK_PROJECT_PLAN, key = "#vxeUpOrDownGradeDTO.projectId")
    @PostMapping("/up-down-grade")
    public R<ProjectPlanSnapshot> upOrDownGrade(@RequestBody VxeUpOrDownGradeDTO vxeUpOrDownGradeDTO) {
        ProjectPlanSnapshot entity = new ProjectPlanSnapshot();
        BeanUtil.copyProperties(vxeUpOrDownGradeDTO, entity);
        return R.ok(projectPlanSnapshotService.doUpOrDownGrade(entity));
    }

    @Operation(summary = "获取最新的版本号")
    @GetMapping("now-version")
    @ProjectAuthCheck(projectId = "#projectId",permCode = "project:plan:effective")
    public R<Integer> getNowVersion(Long projectId) {
        return R.ok(projectPlanSnapshotService.getNowVersion(projectId));
    }

    @Operation(summary = "计划生效")
    @PostMapping("effective")
    @ProjectAuthCheck(projectId = "#dto.projectId",permCode = "project:plan:effective")
    public R<Boolean> effective(@RequestBody @Validated EffectiveDTO dto) {
        projectPlanSnapshotService.effective(dto);
        return R.ok(true);
    }

    @Operation(summary = "查询进度")
    @RequiresPermissions("project:plan-snapshot:list")
    @GetMapping("progress")
    public R<ProjectProgressVo> getProgress(@ParameterObject EffectiveDTO dto) {
        return R.ok(projectPlanSnapshotService.getProgress(dto));
    }
}
