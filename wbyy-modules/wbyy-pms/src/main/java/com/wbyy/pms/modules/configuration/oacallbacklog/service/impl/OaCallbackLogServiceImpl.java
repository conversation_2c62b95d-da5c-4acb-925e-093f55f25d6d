package com.wbyy.pms.modules.configuration.oacallbacklog.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wbyy.pms.modules.configuration.oacallbacklog.domain.OaCallbackLog;
import com.wbyy.pms.modules.configuration.oacallbacklog.mapper.OaCallbackLogMapper;
import com.wbyy.pms.modules.configuration.oacallbacklog.service.IOaCallbackLogService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.List;

/**
 * OA回调接口日志Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-25
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OaCallbackLogServiceImpl extends ServiceImpl<OaCallbackLogMapper, OaCallbackLog> implements IOaCallbackLogService {
    private final OaCallbackLogMapper oaCallbackLogMapper;

    /**
     * 查询OA回调接口日志
     *
     * @param id OA回调接口日志主键
     * @return OA回调接口日志
     */
    @Override
    public OaCallbackLog selectById(Long id) {
        return this.getById(id);
    }

    /**
     * 查询OA回调接口日志列表
     *
     * @param oaCallbackLog OA回调接口日志
     * @return OA回调接口日志
     */
    @Override
    public List<OaCallbackLog> selectList(OaCallbackLog oaCallbackLog) {
        LambdaQueryWrapper<OaCallbackLog> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(StrUtil.isNotEmpty(oaCallbackLog.getClassName()), OaCallbackLog::getClassName, oaCallbackLog.getClassName());
        wrapper.eq(StrUtil.isNotEmpty(oaCallbackLog.getRes()), OaCallbackLog::getRes, oaCallbackLog.getRes());
        wrapper.orderByDesc(OaCallbackLog::getId);
        return this.list(wrapper);
    }

    /**
     * 新增OA回调接口日志
     *
     * @param oaCallbackLog OA回调接口日志
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean insert(OaCallbackLog oaCallbackLog) {
        return this.save(oaCallbackLog);
    }

    /**
     * 修改OA回调接口日志
     *
     * @param oaCallbackLog OA回调接口日志
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean update(OaCallbackLog oaCallbackLog) {
        return this.updateById(oaCallbackLog);
    }

    /**
     * 批量删除OA回调接口日志
     *
     * @param ids 需要删除的OA回调接口日志主键
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByIds(Long[] ids) {
        return this.removeBatchByIds(Arrays.asList(ids));
    }

}
