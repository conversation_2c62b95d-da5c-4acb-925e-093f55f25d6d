package com.wbyy.pms.modules.contract.customer.service.impl;

import java.util.List;
import java.util.Arrays;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import com.wbyy.pms.modules.contract.customer.mapper.ContractCustomerMapper;
import com.wbyy.pms.modules.contract.customer.domain.ContractCustomer;
import com.wbyy.pms.modules.contract.customer.service.IContractCustomerService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.transaction.annotation.Transactional;

/**
 * 合同客户信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-25
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ContractCustomerServiceImpl extends ServiceImpl<ContractCustomerMapper, ContractCustomer> implements IContractCustomerService
{
    private final ContractCustomerMapper contractCustomerMapper;

    /**
     * 查询合同客户信息
     * 
     * @param id 合同客户信息主键
     * @return 合同客户信息
     */
    @Override
    public ContractCustomer selectById(Long id)
    {
        return this.getById(id);
    }

    /**
     * 查询合同客户信息列表
     * 
     * @param contractCustomer 合同客户信息
     * @return 合同客户信息
     */
    @Override
    public List<ContractCustomer> selectList(ContractCustomer contractCustomer)
    {
        LambdaQueryWrapper<ContractCustomer> wrapper = Wrappers.lambdaQuery();
        wrapper.orderByDesc(ContractCustomer::getCreateTime);
        return this.list(wrapper);
    }

    /**
     * 新增合同客户信息
     * 
     * @param contractCustomer 合同客户信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean insert(ContractCustomer contractCustomer)
    {
        return this.save(contractCustomer);
    }

    /**
     * 修改合同客户信息
     * 
     * @param contractCustomer 合同客户信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean update(ContractCustomer contractCustomer)
    {
        return this.updateById(contractCustomer);
    }

    /**
     * 批量删除合同客户信息
     * 
     * @param ids 需要删除的合同客户信息主键
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByIds(Long[] ids)
    {
        return this.removeBatchByIds(Arrays.asList(ids));
    }

    @Override
    public void deleteByContractId(Long contractId) {
        LambdaQueryWrapper<ContractCustomer> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ContractCustomer::getContractId,contractId);
        this.remove(queryWrapper);
    }

    /**
     * 根据合同id获取客户信息
     *
     * @param contractId 合同id
     * @return 客户信息
     */
    @Override
    public ContractCustomer getInfoByContractId(Long contractId) {
        LambdaQueryWrapper<ContractCustomer> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ContractCustomer::getContractId,contractId);
        return this.getOne(queryWrapper);
    }
}
