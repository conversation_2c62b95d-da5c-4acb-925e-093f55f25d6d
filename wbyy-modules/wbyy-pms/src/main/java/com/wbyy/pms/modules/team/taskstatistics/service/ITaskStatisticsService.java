package com.wbyy.pms.modules.team.taskstatistics.service;

import com.wbyy.pms.modules.team.taskstatistics.domain.dto.TaskStatisticsDTO;
import com.wbyy.pms.modules.team.taskstatistics.domain.vo.TaskStatisticsTotalVO;
import com.wbyy.pms.modules.team.taskstatistics.domain.vo.TaskStatisticsVO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/7/2  8:59
 * @description 任务统计接口
 */
public interface ITaskStatisticsService {

    /**
     * 分页查询任务统计数据
     * @param dto 入参
     * @return  任务统计数据
     */
    List<TaskStatisticsVO> selectList(TaskStatisticsDTO dto);

    /**
     * 分页查询任务统计数据汇总
     * @param dto 入参
     * @return  任务统计数据
     */
    TaskStatisticsTotalVO selectTotal(TaskStatisticsDTO dto);
}
