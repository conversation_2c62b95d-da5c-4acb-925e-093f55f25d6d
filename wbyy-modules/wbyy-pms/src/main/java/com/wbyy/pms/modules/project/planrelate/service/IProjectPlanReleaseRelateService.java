package com.wbyy.pms.modules.project.planrelate.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wbyy.pms.modules.project.planrelate.domain.ProjectPlanReleaseRelate;
import com.wbyy.pms.modules.project.planrelate.domain.vo.RelateProjectDetailVO;

import java.util.List;
import java.util.Map;

/**
 * 项目计划关联-跟踪Service接口
 *
 * <AUTHOR>
 * @date 2025-05-13
 */
public interface IProjectPlanReleaseRelateService extends IService<ProjectPlanReleaseRelate> {
    /**
     * 查询项目计划关联-跟踪
     *
     * @param id 项目计划关联-跟踪主键
     * @return 项目计划关联-跟踪
     */
    ProjectPlanReleaseRelate selectById(Long id);

    /**
     * 查询项目计划关联-跟踪列表
     *
     * @param projectPlanReleaseRelate 项目计划关联-跟踪
     * @return 项目计划关联-跟踪集合
     */
    List<ProjectPlanReleaseRelate> selectList(ProjectPlanReleaseRelate projectPlanReleaseRelate);

    /**
     * 新增项目计划关联-跟踪
     *
     * @param projectPlanReleaseRelate 项目计划关联-跟踪
     * @return 结果
     */
    boolean insert(ProjectPlanReleaseRelate projectPlanReleaseRelate);

    /**
     * 修改项目计划关联-跟踪
     *
     * @param projectPlanReleaseRelate 项目计划关联-跟踪
     * @return 结果
     */
    boolean update(ProjectPlanReleaseRelate projectPlanReleaseRelate);

    /**
     * 批量删除项目计划关联-跟踪
     *
     * @param ids 需要删除的项目计划关联-跟踪主键集合
     * @return 结果
     */
    boolean deleteByIds(Long[] ids);


    /**
     * 获取被关联项目明细
     *
     * @param id 任务id
     */
    RelateProjectDetailVO getRelateProjectDetail(Long id);

    RelateProjectDetailVO getRelateProjectDetailCommon(Long projectId, Long planId);

    /**
     * 根据项目ID 删除计划关联信息 - 跟踪
     *
     * @param projectId 项目ID
     * @return 结果
     */
    boolean deleteByProjectId(Long projectId);

    /**
     * 查询计划是否有关联任务
     *
     * @param projectId     项目ID
     * @param projectPlanId 计划任务ID
     */
    Map<Long, Boolean> findMapByProjectIdAndPlanIds(Long projectId, List<Long> projectPlanId);

    /**
     * 查询计划关联信息 - 编制
     *
     * @param projectId 项目id
     * @param planIds   计划ids
     */
    List<ProjectPlanReleaseRelate> findByProjectIdAndPlanIds(Long projectId, List<Long> planIds);

    /**
     * 校验任务是否关联其他任务
     *
     * @param projectId 项目id
     * @param planId    计划id
     * @return 是否关联其他任务
     */
    boolean checkIsRelatedOther(Long projectId, Long planId);
}
