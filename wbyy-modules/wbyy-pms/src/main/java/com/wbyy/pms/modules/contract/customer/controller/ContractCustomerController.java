package com.wbyy.pms.modules.contract.customer.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.List;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.wbyy.common.core.domain.R;
import com.wbyy.common.log.annotation.Log;
import com.wbyy.common.log.enums.BusinessType;
import com.wbyy.common.security.annotation.RequiresPermissions;
import com.wbyy.pms.modules.contract.customer.domain.ContractCustomer;
import com.wbyy.pms.modules.contract.customer.service.IContractCustomerService;
import com.wbyy.common.core.web.controller.BaseController;
import com.wbyy.common.core.utils.poi.ExcelUtil;
import com.wbyy.common.core.web.page.TableDataInfo;

/**
 * 合同客户信息Controller
 * 
 * <AUTHOR>
 * @date 2025-06-25
 */
@RestController
@RequiredArgsConstructor
@Tag(name = "合同客户信息")
@RequestMapping("/contract/customer")
public class ContractCustomerController extends BaseController
{

    private final IContractCustomerService contractCustomerService;

    @Operation(summary = "获取详细信息")
    @RequiresPermissions("contract:customer:query")
    @GetMapping(value = "/{id}")
    public R<ContractCustomer> getInfo(@PathVariable("id") Long id)
    {
        return R.ok(contractCustomerService.selectById(id));
    }

    @Operation(summary = "根据合同id获取详细信息")
    @RequiresPermissions("contract:customer:query")
    @GetMapping(value = "/info-by-contract-id")
    public R<ContractCustomer> getInfoByContractId(@RequestParam("contractId") Long contractId)
    {
        return R.ok(contractCustomerService.getInfoByContractId(contractId));
    }
}
