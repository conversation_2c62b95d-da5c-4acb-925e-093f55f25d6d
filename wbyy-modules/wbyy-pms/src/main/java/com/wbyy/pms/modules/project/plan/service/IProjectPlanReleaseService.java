package com.wbyy.pms.modules.project.plan.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wbyy.pms.modules.project.plan.domain.ProjectPlanRelease;
import com.wbyy.pms.modules.project.plan.domain.dto.ContractSelectOptionsDTO;
import com.wbyy.pms.modules.project.plan.domain.dto.PageDutyPlanDTO;
import com.wbyy.pms.modules.project.plan.domain.dto.ProjectPlanReleaseEditDTO;
import com.wbyy.pms.modules.project.plan.domain.dto.UserPlanDto;
import com.wbyy.pms.modules.project.plan.domain.vo.*;
import com.wbyy.system.api.domain.ApiSysFile;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 项目计划 - 跟踪Service接口
 *
 * <AUTHOR>
 * @date 2025-05-13
 */
public interface IProjectPlanReleaseService extends IService<ProjectPlanRelease> {
    /**
     * 查询项目计划 - 跟踪
     *
     * @param id 项目计划 - 跟踪主键
     * @return 项目计划 - 跟踪
     */
    ProjectPlanDetailVO selectById(Long id);

    /**
     * 查询基础数据
     *
     * @param id 主键
     * @return 基础数据
     */
    ProjectPlanRelease getBasicById(Long id);

    /**
     * 查询项目计划 - 跟踪列表
     *
     * @param projectPlanRelease 项目计划 - 跟踪
     * @return 项目计划 - 跟踪集合
     */
    List<ProjectPlanRelease> selectList(ProjectPlanRelease projectPlanRelease);

    /**
     * 查询参与任务
     *
     * @param dto 入参
     * @return 参与任务
     */
    List<DutyPlanVO> selectDutyPlan(PageDutyPlanDTO dto);

    /**
     * 新增项目计划 - 跟踪
     *
     * @param projectPlanRelease 项目计划 - 跟踪
     * @return 结果
     */
    boolean insert(ProjectPlanRelease projectPlanRelease);

    /**
     * 修改项目计划 - 跟踪
     *
     * @param projectPlanRelease 项目计划 - 跟踪
     * @return 结果
     */
    boolean update(ProjectPlanRelease projectPlanRelease);

    /**
     * 批量删除项目计划 - 跟踪
     *
     * @param ids 需要删除的项目计划 - 跟踪主键集合
     * @return 结果
     */
    boolean deleteByIds(Long[] ids);

    /**
     * @param projectIds
     * @Description: 根据项目id集合获取项目跟踪计划
     * @return: List<ProjectPlanRelease>
     * @author: 王金都
     * @Date: 2025/5/14 10:30
     */
    List<ProjectPlanRelease> listByProjectIds(List<Long> projectIds);

    /**
     * 修改项目跟踪计划
     *
     * @param dto 入参
     * @return 结果
     */
    boolean edit(ProjectPlanReleaseEditDTO dto);

    /**
     * 查询实际进度、实际开始、实际结束、实际工期(单位：天) - （实际工时计算得出） 的 map
     *
     * @param projectId 项目ID
     * @param ids       任务IDs
     * @return 结果
     */
    Map<Long, ProjectPlanRelease> findMapByProjectIdAndPlanIds(Long projectId, List<Long> ids);

    /**
     * 查询实 任务名称 的 map
     *
     * @param projectId 项目ID
     * @param ids       任务IDs
     * @return 结果
     */
    Map<Long, ProjectPlanRelease> findNameMapByProjectIdAndPlanIds(Long projectId, List<Long> ids);


    /**
     * 根据项目ID删除计划 - 跟踪
     *
     * @param projectId
     * @return
     */
    boolean deleteByProjectId(Long projectId);

    /**
     * 获取当前任务的全路径
     *
     * @param id 计划id
     * @return 全路径
     */
    String getPlanFullPath(Long id);

    /**
     * 上传预期文档
     *
     * @param projectId 项目id
     * @param file      文件流
     * @return 文件信息
     */
    ApiSysFile uploadExpectedDocs(Long projectId, MultipartFile file);

    /**
     * 下载预期文档
     *
     * @param fileId 文件id
     */
    void downloadExpectedDocs(Long fileId, HttpServletResponse response);


    /**
     * 查询所有子级
     *
     * @param id 主键id
     * @return 所有子级
     */
    List<ProjectPlanRelease> getChildren(Long id);

    /**
     * 查询所有子级
     *
     * @param idList 主键id
     * @return 所有子级
     */
    List<ProjectPlanRelease> getChildrenAndSelfByIdList(List<Long> idList);

    /**
     * 查询 所有父级
     *
     * @param id 主键
     * @return
     */
    List<ProjectPlanRelease> selectAllParentById(Long id);

    /**
     * 查询 所有父级
     *
     * @param idList 主键
     * @return
     */
    List<ProjectPlanRelease> selectAllParentByIdList(Collection<Long> idList);


    /**
     * 查询关联该计划的计划
     *
     * @param planId 计划id
     * @return 计划
     */
    List<ProjectPlanRelease> findByRelatedPlanId(Long planId);

    /**
     * 批量修改实际开始、实际结束、工期、进度等字段
     *
     * @param planReleases 计划
     * @return 结果
     */
    void updateBatchWithNull(List<ProjectPlanRelease> planReleases);

    /**
     * 叶子节点的计划开始时间，计划结束时间，计划工期修改后，父级的实际进度修改
     *
     * @param planIds 变更的计划id
     */
    void planChangedUpdateParentRealityProgress(Collection<Long> planIds);

    /**
     * 修改所有父级的 实际进度、 实际开始时间、实际结束时间、工期
     *
     * @param releaseList 计划
     * @return 计划
     */
    List<ProjectPlanRelease> updateParentsPlan(List<ProjectPlanRelease> releaseList);

    /**
     * 修改所有计划的实际进度、 实际开始时间、实际结束时间、工期
     * 然后递归修改父级的
     *
     * @param releaseList     计划
     * @param realityProgress 实际进度
     * @param startTime       实际开始时间
     * @param endTime         实际结束时间
     * @param period          工期
     * @return 计划
     */
    List<ProjectPlanRelease> updateParentsPlanAndSelf(List<ProjectPlanRelease> releaseList, Integer realityProgress, Date startTime, Date endTime, Integer period);

    /**
     * @param dto
     * @Description: 用户的待办和已办
     * @return: List<UserPlanVo>
     * @author: 王金都
     * @Date: 2025/6/5 16:43
     */
    List<UserPlanVo> selectUserPlan(UserPlanDto dto);

    /**
     * 工时同步
     *
     * @param planWorkHourMap 计划-工时map
     * @return 结果
     */
    boolean syncWorkHours(Map<Long, BigDecimal> planWorkHourMap);


    /**
     * 列表（下拉框-tree）
     *
     * @param dto
     * @return
     */
    List<ProjectPlanRelease> selectOptions(ContractSelectOptionsDTO dto);

    /**
     * 根据计划id集合。查询计划信息和责任人信息
     * @param planIds 计划id集合
     * @return 计划信息和责任人信息
     */
    List<ProjectPlanRelease> findByPlanIds(Collection<Long> planIds);

    /**
     * 根据责任人、项目id、任务的计划起止时间查询任务信息
     * @param userIds 责任人
     * @param projectId 项目id
     * @param startTime 计划起止时间
     * @param endTime 计划起止时间
     * @return 任务信息
     */
    List<DutyPlanDetailVO> findByUserIdsAndProjectIdAndTime(Collection<Long> userIds, Long projectId, Date startTime, Date endTime);

    /**
     * 根据责任人、任务的计划起止时间查询任务信息
     * @param userIds 责任人
     * @param startTime 计划起止时间
     * @param endTime 计划起止时间
     * @return 任务信息
     */
    List<DutyPlanDailyVO> findByUserIdsAndTime(Collection<Long> userIds, Date startTime, Date endTime,Long projectId, Integer projectType);

    /**
     * 最近反馈过的任务，默认是最近7天
     * @param day 天数
     * @return 任务
     */
    List<UserPlanVo> latestFeedback(Integer day);
}
