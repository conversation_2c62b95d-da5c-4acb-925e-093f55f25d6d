package com.wbyy.pms.modules.project.teamperson.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wbyy.pms.modules.project.teamperson.domain.ProjectTeamPersonPO;
import com.wbyy.pms.modules.project.teamperson.dto.ProjectTeamPersonDTO;
import com.wbyy.pms.modules.project.teamperson.vo.ProjectTeamPersonVO;
import com.wbyy.system.api.domain.ApiUser;

import java.util.List;
import java.util.Set;

/**
 * 项目团队人员Service接口
 *
 * <AUTHOR>
 * @date 2025-05-06
 */
public interface IProjectTeamPersonService extends IService<ProjectTeamPersonPO> {
    /**
     * 查询项目团队人员
     *
     * @param id 项目团队人员主键
     * @return 项目团队人员
     */
    ProjectTeamPersonPO selectById(Long id);

    /**
     * 查询项目团队人员列表
     *
     * @param projectTeamPerson 项目团队人员
     * @return 项目团队人员集合
     */
    List<ProjectTeamPersonPO> selectList(ProjectTeamPersonPO projectTeamPerson);

    /**
     * 新增项目团队人员
     *
     * @param dto 项目团队人员
     * @return 结果
     */
    List<ProjectTeamPersonPO> insert(ProjectTeamPersonDTO dto);

    /**
     * 修改项目团队人员
     *
     * @param projectTeamPerson 项目团队人员
     * @return 结果
     */
    boolean update(ProjectTeamPersonPO projectTeamPerson);

    /**
     * 批量删除项目团队人员
     *
     * @param ids 需要删除的项目团队人员主键集合
     * @return 结果
     */
    boolean deleteByIds(Long[] ids);

    /**
     * 删除（项目ID、项目角色ID、用户ID）
     *
     * @param projectId     项目ID
     * @param projectRoleId 项目角色ID
     * @param userId        用户ID
     * @return 结果
     */
    ProjectTeamPersonPO deleteBy(Long projectId, Long projectRoleId, Long userId);

    /**
     * 根据项目ID 查询团队成员
     *
     * @param projectTeamPerson 项目成员
     * @return 成员列表（按角色展示）
     */
    List<ProjectTeamPersonVO> selectListByProjectId(ProjectTeamPersonPO projectTeamPerson);

    List<ProjectTeamPersonPO> listPmByProjectIds(Set<Long> projectIds);

    /**
     * @param userId
     * @Description: 根据用户id获取参与项目
     * @return: Set<Long>
     * @author: 王金都
     * @Date: 2025/5/21 15:15
     */
    Set<Long> listProjectIdsByUserId(Long userId);

    /**
     * @param projectId
     * @param userId
     * @Description: 检查用户是否参与项目
     * @return: Boolean
     * @author: 王金都
     * @Date: 2025/5/21 16:30
     */
    Boolean inProjectCheck(Long projectId, Long userId);

    /**
     * 查询身为项目经理的项目id集合
     *
     * @param userId 用户id
     * @return 项目id集合
     */
    List<Long> findPmProjectIdsByUserId(Long userId);

    /**
     * 根据编号查询项目对应的项目经理id
     *
     * @param groupContractNumber   集合合同编号
     * @param companyContractNumber 公司合同编号
     * @return 项目经理id
     */
    List<Long> findPmByNumbers(String groupContractNumber, String companyContractNumber);

    /**
     * 根据部门ID查询部门用户
     *
     * @return List<DeptApi>
     * <AUTHOR>
     * @date 2025/6/30 15:56
     */
    List<ApiUser> getAllUserByDept(Long deptId, String keyword);

    /**
     * 根据项目id，查询项目成员
     * @param projectId 项目id
     * @return 项目成员
     */
    List<ProjectTeamPersonPO> findByProjectId(Long projectId);


    /**
     * 根据项目角色id，查询项目成员
     * @param projectRoleId 项目角色id
     * @return 项目成员
     */
    List<ProjectTeamPersonPO> findByProjectIdAndProjectRoleId(Long projectId, Long projectRoleId);
}
