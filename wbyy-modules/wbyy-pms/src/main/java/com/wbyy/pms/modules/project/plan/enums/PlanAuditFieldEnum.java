package com.wbyy.pms.modules.project.plan.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/5/30  9:13
 * @description 计划变更监听字段
 */
@Getter
public enum PlanAuditFieldEnum {

    PLAN_CONSTRUCTION_PERIOD("planConstructionPeriod", "计划工期"),
    PLAN_START_DATE("planStartDate", "计划开始时间"),
    PLAN_END_DATE("planEndDate", "计划结束时间"),
    DUTY_RULE("dutyRule", null),
    EXPECTED_DOCS("expectedDocs", null);

    private final String fieldName;
    private final String displayName;

    PlanAuditFieldEnum(String fieldName, String displayName) {
        this.fieldName = fieldName;
        this.displayName = displayName;
    }
}
