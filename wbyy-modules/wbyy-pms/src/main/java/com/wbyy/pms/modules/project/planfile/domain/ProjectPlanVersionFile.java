package com.wbyy.pms.modules.project.planfile.domain;

import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.wbyy.common.core.annotation.Excel;
import com.wbyy.orm.core.domain.BaseAuditEntity;

import java.io.Serial;

/**
 * 项目计划文件-版本对象 project_plan_version_file
 * 
 * <AUTHOR>
 * @date 2025-05-14
 */
@Data
@SuperBuilder
@NoArgsConstructor
@Accessors(chain = true)
@TableName("project_plan_version_file")
@Schema(description = "项目计划文件-版本实体类")
@EqualsAndHashCode(callSuper = true)
public class ProjectPlanVersionFile extends BaseAuditEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /** 主键id */
    @Schema(description = "主键id（新增：不传；修改：必传）")
    private Long id;

    /** 项目计划文件表id */
    @Excel(name = "项目计划文件表id")
    @Schema(description = "项目计划文件表id")
    private Long projectPlanFileId;

    /** 版本号 */
    @Excel(name = "版本号")
    @Schema(description = "版本号")
    private Integer version;

    /** 关联项目id */
    @Excel(name = "关联项目id")
    @Schema(description = "关联项目id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "关联项目id不能为空")
    private Long projectId;

    /** 关联计划任务id */
    @Excel(name = "关联计划任务id")
    @Schema(description = "关联计划任务id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "关联计划任务id不能为空")
    private Long projectPlanId;

    /** 文件大小 */
    @Excel(name = "文件大小")
    @Schema(description = "文件大小", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "文件大小不能为空")
    private Long fileSize;

    /** 原始文件名 */
    @Excel(name = "原始文件名")
    @Schema(description = "原始文件名", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "原始文件名不能为空")
    @Size(max = 100, message = "原始文件名不能超过 100 个字符")
    private String fileName;

    /** 文件地址 */
    @Excel(name = "文件地址")
    @Schema(description = "文件地址", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "文件地址不能为空")
    @Size(max = 500, message = "文件地址不能超过 500 个字符")
    private String filePath;

    /** 删除标志（0代表存在 1代表删除） */
    @Schema(description = "删除标志（0代表存在 1代表删除）", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "文件地址不能为空")
    @TableLogic
    private Integer delFlag;


    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("projectPlanFileId", getProjectPlanFileId())
            .append("version", getVersion())
            .append("projectId", getProjectId())
            .append("projectPlanId", getProjectPlanId())
            .append("fileSize", getFileSize())
            .append("fileName", getFileName())
            .append("filePath", getFilePath())
            .append("delFlag", getDelFlag())
            .append("createBy", getCreateBy())
            .append("createNameBy", getCreateNameBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateNameBy", getUpdateNameBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
