package com.wbyy.pms.modules.project.planrelate.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025/5/26  9:49
 * @description 项目下已完成的任务的计划工时
 */
@Data
@Accessors(chain = true)
@Builder
public class FinishedProjectWorkHourVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "项目id")
    private Long projectId;

    @Schema(description = "计划工时")
    private BigDecimal workHour;
}
