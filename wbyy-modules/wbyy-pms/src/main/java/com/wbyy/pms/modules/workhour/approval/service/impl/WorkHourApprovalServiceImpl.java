package com.wbyy.pms.modules.workhour.approval.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wbyy.common.core.constant.HttpMagConstants;
import com.wbyy.common.core.exception.ServiceException;
import com.wbyy.common.core.utils.PageUtils;
import com.wbyy.common.core.utils.StringUtils;
import com.wbyy.common.rabbitmq.service.RabbitService;
import com.wbyy.common.security.utils.SecurityUtils;
import com.wbyy.log.annotation.OperationLog;
import com.wbyy.pms.modules.log.content.workHour.ApprovalWorkHourContentBuilder;
import com.wbyy.pms.modules.project.planuser.domain.ProjectPlanReleaseUser;
import com.wbyy.pms.modules.project.planuser.service.IProjectPlanReleaseUserService;
import com.wbyy.pms.modules.project.teamperson.service.IProjectTeamPersonService;
import com.wbyy.pms.modules.workhour.approval.domain.WorkHourApproval;
import com.wbyy.pms.modules.workhour.approval.domain.dto.ApprovalDTO;
import com.wbyy.pms.modules.workhour.approval.domain.dto.PageApprovalDTO;
import com.wbyy.pms.modules.workhour.approval.domain.dto.PageApprovalProjectDTO;
import com.wbyy.pms.modules.workhour.approval.domain.vo.ApprovalProjectVO;
import com.wbyy.pms.common.enums.WorkHourStatusEnum;
import com.wbyy.pms.modules.workhour.approval.mapper.WorkHourApprovalMapper;
import com.wbyy.pms.modules.workhour.approval.service.IWorkHourApprovalService;
import com.wbyy.pms.modules.workhour.detail.domain.WorkHourDetail;
import com.wbyy.pms.modules.workhour.detail.service.IWorkHourDetailService;
import com.wbyy.pms.modules.workhour.entry.service.IWorkHourEntryService;
import com.wbyy.system.api.model.LoginUser;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.wbyy.pms.common.constant.RabbitmqConst.WORK_HOUR_EXCHANGE;
import static com.wbyy.pms.common.constant.RabbitmqConst.WORK_HOUR_FILLED_KEY;

/**
 * 工时审核流程Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-05
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WorkHourApprovalServiceImpl extends ServiceImpl<WorkHourApprovalMapper, WorkHourApproval> implements IWorkHourApprovalService {
    private final WorkHourApprovalMapper workHourApprovalMapper;
    private final IProjectTeamPersonService projectTeamPersonService;
    private final IWorkHourEntryService workHourEntryService;
    private final IProjectPlanReleaseUserService projectPlanReleaseUserService;
    private final IWorkHourDetailService workHourDetailService;
    private final RabbitService rabbitService;

    /**
     * 查询工时审核流程
     *
     * @param id 工时审核流程主键
     * @return 工时审核流程
     */
    @Override
    public WorkHourApproval selectById(Long id) {
        return this.getById(id);
    }

    /**
     * 查询工时审核流程列表
     *
     * @param dto 入参
     * @return 工时审核流程集合
     */
    @Override
    public List<WorkHourApproval> selectList(PageApprovalDTO dto) {
        Long userId = SecurityUtils.getUserId();
        Long approvalUserId = null;
        List<Long> projectIds = null;
        //我审核的
        if (BooleanUtil.isTrue(dto.getIsMineApproval())) {
            if (BooleanUtil.isTrue(dto.getHasApproval())) {
                approvalUserId = userId;
            } else if (null == dto.getProjectId()) {
                projectIds = projectTeamPersonService.findPmProjectIdsByUserId(userId);
            }
        }
        //项目过滤
        if (null != dto.getProjectId()) {
            projectIds = List.of(dto.getProjectId());
        }
        if (StrUtil.isNotEmpty(dto.getProjectIds())) {
            dto.setProjectIdList(StrUtil.split(dto.getProjectIds(), StrUtil.COMMA).stream().map(Long::parseLong).toList());
        }

        //审核状态
        List<Integer> statusList = BooleanUtil.isTrue(dto.getHasApproval()) ? List.of(WorkHourStatusEnum.PASS.getCode(),
                WorkHourStatusEnum.REJECTED.getCode()) : List.of(WorkHourStatusEnum.WAIT_APPROVAL.getCode());
        if (dto.getHasApproval()) {
            PageUtils.startPage("approvalTime","desc");
        }else {
            PageUtils.startPage("createTime","asc");
        }
        List<WorkHourApproval> approvals = baseMapper.selectListByObject(dto, projectIds, statusList, approvalUserId, BooleanUtil.isTrue(dto.getIsMineFill()) ? userId : null);

        Set<Long> planIds = new HashSet<>();
        Set<Long> userIds = new HashSet<>();
        approvals.forEach(approval -> {
            userIds.add(approval.getCreateBy());
            if (null != approval.getPlanId()) {
                planIds.add(approval.getPlanId());
            }
        });
        //todo 改为异步
        //条目-人，计划工时
        Map<String, BigDecimal> concatIdPlanMap = projectPlanReleaseUserService.findByProjectPlanIdsAndUserIds(planIds, userIds)
                .stream().collect(Collectors.toMap(item ->
                        item.getProjectPlanId() + "" + item.getUserId(),
                        ProjectPlanReleaseUser::getWorkHour,
                        (v1, v2) -> v1)
                );
        //条目-人，已填报工时
        Map<String, List<WorkHourDetail>> concatIdTotalMap =
                workHourDetailService.findByPlanIdsAndUserIds(
                        planIds, userIds,
                        List.of(WorkHourStatusEnum.PASS.getCode(),
                                WorkHourStatusEnum.WAIT_APPROVAL.getCode(),
                                WorkHourStatusEnum.ARCHIVE.getCode()
                        )).stream().collect(
                        Collectors.groupingBy(item -> item.getPlanId() + "" + item.getCreateBy()));
        approvals.forEach(approvalVO -> {
            String key = approvalVO.getPlanId() + "" + approvalVO.getCreateBy();
            if (concatIdPlanMap.containsKey(key)) {
                approvalVO.setPlanWorkHour(concatIdPlanMap.get(key));
            }
            if (concatIdTotalMap.containsKey(key)) {
                approvalVO.setTotalWorkHour(concatIdTotalMap.get(key).stream().filter(item -> item.getFillDate()
                        .before(approvalVO.getFillDate())).map(WorkHourDetail::getWorkHour).reduce(BigDecimal.ZERO, BigDecimal::add));
            }
        });
        return approvals;
    }

    /**
     * 新增工时审核流程
     *
     * @param workHourApproval 工时审核流程
     * @return 结果
     */
    @Override
    public boolean insert(WorkHourApproval workHourApproval) {
        return this.save(workHourApproval);
    }

    /**
     * 修改工时审核流程
     *
     * @param workHourApproval 工时审核流程
     * @return 结果
     */
    @Override
    public boolean update(WorkHourApproval workHourApproval) {
        return this.updateById(workHourApproval);
    }

    /**
     * 批量删除工时审核流程
     *
     * @param ids 需要删除的工时审核流程主键
     * @return 结果
     */
    @Override
    public boolean deleteByIds(Long[] ids) {
        return this.removeBatchByIds(Arrays.asList(ids));
    }

    /**
     * 审批
     *
     * @param dto 入参
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    @OperationLog(name = "通过工时",
    type = "工时",
    objectClazz = ApprovalDTO.class,
    contentBuilder = ApprovalWorkHourContentBuilder.class)
    public ApprovalDTO approval(ApprovalDTO dto) {
        if (WorkHourStatusEnum.REJECTED.getCode().equals(dto.getStatus()) && StringUtils.isBlank(dto.getRemark())) {
            throw new ServiceException(HttpMagConstants.PARAMS_NOT_NULL);
        }
        LambdaQueryWrapper<WorkHourApproval> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(WorkHourApproval::getId, dto.getIds());
        queryWrapper.eq(WorkHourApproval::getStatus, WorkHourStatusEnum.WAIT_APPROVAL.getCode());
        List<WorkHourApproval> approvals = this.list(queryWrapper);
        //能查到待审批的就走审批，查不到直接返回成功
        if (!approvals.isEmpty()) {
            LoginUser loginUser = SecurityUtils.getLoginUser();
            Long userId = loginUser.getUserId();
            String workNumber = loginUser.getWorkNumber();
            String realName = loginUser.getRealName();
            Date now = new Date();
            approvals.forEach(workHourApproval -> {
                workHourApproval.setApprovalTime(now);
                workHourApproval.setApprovalUserId(userId);
                workHourApproval.setApprovalWorkNumber(workNumber);
                workHourApproval.setApprovalRealName(realName);
                workHourApproval.setStatus(dto.getStatus());
                workHourApproval.setRemark(dto.getRemark());
            });
            Set<Long> idSet = approvals.stream()
                    .map(WorkHourApproval::getWorkHourDetailId).collect(Collectors.toSet());
            List<WorkHourDetail> workHourDetails = workHourDetailService.listByIds(idSet);
            workHourDetails.forEach(item -> {
                item.setStatus(dto.getStatus());
                item.setRejectReason(dto.getRemark());
            });
            this.updateBatchById(approvals);
            workHourDetailService.updateBatchById(workHourDetails);
            // 审批通过发送消息
            if (WorkHourStatusEnum.PASS.getCode().equals(dto.getStatus())) {
                rabbitService.sendMessage(WORK_HOUR_EXCHANGE,WORK_HOUR_FILLED_KEY, JSON.toJSONString(idSet));
            }
        }
        dto.setApprovals(approvals);
        return dto;
    }

    @Override
    public boolean deleteByWorkHourDetailIds(List<Long> workHourDetailIds) {
        if (CollUtil.isEmpty(workHourDetailIds)) {
            return false;
        }
        LambdaQueryWrapper<WorkHourApproval> wrapper = Wrappers.lambdaQuery();
        wrapper.in(WorkHourApproval::getWorkHourDetailId, workHourDetailIds)
                .eq(WorkHourApproval::getStatus, WorkHourStatusEnum.WAIT_APPROVAL.getCode());
        return this.remove(wrapper);
    }

    /**
     * 查询提交工时审核的项目
     *
     * @param dto 入参
     * @return 项目
     */
    @Override
    public List<ApprovalProjectVO> pageApprovalProject(PageApprovalProjectDTO dto) {
        Long userId = SecurityUtils.getUserId();
        Long approvalUserId = null;
        List<Long> projectIds = null;
        //我审核的
        if (BooleanUtil.isTrue(dto.getIsMineApproval())) {
            if (BooleanUtil.isTrue(dto.getHasApproval())) {
                approvalUserId = userId;
            } else {
                projectIds = projectTeamPersonService.findPmProjectIdsByUserId(userId);
            }
        }
        //审核状态
        List<Integer> statusList = BooleanUtil.isTrue(dto.getHasApproval()) ? List.of(WorkHourStatusEnum.PASS.getCode(),
                WorkHourStatusEnum.REJECTED.getCode()) : List.of(WorkHourStatusEnum.WAIT_APPROVAL.getCode());
        PageUtils.startPage();
        return baseMapper.selectProjectByObject(dto, projectIds, statusList, approvalUserId, BooleanUtil.isTrue(dto.getIsMineFill()) ? userId : null);
    }
}
