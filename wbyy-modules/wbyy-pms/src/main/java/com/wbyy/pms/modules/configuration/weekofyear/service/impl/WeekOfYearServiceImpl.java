package com.wbyy.pms.modules.configuration.weekofyear.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.Week;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wbyy.common.core.constant.SecurityConstants;
import com.wbyy.common.core.constant.SqlConstants;
import com.wbyy.common.core.domain.R;
import com.wbyy.common.core.exception.ServiceException;
import com.wbyy.common.core.utils.DateUtils;
import com.wbyy.common.security.utils.SecurityUtils;
import com.wbyy.pms.common.constant.PMSConst;
import com.wbyy.pms.common.enums.WorkHourStatusEnum;
import com.wbyy.pms.modules.configuration.weekofyear.constant.WeekOfYearConst;
import com.wbyy.pms.modules.configuration.weekofyear.domain.WeekOfYear;
import com.wbyy.pms.modules.configuration.weekofyear.domain.vo.WeekDetailVo;
import com.wbyy.pms.modules.configuration.weekofyear.mapper.WeekOfYearMapper;
import com.wbyy.pms.modules.configuration.weekofyear.service.IWeekOfYearService;
import com.wbyy.pms.modules.workhour.detail.domain.WorkHourDetail;
import com.wbyy.pms.modules.workhour.detail.mapper.WorkHourDetailMapper;
import com.wbyy.system.api.CalendarApi;
import com.wbyy.system.api.VacationApi;
import com.wbyy.system.api.domain.ApiCalendar;
import com.wbyy.system.api.domain.ApiVacation;
import com.wbyy.system.api.model.LoginUser;
import com.wbyy.system.api.utils.VacationUtils;
import com.wbyy.weaver.api.AttendApi;
import com.weaver.openapi.pojo.attend.params.AttendVo;
import com.weaver.openapi.pojo.attend.res.vo.AttendResultVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

import static com.wbyy.common.core.constant.Constants.SIGN_IN_NORMAL;
import static com.wbyy.common.core.constant.Constants.SIGN_OUT_NORMAL;

/**
 * 周年Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-05
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WeekOfYearServiceImpl extends ServiceImpl<WeekOfYearMapper, WeekOfYear> implements IWeekOfYearService {
    private final WeekOfYearMapper weekOfYearMapper;
    private final VacationApi  vacationApi;
    private final AttendApi attendApi;
    private final CalendarApi calendarApi;
    private final WorkHourDetailMapper workHourDetailMapper;

    /**
     * 查询周年
     *
     * @param id 周年主键
     * @return 周年
     */
    @Override
    public WeekOfYear selectById(Long id) {
        return this.getById(id);
    }

    /**
     * 查询周年列表
     *
     * @param weekOfYear 周年
     * @return 周年
     */
    @Override
    public List<WeekOfYear> selectList(WeekOfYear weekOfYear) {
        LambdaQueryWrapper<WeekOfYear> wrapper = Wrappers.lambdaQuery();
        wrapper.orderByDesc(WeekOfYear::getCreateTime);
        return this.list(wrapper);
    }

    /**
     * 新增周年
     *
     * @param weekOfYear 周年
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean insert(WeekOfYear weekOfYear) {
        return this.save(weekOfYear);
    }

    /**
     * 修改周年
     *
     * @param weekOfYear 周年
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean update(WeekOfYear weekOfYear) {
        return this.updateById(weekOfYear);
    }

    /**
     * 批量删除周年
     *
     * @param ids 需要删除的周年主键
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByIds(Long[] ids) {
        return this.removeBatchByIds(Arrays.asList(ids));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void asyncData() {
        Date now = new Date();
        this.asyncData(DateUtil.format(now, "yyyy-MM-dd"));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void asyncData(String year) {
        List<WeekOfYear> weekList = new ArrayList<>();
        // 获取该年第一天
        DateTime firstDayOfYear = DateUtil.parse(year + WeekOfYearConst.BEGIN_OF_YEAR);
        // 获取该年最后一天
        DateTime lastDayOfYear = DateUtil.parse(year + WeekOfYearConst.END_OF_YEAR);
        // 计算上一年最后一天
        DateTime prevYearLastDay = DateUtil.offsetDay(firstDayOfYear, -1);
        // 计算上一年最后一周
        DateTime prevWeekStart = DateUtil.beginOfWeek(prevYearLastDay);
        DateTime prevWeekEnd = DateUtil.endOfWeek(prevYearLastDay);
        // 判断是否需要合并跨年周
        if (prevWeekEnd.isAfter(prevYearLastDay)) {
            prevWeekEnd = prevYearLastDay;
        }
        // 计算实际天数
        int prevWeekDays = (int) DateUtil.betweenDay(prevWeekStart, prevWeekEnd, true);
        // 处理跨年周合并
        DateTime weekStart = firstDayOfYear;
        if (prevWeekDays < 6) {
            // 生成合并周日期详情
            List<String> mergedDates = new ArrayList<>();
            DateTime currentDay = prevWeekStart;
            while (currentDay.compareTo(prevWeekEnd) <= 0) {
                mergedDates.add(DateUtil.format(currentDay, "yyyy-MM-dd"));
                currentDay = DateUtil.offsetDay(currentDay, 1);
            }
            // 继续添加新年日期直到满7天
            currentDay = firstDayOfYear;
            while (mergedDates.size() < 7) {
                mergedDates.add(DateUtil.format(currentDay, "yyyy-MM-dd"));
                prevWeekEnd = currentDay;
                currentDay = DateUtil.offsetDay(currentDay, 1);
            }
            WeekOfYear mergedWeek = new WeekOfYear()
                    .setYearNumber(year)
                    .setWeekNumber(1)
                    .setStartDate(prevWeekStart)
                    .setEndDate(prevWeekEnd);
            mergedWeek.setDateDetail(mergedDates);
            weekList.add(mergedWeek);
            // 新的周开始日期从合并周结束的下一天开始
            weekStart = DateUtil.offsetDay(mergedWeek.getEndDate(), 1);
        }
        // 处理第一周
        int weekNum = weekList.isEmpty() ? 1 : 2;
        // 处理所有周
        while (DateUtil.compare(weekStart, lastDayOfYear) <= 0) {
            // 计算本周结束日期
            DateTime weekEnd;
            // 如果是第一周且不是周一开始，则找到本周日
            if (weekNum == 1 && DateUtil.dayOfWeekEnum(weekStart) != Week.MONDAY) {
                // 找到本周日的正确方式
                int dayOfWeek = DateUtil.dayOfWeek(weekStart);
                // 周日
                if (DateUtil.dayOfWeekEnum(weekStart) == Week.SUNDAY) {
                    weekEnd = weekStart;
                } else {
                    // 计算到周日的天数
                    int daysToSunday = (7 - (dayOfWeek - 1)) % 7;
                    weekEnd = DateUtil.offsetDay(weekStart, daysToSunday);
                }
            } else {
                // 正常情况下，周结束日期是开始日期后的第6天（包含当天共7天）
                weekEnd = DateUtil.offsetDay(weekStart, 6);
            }
            // 确保不超过年末（增加更精确的边界检查）
            if (weekEnd.isAfter(lastDayOfYear)) {
                weekEnd = lastDayOfYear;
            }
            // 创建本周数据
            createWeekData(weekList, year, weekNum, weekStart, weekEnd);
            // 移动到下一周的开始（周结束日期的下一天）
            weekStart = DateUtil.offsetDay(weekEnd, 1);
            weekNum++;
            // 如果已经超过年末，则结束循环
            if (DateUtil.compare(weekStart, lastDayOfYear) > 0) {
                break;
            }
        }
        if (CollectionUtil.isNotEmpty(weekList)) {
            // 最后一周是否不满七天
            WeekOfYear lastWeek = weekList.get(weekList.size() - 1);
            // 计算实际包含天数
            int actualDays = (int) DateUtil.betweenDay(lastWeek.getStartDate(), lastWeek.getEndDate(), true);
            if (actualDays < 6) { // 包含开始日共7天
                // 移除最后一周
                weekList.remove(weekList.size() - 1);
            }
            this.deleteByYear(year);
            this.saveBatch(weekList);
        }
    }

    @Override
    public List<WeekOfYear> list(String yearNumber) {
        return listYearWeeks(yearNumber, new Date());
    }

    @Override
    public List<WeekDetailVo> detail(String yearNumber, Integer weekNumber) {
        WeekOfYear week = getByYearNumberByWeekNumber(yearNumber, weekNumber);
        if (null == week) {
            return Collections.emptyList();
        }
        List<WeekDetailVo> details = new ArrayList<>();
        List<String> dateDetail = week.getDateDetail();
        if (CollectionUtil.isEmpty(dateDetail)) {
            return details;
        }
        LoginUser user = SecurityUtils.getLoginUser();
        Date startDate = week.getStartDate();
        Date endDate = week.getEndDate();
        // 获取休息日
        Set<String> restDays = this.restDays(startDate, endDate);
        // 获取请假数据
        List<ApiVacation> vacations = this.vacationList(user.getUserId(), startDate, endDate);
        // 获取考情数据
        String weaverUserId = user.getWeaverUserId();
        String startDateStr = DateUtil.formatDate(startDate);
        String endDateStr = DateUtil.formatDate(endDate);
        // 签到
        List<AttendResultVo> signIns = this.signInData(weaverUserId, startDateStr, endDateStr);
        // 签退
        List<AttendResultVo> signOuts = this.signOutData(weaverUserId, startDateStr, endDateStr);
        for (String dateStr : dateDetail) {
            WeekDetailVo vo = new WeekDetailVo();
            DateTime date = DateUtil.parseDate(dateStr);
            vo.setDateTime(date);
            vo.setWeek(DateUtils.getNormalWeekCn(date));
            vo.setDate(DateUtil.format(date, "M-d"));
            vo.setDateStr(DateUtil.format(date,"M月d日"));
            vo.setDay(DateUtil.dayOfMonth(date));
            // 这天的开始和结束时间
            DateTime startDay = DateUtil.beginOfDay(date);
            DateTime endDay = DateUtil.endOfDay(date);
            // 上午下班时间和下午上班时间
            Date endMorning = DateUtils.morningEnd(dateStr);
            Date startAfternoon = DateUtils.afternoonStart(dateStr);
            // 获取请假时长
            BigDecimal vacationHour = VacationUtils.getVacationHour(startDay, endDay,endMorning,startAfternoon, vacations);
            // 是否请了7.5小时
            if (vacationHour.compareTo(PMSConst.WORKING_HOURS_OF_DAY)>=0){
                vo.setVacationHour("休假");
            }
            // 是否是休息日
            else if (restDays.contains(dateStr)){
                vo.setVacationHour("休假");
            }
            else {
                if (vacationHour.compareTo(BigDecimal.ZERO)>0){
                    vo.setVacationHour("休假 "+vacationHour.toPlainString()+"H");
                }

                Date signInTime = null;
                Date signOutTime = null;
                // 上班打卡数据
                AttendResultVo signIn = signIns.stream()
                        .filter(item->item.getDate().equals(dateStr))
                        .filter(item->SIGN_IN_NORMAL.equals(item.getSignStatus()))
                        .min(Comparator.comparing(AttendResultVo::getSignTime))
                        .orElse(null);
                if (null != signIn) {
                    signInTime = DateUtil.parseDateTime(signIn.getSignTime());
                    vo.setAttendStartTime(DateUtil.format(signInTime, "HH:mm"));
                    vo.setSignInTime(DateUtil.format(signInTime,"yyyy-MM-dd HH:mm"));
                }else {
                    vo.setAttendStartTime("??");
                    vo.setSignInTime("未打卡");
                }
                // 下班打卡数据
                AttendResultVo signOut = signOuts.stream()
                        .filter(item->item.getDate().equals(dateStr))
                        .filter(item->SIGN_OUT_NORMAL.equals(item.getSignStatus()))
                        .max(Comparator.comparing(AttendResultVo::getSignTime))
                        .orElse(null);
                if (null != signOut){
                    signOutTime = DateUtil.parseDateTime(signOut.getSignTime());
                    vo.setAttendEndTime(DateUtil.format(signOutTime, "HH:mm"));
                    vo.setSignOutTime(DateUtil.format(signOutTime,"yyyy-MM-dd HH:mm"));
                }else {
                    vo.setAttendEndTime("??");
                    vo.setSignOutTime("未打卡");
                }
                if (null!=signInTime&&null!=signOutTime){
                    long minute = DateUtil.between(signInTime, signOutTime, DateUnit.MINUTE);
                    // 是否横跨午休
                    if (DateUtil.compare(signInTime,endMorning)<=0&&DateUtil.compare(signOutTime,startAfternoon)>=0){
                        minute-=PMSConst.NOON_REST_MINUTES;
                    }
                    vo.setHour(BigDecimal.valueOf(minute)
                            .divide(PMSConst.MINUTES_OF_HOUR,1,RoundingMode.HALF_UP)
                            .toPlainString()+"H");
                    vo.setHourStr(DateUtils.toHm(minute));
                }else  {
                    vo.setHour("?H");
                    vo.setHourStr("-");
                }
            }
            details.add(vo);
        }
        return details;
    }

    @Override
    public List<WeekDetailVo> appDetail(String yearNumber, Integer weekNumber) {
        List<WeekDetailVo> details = this.detail(yearNumber, weekNumber);
        if (CollectionUtil.isEmpty(details)) return List.of();
        // 数据状态处理
        Long userId = SecurityUtils.getUserId();
        // 这周被驳回或审核中的工时数据
        List<WorkHourDetail> hourDetails = workHourDetailMapper.selectRejectAndWaitApproval(yearNumber, weekNumber, userId);
        details.forEach(item->{
            item.setDataStatus(1);
            List<WorkHourDetail> thisDayHourDetails = hourDetails.stream()
                    .filter(hd -> DateUtil.compare(item.getDateTime(), hd.getFillDate()) == 0)
                    .toList();
            if (thisDayHourDetails.stream()
                    .anyMatch(hd->hd.getStatus().equals(WorkHourStatusEnum.REJECTED.getCode()))
            ){
                item.setDataStatus(WorkHourStatusEnum.REJECTED.getCode());
            }
            else if (thisDayHourDetails.stream()
                    .anyMatch(hd->hd.getStatus().equals(WorkHourStatusEnum.WAIT_APPROVAL.getCode()))
            ){
                item.setDataStatus(WorkHourStatusEnum.WAIT_APPROVAL.getCode());
            }
        });
        return details;
    }

    /**
     * @param startDate
     * @param endDate
     * @Description: 获取休息日
     * @return: List<ApiCalendar>
     * @author: 王金都
     * @Date: 2025/6/16 17:53
     */
    private Set<String> restDays(Date startDate, Date endDate) {
        R<List<ApiCalendar>> listR = calendarApi.list(DateUtil.formatDate(startDate), DateUtil.formatDate(endDate), SecurityConstants.INNER);
        if (R.isError(listR)) {
            return new  HashSet<>();
        }
        return listR.getData().stream().filter(item->1==item.getDateType())
                .map(item->DateUtil.formatDate(item.getDate())).collect(Collectors.toSet());
    }


    /**
     * @Description: 获取签到数据
     * @param weaverUserId
     * @param startDate
     * @param endDate
     * @return: List<AttendResultVo>
     * @author: 王金都
     * @Date: 2025/6/16 11:00
     */
    private List<AttendResultVo> signInData(String weaverUserId,String startDate,String endDate) {
        if (StrUtil.isBlank(weaverUserId)) {
            log.warn("用户OAid为空");
            return List.of();
        }
        AttendVo vo = new AttendVo();
        vo.setUserid(Long.valueOf(weaverUserId));
        vo.setBeginDate(startDate);
        vo.setEndDate(endDate);
        vo.setEmpIds(List.of(Long.valueOf(weaverUserId)));
        R<List<AttendResultVo>> listR = attendApi.getSignInData(vo, SecurityConstants.INNER);
        if(R.isError(listR)){
            log.warn("获取企微用户【{}】签到数据失败",weaverUserId);
            return Collections.emptyList();
        }
        return Optional.ofNullable(listR.getData()).orElse(List.of());
    }

    /**
     * @Description: 获取签退数据
     * @param weaverUserId
     * @param startDate
     * @param endDate
     * @return: List<AttendResultVo>
     * @author: 王金都
     * @Date: 2025/6/16 14:59
     */
    private List<AttendResultVo> signOutData(String weaverUserId,String startDate,String endDate) {
        if (StrUtil.isBlank(weaverUserId)) {
            log.warn("用户OAid为空");
            return List.of();
        }
        AttendVo vo = new AttendVo();
        vo.setUserid(Long.valueOf(weaverUserId));
        vo.setBeginDate(startDate);
        vo.setEndDate(endDate);
        vo.setEmpIds(List.of(Long.valueOf(weaverUserId)));
        R<List<AttendResultVo>> listR = attendApi.getSignOutData(vo, SecurityConstants.INNER);
        if(R.isError(listR)){
            log.warn("获取企微用户【{}】签退数据失败",weaverUserId);
            return Collections.emptyList();
        }
        return Optional.ofNullable(listR.getData()).orElse(List.of());
    }





    /**
     * @Description: 获取请假数据
     * @param userId
     * @param startDate
     * @param endDate
     * @return: List<ApiVacation>
     * @author: 王金都
     * @Date: 2025/6/16 10:45
     */
    private List<ApiVacation>  vacationList(Long userId,Date startDate,Date endDate){
        R<List<ApiVacation>> listR = vacationApi.listUserVacation(userId, DateUtil.formatDate(startDate), DateUtil.formatDate(endDate), SecurityConstants.INNER);
        if (R.isError(listR)) {
            throw new ServiceException("获取用户请假数据失败");
        }
        return listR.getData();
    }

    @Override
    public Integer getEndWeekNumberByYear(Integer yearNumber) {
        if (yearNumber == null) {
            throw new ServiceException("年不能为空");
        }
        LambdaQueryWrapper<WeekOfYear> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(WeekOfYear::getYearNumber, yearNumber.toString())
                .orderByDesc(WeekOfYear::getWeekNumber)
                .last(SqlConstants.LIMIT_1);
        WeekOfYear one = this.getOne(wrapper, false);
        return Optional.ofNullable(one).map(WeekOfYear::getWeekNumber).orElse(null);
    }

    /**
     * @param yearNumber
     * @param weekNumber
     * @Description: 根据年周获取周数据
     * @return: WeekOfYear
     * @author: 王金都
     * @Date: 2025/6/9 10:59
     */
    private WeekOfYear getByYearNumberByWeekNumber(String yearNumber, Integer weekNumber) {
        LambdaQueryWrapper<WeekOfYear> query = new LambdaQueryWrapper<>();
        query.eq(WeekOfYear::getYearNumber, yearNumber);
        query.eq(WeekOfYear::getWeekNumber, weekNumber);
        return this.getOne(query, false);
    }

    /**
     * @Description: 获取这一年中这一天和之前天的周数据
     * @param yearNumber
     * @param date
     * @return: List<WeekOfYear>
     * @author: 王金都
     * @Date: 2025/6/9 11:24
     */
    private List<WeekOfYear> listYearWeeks(String yearNumber, Date date) {
        LambdaQueryWrapper<WeekOfYear> query = new LambdaQueryWrapper<>();
        query.eq(WeekOfYear::getYearNumber, yearNumber);
        query.le(WeekOfYear::getStartDate, date);
        query.orderByDesc(WeekOfYear::getWeekNumber);
        return list(query);
    }

    /**
     * @Description: 根据时间获取周信息
     * @param date
     * @return: WeekOfYear
     * @author: 王金都
     * @Date: 2025/6/9 10:42
     */
    private WeekOfYear getByDate(Date date) {
        LambdaQueryWrapper<WeekOfYear> query = new LambdaQueryWrapper<>();
        query.ge(WeekOfYear::getEndDate, date);
        query.le(WeekOfYear::getStartDate, date);
        return this.getOne(query, false);
    }

    /**
     * @Description: 根据年删除数据
     * @param year
     * @return: void
     * @author: 王金都
     * @Date: 2025/6/6 15:52
     */
    private void deleteByYear(String year) {
        LambdaQueryWrapper<WeekOfYear> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(WeekOfYear::getYearNumber, year);
        this.remove(wrapper);
    }

    /**
     * @Description: 创建周数据
     * @param weekList
     * @param year
     * @param weekNum
     * @param weekStart
     * @param weekEnd
     * @return: void
     * @author: 王金都
     * @Date: 2025/6/6 15:46
     */
    private void createWeekData(List<WeekOfYear> weekList, String year, int weekNum,
                                DateTime weekStart, DateTime weekEnd) {
        WeekOfYear weekOfYear = new WeekOfYear();
        weekOfYear.setYearNumber(year);
        weekOfYear.setWeekNumber(weekNum);
        weekOfYear.setStartDate(weekStart);
        weekOfYear.setEndDate(weekEnd);
        // 生成周内每天的日期详情
        List<String> dateDetails = new ArrayList<>();
        DateTime currentDay = DateUtil.date(weekStart);
        // 计算这周有多少天（包含首尾两天）
        int daysBetween = Math.toIntExact(DateUtil.betweenDay(weekStart, weekEnd, true));
        for (int i = 0; i <= daysBetween; i++) {
            dateDetails.add(DateUtil.format(currentDay, "yyyy-MM-dd"));
            currentDay = DateUtil.offsetDay(currentDay, 1);
        }
        weekOfYear.setDateDetail(dateDetails);
        weekList.add(weekOfYear);
    }

}
