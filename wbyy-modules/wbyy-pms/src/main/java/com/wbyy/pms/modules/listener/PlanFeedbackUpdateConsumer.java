package com.wbyy.pms.modules.listener;

import com.rabbitmq.client.Channel;
import com.wbyy.pms.modules.project.planfeedback.service.IProjectPlanFeedbackService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;

import static com.wbyy.pms.common.constant.RabbitmqConst.PLAN_FEEDBACK_UPDATE_QUEUE;

/**
 * 被关联计划反馈后，所有关联计划同步反馈
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class PlanFeedbackUpdateConsumer {

    private final IProjectPlanFeedbackService projectPlanFeedbackService;

    @RabbitListener(queues = PLAN_FEEDBACK_UPDATE_QUEUE)
    public void consume(Message message, String msg, Channel channel) {
        log.info("队列：{}, 消息: {} -- {}", PLAN_FEEDBACK_UPDATE_QUEUE, message.toString(), msg);
        projectPlanFeedbackService.relatedPlanFeedback(msg);
    }
}
