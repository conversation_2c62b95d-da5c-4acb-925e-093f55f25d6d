package com.wbyy.pms.modules.project.overview.service.impl;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson2.JSONObject;
import com.wbyy.pms.common.constant.ProjectConst;
import com.wbyy.pms.modules.project.overview.service.IProjectOverviewService;
import com.wbyy.pms.common.utils.ProjectUtil;
import com.wbyy.pms.common.utils.vo.ProjectCycleVo;
import com.wbyy.pms.common.utils.vo.ProjectMilestoneVo;
import com.wbyy.pms.common.utils.vo.ProjectPlanFinishVo;
import com.wbyy.pms.common.utils.vo.ProjectProgressVo;
import com.wbyy.pms.modules.project.plan.domain.ProjectPlanRelease;
import com.wbyy.pms.modules.project.plan.service.IProjectPlanReleaseService;
import com.wbyy.pms.modules.project.planfeedback.domain.ProjectPlanFeedback;
import com.wbyy.pms.modules.project.planfeedback.service.IProjectPlanFeedbackService;
import com.wbyy.system.api.CalendarApi;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @author: 王金都
 * @date: 2025/5/16 10:12
 */
@Service
@Slf4j
@AllArgsConstructor
public class ProjectOverviewServiceImpl implements IProjectOverviewService {

    private final IProjectPlanReleaseService projectPlanReleaseService;
    private final CalendarApi calendarApi;
    private final IProjectPlanFeedbackService projectPlanFeedbackService;


    @Override
    public ProjectProgressVo getProgress(Long projectId) {
        List<ProjectPlanRelease> projectPlans = projectPlanList(projectId);
        return getProgress(projectPlans);
    }

    private ProjectProgressVo getProgress(List<ProjectPlanRelease> projectPlans) {
        return ProjectUtil.getProgress(projectPlans, true, true);
    }

    @Override
    public ProjectCycleVo getCycle(Long projectId) {
        List<ProjectPlanRelease> projectPlans = projectPlanList(projectId);
        return getCycle(projectPlans);
    }

    private List<ProjectPlanRelease> projectPlanList(Long projectId) {
        List<ProjectPlanRelease> projectPlans = projectPlanReleaseService.listByProjectIds(List.of(projectId));
         return projectPlans.stream().peek(item->{
            if (null!=item.getPlanStartDate()) item.setPlanStartDate(DateUtil.beginOfDay(item.getPlanStartDate()));
            if (null!=item.getPlanStartDate()) item.setPlanStartDate(DateUtil.beginOfDay(item.getPlanStartDate()));
        }).toList();
    }

    private ProjectCycleVo getCycle(List<ProjectPlanRelease> projectPlans) {
        // 最外层任务
        List<ProjectPlanRelease> parentPlans = ProjectUtil.getParentPlans(projectPlans);
        // 最小开始时间
        Date minStartDate = ProjectUtil.getPlanStartDate(parentPlans);
        // 最大结束时间
        Date maxEndDate = ProjectUtil.getPlanEndDate(parentPlans);

        return ProjectUtil.getCycle(projectPlans, minStartDate, maxEndDate);
    }

    @Override
    public ProjectPlanFinishVo getPlanFinish(Long projectId) {
        List<ProjectPlanRelease> projectPlans = projectPlanList(projectId);
        return getPlanFinish(projectPlans);
    }

    private ProjectPlanFinishVo getPlanFinish(List<ProjectPlanRelease> projectPlans) {
        // 取叶子任务
        List<ProjectPlanRelease> leafPlans = projectPlans.stream()
                .filter(item -> ProjectConst.LEAF.equals(item.getLeafFlag()))
                .toList();
        return ProjectUtil.projectFinish(leafPlans);
    }

    public List<ProjectMilestoneVo> getMilestone(Long projectId){
        List<ProjectPlanRelease> projectPlans = projectPlanList(projectId);
        return this.getMilestone(projectPlans,projectId);
    }

    private List<ProjectMilestoneVo> getMilestone(List<ProjectPlanRelease> projectPlans,Long projectId){
        // 里程碑
        List<ProjectPlanRelease> milestones = projectPlans.stream().filter(item -> ProjectConst.MILESTONE == item.getMilestoneFlag())
                .toList();
        // 最新任务反馈
        List<ProjectPlanFeedback> feedbacks = projectPlanFeedbackService.listCurrentByProjectIds(List.of(projectId));
        List<Long> planIds = milestones.stream().map(ProjectPlanRelease::getId).toList();
        // 里程碑反馈
        List<ProjectPlanFeedback> milestoneFeedbacks = feedbacks.stream().filter(item -> planIds.contains(item.getProjectPlanId()))
                .toList();
        List<ProjectMilestoneVo> result = new ArrayList<>();
        for (ProjectPlanRelease milestone : milestones) {
            ProjectMilestoneVo vo = new ProjectMilestoneVo();
            vo.setPlanEndDate(milestone.getPlanEndDate());
            vo.setRealityEndDate(milestone.getRealityEndDate());
            vo.setPlanName(milestone.getName());
            // 里程碑的反馈
            ProjectPlanFeedback findFeedBack = milestoneFeedbacks.stream()
                    .filter(item -> item.getProjectPlanId().equals(milestone.getId()))
                    .findFirst().orElse(null);
            // 是否延期
            boolean isDelay = false;
            Date lastDate = new Date();
            if (null!=milestone.getRealityEndDate()){
                lastDate = milestone.getRealityEndDate();
            }
            isDelay = DateUtil.compare(milestone.getPlanEndDate(),lastDate)<0;
            // 有反馈
            if (null!=findFeedBack){
                // 已完成
                if (ProjectConst.PROGRESS_100.equals(findFeedBack.getRealityProgress())){
                    vo.setStatus(isDelay?ProjectConst.DELAY_FINISH:ProjectConst.NORMAL_FINISH);
                }
                // 未完成
                else {
                    vo.setStatus(isDelay?ProjectConst.DELAY_GOING:ProjectConst.NORMAL_GOING);
                }
            }
            // 没有反馈
            else {
                vo.setStatus(isDelay?ProjectConst.DELAY_GOING:ProjectConst.NORMAL_GOING);
            }
            result.add(vo);
        }
        return result;
    }

    public JSONObject overview(Long projectId){
        JSONObject result = new JSONObject();
        // 项目周期
        ProjectCycleVo cycle = this.getCycle(projectId);
        result.put("cycle",cycle);
        // 项目进度
        ProjectProgressVo progress = this.getProgress(projectId);
        result.put("progress",progress);
        // 项目里程碑
        List<ProjectMilestoneVo> milestone = this.getMilestone(projectId);
        result.put("milestone",milestone);
        // 任务完成情况
        ProjectPlanFinishVo planFinish = this.getPlanFinish(projectId);
        result.put("planFinish",planFinish);
        return result;
    }
}
