package com.wbyy.pms.modules.project.planuser.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wbyy.pms.modules.project.planuser.domain.ProjectPlanVersionUser;
import com.wbyy.pms.modules.project.planuser.mapper.ProjectPlanVersionUserMapper;
import com.wbyy.pms.modules.project.planuser.service.IProjectPlanVersionUserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 项目计划责任人-版本Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-13
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ProjectPlanVersionUserServiceImpl extends ServiceImpl<ProjectPlanVersionUserMapper, ProjectPlanVersionUser> implements IProjectPlanVersionUserService {
    private final ProjectPlanVersionUserMapper projectPlanVersionUserMapper;

    /**
     * 查询项目计划责任人-版本
     *
     * @param id 项目计划责任人-版本主键
     * @return 项目计划责任人-版本
     */
    @Override
    public ProjectPlanVersionUser selectById(Long id) {
        return this.getById(id);
    }

    /**
     * 查询项目计划责任人-版本列表
     *
     * @param projectPlanVersionUser 项目计划责任人-版本
     * @return 项目计划责任人-版本
     */
    @Override
    public List<ProjectPlanVersionUser> selectList(ProjectPlanVersionUser projectPlanVersionUser) {
        LambdaQueryWrapper<ProjectPlanVersionUser> wrapper = Wrappers.lambdaQuery();
        wrapper.orderByDesc(ProjectPlanVersionUser::getCreateTime);
        return this.list(wrapper);
    }

    /**
     * 新增项目计划责任人-版本
     *
     * @param projectPlanVersionUser 项目计划责任人-版本
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean insert(ProjectPlanVersionUser projectPlanVersionUser) {
        return this.save(projectPlanVersionUser);
    }

    /**
     * 修改项目计划责任人-版本
     *
     * @param projectPlanVersionUser 项目计划责任人-版本
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean update(ProjectPlanVersionUser projectPlanVersionUser) {
        return this.updateById(projectPlanVersionUser);
    }

    /**
     * 批量删除项目计划责任人-版本
     *
     * @param ids 需要删除的项目计划责任人-版本主键
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByIds(Long[] ids) {
        return this.removeBatchByIds(Arrays.asList(ids));
    }


    @Override
    public Map<Long, String> findMapByProjectIdAndProjectPlanIds(Long projectId, Integer version, List<Long> projectPlanIds) {
        List<ProjectPlanVersionUser> list = this.findByProjectIdAndProjectPlanIds(projectId, Collections.singletonList(version), projectPlanIds);

        if (CollUtil.isNotEmpty(list)) {
            Map<Long, String> resMap = new HashMap<>(list.size());
            list.stream().collect(Collectors.groupingBy(ProjectPlanVersionUser::getProjectPlanId))
                    .forEach((key, value) -> {
                        List<String> userNames = new ArrayList<>();
                        value.forEach(user -> userNames.add(user.getRealName()));
                        resMap.put(key, CollUtil.join(userNames, ","));
                    });
            return resMap;
        }
        return Map.of();
    }

    @Override
    public List<ProjectPlanVersionUser> findByProjectIdAndProjectPlanIds(Long projectId, List<Integer> versionIds, List<Long> projectPlanIds) {
        if (CollUtil.isEmpty(projectPlanIds) || projectId == null) {
            return List.of();
        }
        LambdaQueryWrapper<ProjectPlanVersionUser> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(ProjectPlanVersionUser::getProjectId, projectId);
        wrapper.in(ProjectPlanVersionUser::getVersion, versionIds);
        wrapper.in(ProjectPlanVersionUser::getProjectPlanId, projectPlanIds);
        return this.list(wrapper);
    }
}
