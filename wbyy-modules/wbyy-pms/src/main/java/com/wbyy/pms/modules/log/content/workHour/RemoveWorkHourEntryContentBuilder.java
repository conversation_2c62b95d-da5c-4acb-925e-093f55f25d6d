package com.wbyy.pms.modules.log.content.workHour;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.wbyy.log.builder.IContentBuilder;
import com.wbyy.log.model.LogRecord;
import com.wbyy.pms.modules.workhour.entry.domain.WorkHourEntry;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/7/10 11:55
 */
@Slf4j
public class RemoveWorkHourEntryContentBuilder implements IContentBuilder<List<WorkHourEntry>> {
    @Override
    public String buildContent(LogRecord logRecord, List<WorkHourEntry> originalData, List<WorkHourEntry> newData) {
        if (CollectionUtil.isEmpty(originalData)) {
            log.warn("删除工时条目时，原始数据为空");
            return "";
        }
        String userName = "【" + logRecord.getUserName() + "(" + logRecord.getWorkNumber() + ")】";
        logRecord.setDataModule(originalData
                .stream().map(WorkHourEntry::getEntryName)
                .collect(Collectors.joining(",")));
        logRecord.setDataModuleId(originalData.stream()
                .map(item -> StrUtil.toStringOrNull(item.getPlanId()))
                .collect(Collectors.joining(",")));
        WorkHourEntry entry = originalData.get(0);
        return userName+"删除了【"+entry.getYearNumber()+"年"+entry.getWeekNumber()+"周】工时";
    }
}
