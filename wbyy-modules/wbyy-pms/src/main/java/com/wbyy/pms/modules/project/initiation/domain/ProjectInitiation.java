package com.wbyy.pms.modules.project.initiation.domain;

import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.wbyy.common.core.annotation.Excel;
import com.wbyy.orm.core.domain.BaseAuditEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serial;
import java.util.Date;

/**
 * 项目立项对象 project_initiation
 * 
 * <AUTHOR>
 * @date 2025-06-25
 */
@Data
@SuperBuilder
@NoArgsConstructor
@Accessors(chain = true)
@TableName(value = "project_initiation", autoResultMap = true)
@Schema(description = "项目立项实体类")
@EqualsAndHashCode(callSuper = true)
public class ProjectInitiation extends BaseAuditEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /** 主键id */
    @Schema(description = "主键id（新增：不传；修改：必传）")
    private Long id;

    /** OA工作流-id */
    @Excel(name = "OA工作流-id")
    @Schema(description = "OA工作流-id")
    private Long oaWorkflowId;

    @Schema(description = "OA表单数据-id")
    private Long oaFormDataId;

    /** OA工作流-编号 */
    @Excel(name = "OA工作流-编号")
    @Schema(description = "OA工作流-编号")
    private String oaWorkflowNumber;

    /** OA工作流-名称 */
    @Excel(name = "OA工作流-名称")
    @Schema(description = "OA工作流-名称")
    private String oaWorkflowName;

    /** OA工作流-立项项目-项目经理 */
    @Excel(name = "OA工作流-立项项目-项目经理")
    @Schema(description = "OA工作流-立项项目-项目经理")
    private String oaWorkflowPmName;

    /** 项目经理-用户ID */
    @Excel(name = "项目经理-用户ID")
    @Schema(description = "项目经理-用户ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "项目经理ID不能为空")
    private Long pmUserId;

    /** OA工作流-立项项目-创建时间 */
    @Excel(name = "OA工作流-立项项目-创建时间")
    @Schema(description = "OA工作流-立项项目-创建时间")
    private Date oaWorkflowCreateTime;

    /** 删除标志（0代表存在 1代表删除） */
    @Schema(description = "删除标志（0代表存在 1代表删除）")
    @TableLogic
    private Integer delFlag;


    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("oaWorkflowId", getOaWorkflowId())
            .append("oaWorkflowNumber", getOaWorkflowNumber())
            .append("oaWorkflowName", getOaWorkflowName())
            .append("oaWorkflowPmName", getOaWorkflowPmName())
            .append("pmUserId", getPmUserId())
            .append("oaWorkflowCreateTime", getOaWorkflowCreateTime())
            .append("delFlag", getDelFlag())
            .append("createBy", getCreateBy())
            .append("createNameBy", getCreateNameBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateNameBy", getUpdateNameBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
