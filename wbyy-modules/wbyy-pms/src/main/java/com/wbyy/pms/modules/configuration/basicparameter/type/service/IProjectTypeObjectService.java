package com.wbyy.pms.modules.configuration.basicparameter.type.service;

import java.util.List;

import com.wbyy.pms.modules.configuration.basicparameter.type.domain.ProjectTypeObjectPO;

/**
 * 项目类型(业务类型)与对象关系Service接口
 *
 * <AUTHOR>
 * @date 2025-07-15
 */
public interface IProjectTypeObjectService {
    /**
     * 查询项目类型(业务类型)与对象关系
     *
     * @param id 项目类型(业务类型)与对象关系主键
     * @return 项目类型(业务类型)与对象关系
     */
    ProjectTypeObjectPO selectById(Long id);

    /**
     * 查询项目类型(业务类型)与对象关系列表
     *
     * @param po 项目类型(业务类型)与对象关系
     * @return 项目类型(业务类型)与对象关系集合
     */
    List<ProjectTypeObjectPO> selectList(ProjectTypeObjectPO po);

    /**
     * 新增项目类型(业务类型)与对象关系
     *
     * @param po 项目类型(业务类型)与对象关系
     * @return 结果
     */
    boolean insert(ProjectTypeObjectPO po);

    /**
     * 修改项目类型(业务类型)与对象关系
     *
     * @param po 项目类型(业务类型)与对象关系
     * @return 结果
     */
    boolean update(ProjectTypeObjectPO po);

    /**
     * 批量删除项目类型(业务类型)与对象关系
     *
     * @param ids 需要删除的项目类型(业务类型)与对象关系主键集合
     * @return 结果
     */
    boolean deleteByIds(Long[] ids);

    /**
     * 绑定项目业务类型与对象
     *
     * @param po
     * @return void
     * <AUTHOR>
     * @date 2025/7/15 11:10
     */
    void binding(ProjectTypeObjectPO po);

}
