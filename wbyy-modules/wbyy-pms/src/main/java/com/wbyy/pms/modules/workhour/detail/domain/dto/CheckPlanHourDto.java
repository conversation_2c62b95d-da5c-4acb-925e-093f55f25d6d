package com.wbyy.pms.modules.workhour.detail.domain.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @author: 王金都
 * @date: 2025/6/5 15:18
 */
@Data
@Schema(description = "校验工时是否满")
public class CheckPlanHourDto {

    @Schema(description = "任务id")
    @NotNull(message = "任务id不得为空")
    private Long planId;

    @Schema(description = "填写的工时")
    @NotNull(message = "填写的工时不得为空")
    private BigDecimal workHour;
}
