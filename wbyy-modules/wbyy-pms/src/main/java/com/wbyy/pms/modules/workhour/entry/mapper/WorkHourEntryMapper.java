package com.wbyy.pms.modules.workhour.entry.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wbyy.pms.modules.workhour.entry.domain.WorkHourEntry;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 工时条目Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-05
 */
public interface WorkHourEntryMapper extends BaseMapper<WorkHourEntry> {
    List<Long> idsByUserIdByYearAndWeek(@Param("userId") Long userId,@Param("yearNumber") String yearNumber,@Param("weekNumber") Integer weekNumber);
}
