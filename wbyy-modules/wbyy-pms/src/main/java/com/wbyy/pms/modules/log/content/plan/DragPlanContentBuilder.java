package com.wbyy.pms.modules.log.content.plan;

import com.wbyy.log.builder.IContentBuilder;
import com.wbyy.log.model.LogRecord;
import com.wbyy.pms.modules.project.plan.domain.ProjectPlanSnapshot;
import lombok.extern.slf4j.Slf4j;

/**
 * @author: 王金都
 * @date: 2025/7/8 14:35
 */
@Slf4j
public class DragPlanContentBuilder implements IContentBuilder<ProjectPlanSnapshot> {
    @Override
    public String buildContent(LogRecord logRecord, ProjectPlanSnapshot originalData, ProjectPlanSnapshot newData) {
        if (null==originalData){
            log.warn("任务调整生成日志时，原始数据为空");
            return "";
        }
        if (null==newData){
            log.warn("任务调整生成日志时，新数据为空");
            return "";
        }
        String userName = "【" + logRecord.getUserName() + "(" + logRecord.getWorkNumber() + ")】";
        return userName+"将任务WBS由【"+originalData.getWbs()+"】变更为【"+newData.getWbs()+"】";
    }
}
