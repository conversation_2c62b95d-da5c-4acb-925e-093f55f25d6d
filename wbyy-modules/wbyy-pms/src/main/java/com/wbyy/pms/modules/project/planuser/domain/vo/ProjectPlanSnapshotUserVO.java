package com.wbyy.pms.modules.project.planuser.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
@Accessors(chain = true)
public class ProjectPlanSnapshotUserVO implements Serializable {
    @Serial
    private static final long serialVersionUID = -4254714535442361511L;

    @Schema(description = "责任人id")
    private Long id;

    @Schema(description = "关联项目id")
    private Long projectId;

    @Schema(description = "关联计划id")
    private Long projectPlanId;

    @Schema(description = "责任人id")
    private Long userId;

    @Schema(description = "责任人姓名（查询用）")
    private String userName;

    @Schema(description = "工时，默认取工期内的自然日天数")
    private BigDecimal workHour;

    @Schema(description = "计划工期（天）")
    private BigDecimal planConstructionPeriod;


    @Schema(description = "项目角色（已翻译）")
    private String projectRoleNames;

    @Schema(description = "项目角色（已翻译）")
    private List<ProjectRoleListVO> projectRoleList;

}
