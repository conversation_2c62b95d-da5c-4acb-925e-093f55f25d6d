package com.wbyy.pms.common.aspect;

import com.alibaba.fastjson2.JSON;
import com.wbyy.pms.modules.configuration.oacallbacklog.domain.OaCallbackLog;
import com.wbyy.pms.modules.configuration.oacallbacklog.service.IOaCallbackLogService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;


/**
 * <AUTHOR>
 * @date 2025/6/25 13:53
 */
@Component
@Aspect
@Slf4j
@AllArgsConstructor
public class OaCallbackLogAspect {

    private final IOaCallbackLogService oaCallbackLogService;

    @Around("execution(* com.wbyy.pms.modules.oacallback.strategy.impl.*.handle(..))")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        String className = joinPoint.getTarget().getClass().getSimpleName();
        log.debug("[OA回调]切面] 类名：{}.handle(). 开始执行...", className);
        long startTime = System.currentTimeMillis();

        Object[] args = joinPoint.getArgs();
        String params = null;
        if (args != null && args.length > 0) {
            params = JSON.toJSONString(args);
            log.debug("[OA回调]切面] 类名：{}.handle(). 入参：{}", className, JSON.toJSONString(args));
        }
        Object result = null;
        String errorMsg = null;
        try {
            result = joinPoint.proceed();
            log.debug("[OA回调]切面] 类名：{}.handle(). 执行完毕...", className);
        } catch (Throwable e) {
            errorMsg = e.getMessage();
            log.error("[OA回调]切面] 类名：{}.handle(). 执行异常... \n 入参：{}", className, params, e);
            throw e;
        } finally {
            try {
                long endTime = System.currentTimeMillis();
                log.debug("[OA回调]切面] 类名：{}.handle(). 执行完毕，执行结果：{}, 耗时：{}ms", className, (result != null ? result : "没有结果返回"), endTime - startTime);
                oaCallbackLogService.save(OaCallbackLog.builder()
                        .className(className)
                        .methodParams(params)
                        .res(result != null ? JSON.toJSONString(result) : "没有结果返回")
                        .consumeTime(endTime - startTime)
                        .errorMsg(errorMsg)
                        .build());
            } catch (Exception e) {
                log.error("[OA回调]切面] 类名:{}, 保存OA回调日志失败", className, e);
            }
        }
        return result;
    }

}
