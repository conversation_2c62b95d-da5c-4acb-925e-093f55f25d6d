package com.wbyy.pms.modules.configuration.weekofyear.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wbyy.pms.modules.configuration.weekofyear.domain.WeekOfYear;
import com.wbyy.pms.modules.configuration.weekofyear.domain.vo.WeekDetailVo;

import java.util.List;

/**
 * 周年Service接口
 *
 * <AUTHOR>
 * @date 2025-06-05
 */
public interface IWeekOfYearService  extends IService<WeekOfYear> {
    /**
     * 查询周年
     *
     * @param id 周年主键
     * @return 周年
     */
    WeekOfYear selectById(Long id);

    /**
     * 查询周年列表
     *
     * @param weekOfYear 周年
     * @return 周年集合
     */
    List<WeekOfYear> selectList(WeekOfYear weekOfYear);

    /**
     * 新增周年
     *
     * @param weekOfYear 周年
     * @return 结果
     */
    boolean insert(WeekOfYear weekOfYear);

    /**
     * 修改周年
     *
     * @param weekOfYear 周年
     * @return 结果
     */
    boolean update(WeekOfYear weekOfYear);

    /**
     * 批量删除周年
     *
     * @param ids 需要删除的周年主键集合
     * @return 结果
     */
    boolean deleteByIds(Long[] ids);

    /**
     * @Description: 同步数据
     * @param
     * @return: void
     * @author: 王金都
     * @Date: 2025/6/6 15:20
     */
    void asyncData();

    /**
     * @Description: 按时间同步数据
     * @param year
     * @return: void
     * @author: 王金都
     * @Date: 2025/6/6 15:20
     */
    void asyncData(String year);

    /**
     * @Description: 根据年获取周列表，按当前周倒序到年第一周
     * @param yearNumber
     * @return: List<WeekOfYear>
     * @author: 王金都
     * @Date: 2025/6/9 10:32
     */
    List<WeekOfYear> list(String yearNumber);

    /**
     * @Description: 周详情
     * @param yearNumber
     * @param weekNumber
     * @return: List<WeekDetailVo>
     * @author: 王金都
     * @Date: 2025/6/9 10:58
     */
    List<WeekDetailVo> detail(String yearNumber,Integer weekNumber);

    /**
     * @Description: 移动端周详情
     * @param yearNumber
     * @param weekNumber
     * @return: List<WeekDetailVo>
     * @author: 王金都
     * @Date: 2025/6/17 10:20
     */
    List<WeekDetailVo> appDetail(String yearNumber,Integer weekNumber);

    /**
     * 跟年度年，查询最后一周
     * @param yearNumber 年份
     * @return 最后一周
     */
    Integer getEndWeekNumberByYear(Integer yearNumber);

}
