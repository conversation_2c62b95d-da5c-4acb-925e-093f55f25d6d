package com.wbyy.pms.common.converter;

import com.wbyy.pms.modules.project.planuser.domain.ProjectPlanReleaseUser;
import com.wbyy.pms.modules.project.planuser.domain.ProjectPlanSnapshotUser;
import com.wbyy.pms.modules.project.planuser.domain.ProjectPlanVersionUser;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface ProjectPlanUserConverter {
    ProjectPlanUserConverter INSTANCT = Mappers.getMapper(ProjectPlanUserConverter.class);

    ProjectPlanReleaseUser snapshot2Release(ProjectPlanSnapshotUser model);

    List<ProjectPlanReleaseUser> snapshot2Release(List<ProjectPlanSnapshotUser> model);

    ProjectPlanVersionUser snapshot2Version(ProjectPlanSnapshotUser model);

    List<ProjectPlanVersionUser> snapshot2Version(List<ProjectPlanSnapshotUser> model);
}
