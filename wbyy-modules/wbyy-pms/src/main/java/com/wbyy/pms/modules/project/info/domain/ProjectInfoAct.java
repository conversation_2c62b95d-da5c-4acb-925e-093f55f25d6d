package com.wbyy.pms.modules.project.info.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.wbyy.log.annotation.LogField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.wbyy.common.core.annotation.Excel;
import com.wbyy.orm.core.domain.BaseAuditEntity;

import java.io.Serial;

/**
 * 项目信息-分析测试中心对象 project_info_atc
 * 
 * <AUTHOR>
 * @date 2025-05-06
 */
@Data
@TableName("project_info_act")
@Schema(description = "项目信息-分析测试中心实体类")
@EqualsAndHashCode(callSuper = true)
public class ProjectInfoAct extends BaseAuditEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /** 主键，与项目基础信息表id关联 */
    @Schema(description = "主键，与项目基础信息表id关联（新增：不传；修改：必传）")
    private Long id;

    /** 客户 */
    @Excel(name = "客户")
    @Schema(description = "客户")
    @LogField(name = "客户")
    private String customer;

    /** 客户联系人和联系方式 */
    @Excel(name = "客户联系人和联系方式")
    @Schema(description = "客户联系人和联系方式")
    @LogField(name = "客户联系人和联系方式")
    private String customerContact;

    /** 试验类型（BE预试验；BE正式试验；药代动力学试验；I期；II期；III期；IV期；医疗器械；IIT研究；保健食品；特医食品） */
    @Excel(name = "试验类型", readConverterExp = "B=E预试验；BE正式试验；药代动力学试验；I期；II期；III期；IV期；医疗器械；IIT研究；保健食品；特医食品")
    @Schema(description = "试验类型（BE预试验；BE正式试验；药代动力学试验；I期；II期；III期；IV期；医疗器械；IIT研究；保健食品；特医食品）")
    @LogField(name = "试验类型")
    private String experimentType;

    /** 临床机构，默认安徽万邦医药科技股份有限公司 */
    @Excel(name = "临床机构，默认安徽万邦医药科技股份有限公司")
    @Schema(description = "临床机构，默认安徽万邦医药科技股份有限公司")
    @LogField(name = "临床机构")
    private String clinicalInstitution;

    /** 统计分析单位，安徽本奥医学科技有限公司 */
    @Excel(name = "统计分析单位，安徽本奥医学科技有限公司")
    @Schema(description = "统计分析单位，安徽本奥医学科技有限公司")
    @LogField(name = "统计分析单位")
    private String statisticalAnalysisUnit;

    /** 项目概况 */
    @Excel(name = "项目概况")
    @Schema(description = "项目概况")
    @LogField(name = "项目概况")
    private String remark;

    /** 删除标志（0代表存在 1代表删除） */
    @Schema(description = "删除标志（0代表存在 1代表删除）")
    @TableLogic
    private Integer delFlag;


    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("customer", getCustomer())
            .append("customerContact", getCustomerContact())
            .append("experimentType", getExperimentType())
            .append("clinicalInstitution", getClinicalInstitution())
            .append("statisticalAnalysisUnit", getStatisticalAnalysisUnit())
            .append("remark", getRemark())
            .append("delFlag", getDelFlag())
            .append("createBy", getCreateBy())
            .append("createNameBy", getCreateNameBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateNameBy", getUpdateNameBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
