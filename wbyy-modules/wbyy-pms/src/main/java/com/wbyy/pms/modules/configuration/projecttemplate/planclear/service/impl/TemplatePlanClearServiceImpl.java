package com.wbyy.pms.modules.configuration.projecttemplate.planclear.service.impl;

import java.util.List;
import java.util.Arrays;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import com.wbyy.pms.modules.configuration.projecttemplate.planclear.mapper.TemplatePlanClearMapper;
import com.wbyy.pms.modules.configuration.projecttemplate.planclear.domain.TemplatePlanClear;
import com.wbyy.pms.modules.configuration.projecttemplate.planclear.service.ITemplatePlanClearService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.transaction.annotation.Transactional;

/**
 * 项目模板计划清空记录Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-04-26
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TemplatePlanClearServiceImpl extends ServiceImpl<TemplatePlanClearMapper, TemplatePlanClear> implements ITemplatePlanClearService
{
    private final TemplatePlanClearMapper templatePlanClearMapper;

    /**
     * 查询项目模板计划清空记录
     * 
     * @param id 项目模板计划清空记录主键
     * @return 项目模板计划清空记录
     */
    @Override
    public TemplatePlanClear selectById(Long id)
    {
        return this.getById(id);
    }

    /**
     * 查询项目模板计划清空记录列表
     * 
     * @param templatePlanClear 项目模板计划清空记录
     * @return 项目模板计划清空记录
     */
    @Override
    public List<TemplatePlanClear> selectList(TemplatePlanClear templatePlanClear)
    {
        LambdaQueryWrapper<TemplatePlanClear> wrapper = Wrappers.lambdaQuery();
        wrapper.orderByDesc(TemplatePlanClear::getCreateTime);
        return this.list(wrapper);
    }

    /**
     * 新增项目模板计划清空记录
     * 
     * @param templatePlanClear 项目模板计划清空记录
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean insert(TemplatePlanClear templatePlanClear)
    {
        return this.save(templatePlanClear);
    }

    /**
     * 修改项目模板计划清空记录
     * 
     * @param templatePlanClear 项目模板计划清空记录
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean update(TemplatePlanClear templatePlanClear)
    {
        return this.updateById(templatePlanClear);
    }

    /**
     * 批量删除项目模板计划清空记录
     * 
     * @param ids 需要删除的项目模板计划清空记录主键
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByIds(Long[] ids)
    {
        return this.removeBatchByIds(Arrays.asList(ids));
    }

}
