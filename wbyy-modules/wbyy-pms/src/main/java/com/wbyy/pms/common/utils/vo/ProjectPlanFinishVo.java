package com.wbyy.pms.common.utils.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: 王金都
 * @date: 2025/5/16 15:51
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ProjectPlanFinishVo {

    @Schema(description = "正常完成数")
    private Integer normalFinishCount;

    @Schema(description = "正常完成百分比")
    private Integer normalFinishPercent;

    @Schema(description = "延误完成数")
    private Integer delayFinishCount;

    @Schema(description = "延误完成百分比")
    private Integer delayFinishPercent;

    @Schema(description = "正常进行数")
    private Integer normalConductCount;

    @Schema(description = "正常进行百分比")
    private Integer normalConductPercent;

    @Schema(description = "即将到期数")
    private Integer DueSoonCount;

    @Schema(description = "即将到期百分比")
    private Integer DueSoonPercent;

    @Schema(description = "已延误数")
    private Integer delayCount;

    @Schema(description = "已延误百分比")
    private Integer delayPercent;

    @Schema(description = "未开始计划任务数")
    private Integer notStartCount;

    @Schema(description = "未开始计划任务百分比")
    private Integer notStartPercent;

    public static ProjectPlanFinishVo getZero(){
        return new ProjectPlanFinishVo(0,0,0,0,0,0,0,0,0,0,0,0);
    }
}
