package com.wbyy.pms.common.utils;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import lombok.extern.slf4j.Slf4j;

import java.util.*;

/**
 * 循环依赖工具类
 *
 * <AUTHOR>
 * @date 2025/06/02
 */
@Slf4j
public class CircularDependencyUtils {

    /**
     * 判断是否存在循环依赖问题
     *
     * @param plans      任务计划
     * @param planFronts 前置任务关系
     * @param idKeyName  id字段名
     * @param pidKeyName 父id字段名
     * @return 是否存在循环依赖
     */
    public static boolean hasCircularDependency(JSONArray plans, JSONArray planFronts, String idKeyName, String pidKeyName) {
        if (CollUtil.isEmpty(plans) || CollUtil.isEmpty(planFronts)) {
            return false;
        }

        // 1. 构建任务层级结构
        Map<Long, List<Long>> childMap = buildChildMap(plans);

        // 2. 构建前置任务图（包括直接前置和层级影响）
        Map<Long, Set<Long>> graph = buildDependencyGraph(plans, planFronts, idKeyName, pidKeyName, childMap);
        log.debug("Dependency Graph:");
        printGraph(graph);

        // 3. 检查图中是否有环
        return hasCycleInGraph(graph);
    }

    /**
     * 构建子任务映射表(parentId -> children)
     */
    private static Map<Long, List<Long>> buildChildMap(JSONArray plans) {
        Map<Long, List<Long>> childMap = new HashMap<>();
        for (int i = 0; i < plans.size(); i++) {
            JSONObject plan = plans.getJSONObject(i);
            Long id = plan.getLong("id");
            Long parentId = plan.getLongValue("parentId", 0); // 默认parentId为0

            if (parentId != 0) { // 忽略顶层任务
                childMap.putIfAbsent(parentId, new ArrayList<>());
                childMap.get(parentId).add(id);
            }
        }
        return childMap;
    }

    /**
     * 构建完整的依赖图（包括直接前置和层级影响）
     */
    private static Map<Long, Set<Long>> buildDependencyGraph(JSONArray plans,
                                                             JSONArray planFronts,
                                                             String idKeyName,
                                                             String pidKeyName,
                                                             Map<Long, List<Long>> childMap) {
        Map<Long, Set<Long>> graph = new HashMap<>();

        // 1. 添加直接前置关系
        for (int i = 0; i < planFronts.size(); i++) {
            JSONObject front = planFronts.getJSONObject(i);
            if (!front.containsKey(idKeyName) || !front.containsKey(pidKeyName)) {
                continue;
            }

            Long source = front.getLong(idKeyName);  // 前置任务ID
            Long target = front.getLong(pidKeyName); // 被依赖的任务ID

            graph.computeIfAbsent(source, k -> new HashSet<>()).add(target);
        }

        // 2. 添加父子层级关系为依赖（父 → 子）
        for (int i = 0; i < plans.size(); i++) {
            JSONObject plan = plans.getJSONObject(i);
            long parentId = plan.getLongValue(pidKeyName, 0L); // 默认parentId为0
            Long id = plan.getLong(idKeyName);

            if (parentId != 0L) {
                graph.computeIfAbsent(parentId, k -> new HashSet<>()).add(id);
            }
        }

        // 3. 处理层级传播：子任务依赖父任务
        for (int i = 0; i < plans.size(); i++) {
            JSONObject plan = plans.getJSONObject(i);

            Long id = plan.getLong("id");  // 任务ID
            Long parentId = plan.getLong("parentId"); // 父任务ID
            if (parentId == 0L) continue;
            graph.computeIfAbsent(parentId, k -> new HashSet<>()).add(id);
        }

        return graph;
    }

    private static void printGraph(Map<Long, Set<Long>> graph) {
        for (Map.Entry<Long, Set<Long>> entry : graph.entrySet()) {
            log.info("Node {} depends on: {}", entry.getKey(), entry.getValue());
        }
    }

    /**
     * 检查图中是否有环
     */
    private static boolean hasCycleInGraph(Map<Long, Set<Long>> graph) {
        Set<Long> visited = new HashSet<>();
        Set<Long> recursionStack = new HashSet<>();

        for (Long node : graph.keySet()) {
            if (!visited.contains(node) &&
                    hasCycleDFS(graph, node, visited, recursionStack)) {
                log.error("循环依赖：{}", JSON.toJSONString(graph.get(node)));
                return true;
            }
        }

        return false;
    }

    /**
     * DFS检测环
     */
    private static boolean hasCycleDFS(Map<Long, Set<Long>> graph,
                                       Long node,
                                       Set<Long> visited,
                                       Set<Long> recursionStack) {
        if (recursionStack.contains(node)) {
            return true;
        }

        if (visited.contains(node)) {
            return false;
        }

        visited.add(node);
        recursionStack.add(node);

        if (graph.containsKey(node)) {
            for (Long neighbor : graph.get(node)) {
                if (hasCycleDFS(graph, neighbor, visited, recursionStack)) {
                    log.error("循环依赖1：{}", JSON.toJSONString(graph.get(node)));
                    return true;
                }
            }
        }

        recursionStack.remove(node);
        return false;
    }

}
