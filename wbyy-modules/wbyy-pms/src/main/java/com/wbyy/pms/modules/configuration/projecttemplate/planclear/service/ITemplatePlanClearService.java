package com.wbyy.pms.modules.configuration.projecttemplate.planclear.service;

import java.util.List;
import com.wbyy.pms.modules.configuration.projecttemplate.planclear.domain.TemplatePlanClear;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 项目模板计划清空记录Service接口
 *
 * <AUTHOR>
 * @date 2025-04-26
 */
public interface ITemplatePlanClearService  extends IService<TemplatePlanClear> {
    /**
     * 查询项目模板计划清空记录
     *
     * @param id 项目模板计划清空记录主键
     * @return 项目模板计划清空记录
     */
    TemplatePlanClear selectById(Long id);

    /**
     * 查询项目模板计划清空记录列表
     *
     * @param templatePlanClear 项目模板计划清空记录
     * @return 项目模板计划清空记录集合
     */
    List<TemplatePlanClear> selectList(TemplatePlanClear templatePlanClear);

    /**
     * 新增项目模板计划清空记录
     *
     * @param templatePlanClear 项目模板计划清空记录
     * @return 结果
     */
    boolean insert(TemplatePlanClear templatePlanClear);

    /**
     * 修改项目模板计划清空记录
     *
     * @param templatePlanClear 项目模板计划清空记录
     * @return 结果
     */
    boolean update(TemplatePlanClear templatePlanClear);

    /**
     * 批量删除项目模板计划清空记录
     *
     * @param ids 需要删除的项目模板计划清空记录主键集合
     * @return 结果
     */
    boolean deleteByIds(Long[] ids);

}
