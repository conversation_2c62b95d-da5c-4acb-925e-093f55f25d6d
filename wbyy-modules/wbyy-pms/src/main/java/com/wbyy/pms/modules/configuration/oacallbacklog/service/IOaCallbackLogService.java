package com.wbyy.pms.modules.configuration.oacallbacklog.service;

import java.util.List;
import com.wbyy.pms.modules.configuration.oacallbacklog.domain.OaCallbackLog;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * OA回调接口日志Service接口
 *
 * <AUTHOR>
 * @date 2025-06-25
 */
public interface IOaCallbackLogService  extends IService<OaCallbackLog> {
    /**
     * 查询OA回调接口日志
     *
     * @param id OA回调接口日志主键
     * @return OA回调接口日志
     */
    OaCallbackLog selectById(Long id);

    /**
     * 查询OA回调接口日志列表
     *
     * @param oaCallbackLog OA回调接口日志
     * @return OA回调接口日志集合
     */
    List<OaCallbackLog> selectList(OaCallbackLog oaCallbackLog);

    /**
     * 新增OA回调接口日志
     *
     * @param oaCallbackLog OA回调接口日志
     * @return 结果
     */
    boolean insert(OaCallbackLog oaCallbackLog);

    /**
     * 修改OA回调接口日志
     *
     * @param oaCallbackLog OA回调接口日志
     * @return 结果
     */
    boolean update(OaCallbackLog oaCallbackLog);

    /**
     * 批量删除OA回调接口日志
     *
     * @param ids 需要删除的OA回调接口日志主键集合
     * @return 结果
     */
    boolean deleteByIds(Long[] ids);

}
