package com.wbyy.pms.modules.log.content.plan;

import com.wbyy.log.builder.IContentBuilder;
import com.wbyy.log.model.LogRecord;
import com.wbyy.pms.modules.project.plan.domain.ProjectPlanSnapshot;
import lombok.extern.slf4j.Slf4j;

/**
 * @author: 王金都
 * @date: 2025/7/8 14:17
 */
@Slf4j
public class UpOrDownGradePlanContentBuilder implements IContentBuilder<ProjectPlanSnapshot> {
    @Override
    public String buildContent(LogRecord logRecord, ProjectPlanSnapshot originalData, ProjectPlanSnapshot newData) {
        if (null==originalData){
            log.warn("任务升/降级生成日志时，原始数据为空");
            return "";
        }
        if (null==newData){
            log.warn("任务升/降级生成日志时，新数据为空");
            return "";
        }
        String userName = "【" + logRecord.getUserName() + "(" + logRecord.getWorkNumber() + ")】";
        String up = newData.getUp()?"升级为":"降级为";
        return userName+"将任务WBS由【"+originalData.getWbs()+"】"+up+"【"+newData.getWbs()+"】";
    }
}
