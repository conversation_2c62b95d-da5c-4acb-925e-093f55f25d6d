package com.wbyy.pms.modules.project.planrelate.domain;

import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.wbyy.common.core.annotation.Excel;
import com.wbyy.orm.core.domain.BaseAuditEntity;

import java.io.Serial;

/**
 * 项目计划关联-版本对象 project_plan_version_relate
 * 
 * <AUTHOR>
 * @date 2025-05-13
 */
@Data
@SuperBuilder
@NoArgsConstructor
@Accessors(chain = true)
@TableName("project_plan_version_relate")
@Schema(description = "项目计划关联-版本实体类")
@EqualsAndHashCode(callSuper = true)
public class ProjectPlanVersionRelate extends BaseAuditEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /** 主键id */
    @Schema(description = "主键id（新增：不传；修改：必传）")
    private Long id;

    /** 项目id */
    @Excel(name = "项目id")
    @Schema(description = "项目id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "项目id不能为空")
    private Long projectId;

    /** 计划任务id */
    @Excel(name = "计划任务id")
    @Schema(description = "计划任务id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "计划任务id不能为空")
    private Long projectPlanId;

    /** 版本号 */
    @Excel(name = "版本号")
    @Schema(description = "版本号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "版本号不能为空")
    private Integer version;

    /** 关联计划任务的项目id */
    @Excel(name = "关联计划任务的项目id")
    @Schema(description = "关联计划任务的项目id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "关联计划任务的项目id不能为空")
    private Long projectRelateId;

    /** 关联计划任务的id */
    @Excel(name = "关联计划任务的id")
    @Schema(description = "关联计划任务的id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "关联计划任务的id不能为空")
    private Long projectPlanRelateId;

    /** 删除标志（0代表存在 1代表删除） */
    @Schema(description = "删除标志（0代表存在 1代表删除）", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "关联计划任务的id不能为空")
    @TableLogic
    private Integer delFlag;


    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("projectId", getProjectId())
            .append("projectPlanId", getProjectPlanId())
            .append("projectRelateId", getProjectRelateId())
            .append("projectPlanRelateId", getProjectPlanRelateId())
            .append("delFlag", getDelFlag())
            .append("createBy", getCreateBy())
            .append("createNameBy", getCreateNameBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateNameBy", getUpdateNameBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
