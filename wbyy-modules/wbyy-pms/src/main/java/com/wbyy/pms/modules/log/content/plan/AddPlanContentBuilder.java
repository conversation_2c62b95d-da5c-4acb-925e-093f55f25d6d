package com.wbyy.pms.modules.log.content.plan;

import com.wbyy.log.builder.IContentBuilder;
import com.wbyy.log.model.LogRecord;
import com.wbyy.pms.modules.project.plan.domain.ProjectPlanSnapshot;
import lombok.extern.slf4j.Slf4j;

/**
 * @author: 王金都
 * @date: 2025/7/7 14:53
 */
@Slf4j
public class AddPlanContentBuilder implements IContentBuilder<ProjectPlanSnapshot> {
    @Override
    public String buildContent(LogRecord logRecord, ProjectPlanSnapshot originalData, ProjectPlanSnapshot newData) {
        if (null==newData){
            log.warn("添加任务生成日志时，新数据为空");
            return "";
        }
        String userName = "【" + logRecord.getUserName() + "(" + logRecord.getWorkNumber() + ")】";
        return userName+"添加【"+newData.getName()+"】任务";
    }
}
