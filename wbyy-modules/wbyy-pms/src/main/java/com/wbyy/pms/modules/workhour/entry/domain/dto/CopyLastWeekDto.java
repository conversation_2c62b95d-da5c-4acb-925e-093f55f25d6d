package com.wbyy.pms.modules.workhour.entry.domain.dto;

import com.wbyy.pms.modules.workhour.entry.domain.WorkHourEntry;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

/**
 * @author: 王金都
 * @date: 2025/6/5 15:38
 */
@Data
@Schema(description = "复制上周工时条目/内容实体类")
public class CopyLastWeekDto {

    @Schema(description = "年（当前年）")
    @NotBlank(message = "年不得为空")
    private String yearNumber;

    private String lastYearNumber;

    @Schema(description = "周（当前周）")
    @NotNull(message = "周不得为空")
    private Integer weekNumber;

    private Integer lastWeekNumber;

    @Schema(description = "是否复制工时内容（默认false）")
    private Boolean copyDetail = Boolean.FALSE;

    private List<WorkHourEntry> entries;
}
