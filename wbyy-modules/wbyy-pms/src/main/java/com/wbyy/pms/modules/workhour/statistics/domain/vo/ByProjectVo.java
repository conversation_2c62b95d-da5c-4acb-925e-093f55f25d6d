package com.wbyy.pms.modules.workhour.statistics.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * @author: 王金都
 * @date: 2025/6/5 15:51
 */
@Data
@Schema(description = "按项目统计")
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ByProjectVo {
    @Schema(description = "项目id")
    private Long projectId;

    @Schema(description = "项目名称")
    private String projectName;

    @Schema(description = "项目编码")
    private String projectNumber;

    @Schema(description = "集团合同编号")
    private String projectGroupContractNumber;

    @Schema(description = "项目隶属部门")
    private String projectDeptName;

    @Schema(description = "项目隶属中心名称")
    private String projectDeptCenterName;

    @Schema(description = "完成任务总工时")
    private BigDecimal finishHour;

    @Schema(description = "填报总工时")
    private BigDecimal fillHour;

    @Schema(description = "填报人数")
    private Long fillCount;
}
