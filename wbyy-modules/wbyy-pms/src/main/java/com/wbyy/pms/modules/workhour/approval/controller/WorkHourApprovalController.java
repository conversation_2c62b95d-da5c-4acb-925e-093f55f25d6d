package com.wbyy.pms.modules.workhour.approval.controller;

import com.wbyy.pms.modules.workhour.approval.domain.dto.ApprovalDTO;
import com.wbyy.pms.modules.workhour.approval.domain.dto.PageApprovalDTO;
import com.wbyy.pms.modules.workhour.approval.domain.dto.PageApprovalProjectDTO;
import com.wbyy.pms.modules.workhour.approval.domain.vo.ApprovalProjectVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.wbyy.common.core.domain.R;
import com.wbyy.common.log.annotation.Log;
import com.wbyy.common.log.enums.BusinessType;
import com.wbyy.common.security.annotation.RequiresPermissions;
import com.wbyy.pms.modules.workhour.approval.domain.WorkHourApproval;
import com.wbyy.pms.modules.workhour.approval.service.IWorkHourApprovalService;
import com.wbyy.common.core.web.controller.BaseController;
import com.wbyy.common.core.web.page.TableDataInfo;

/**
 * 工时审核流程Controller
 * 
 * <AUTHOR>
 * @date 2025-06-05
 */
@RestController
@RequiredArgsConstructor
@Tag(name = "工时审核流程")
@RequestMapping("/work/hour/approval")
public class WorkHourApprovalController extends BaseController
{

    private final IWorkHourApprovalService workHourApprovalService;

    @Operation(summary = "分页列表")
    @RequiresPermissions("workhour:approval:list")
    @GetMapping("/page")
    public TableDataInfo<WorkHourApproval> page(@Validated @ParameterObject PageApprovalDTO dto)
    {
        List<WorkHourApproval> list = workHourApprovalService.selectList(dto);
        return getDataTable(list);
    }

//    @Operation(summary = "导出列表")
//    @RequiresPermissions("workhour:approval:export")
//    @Log(title = "工时审核流程", businessType = BusinessType.EXPORT)
//    @PostMapping("/export")
//    public void export(HttpServletResponse response, WorkHourApproval workHourApproval)
//    {
//        List<WorkHourApproval> list = workHourApprovalService.selectList(workHourApproval);
//        ExcelUtil<WorkHourApproval> util = new ExcelUtil<WorkHourApproval>(WorkHourApproval.class);
//        util.exportExcel(response, list, "工时审核流程数据");
//    }

    @Operation(summary = "新增")
    @RequiresPermissions("workhour:approval:add")
    @Log(title = "工时审核流程", businessType = BusinessType.INSERT)
    @PostMapping
    public R<Boolean> add(@RequestBody @Validated WorkHourApproval workHourApproval)
    {
        return R.ok(workHourApprovalService.insert(workHourApproval));
    }

    @Operation(summary = "审批")
    @RequiresPermissions("workhour:approval:add")
    @Log(title = "工时审核流程-审批", businessType = BusinessType.UPDATE)
    @PostMapping("approval")
    public R<Boolean> approval(@RequestBody @Validated ApprovalDTO dto)
    {
        workHourApprovalService.approval(dto);
        return R.ok(true);
    }

    @Operation(summary = "查询提交工时审核的项目,用作过滤条件")
    @RequiresPermissions("workhour:approval:list")
    @GetMapping("/approval-project/page")
    public TableDataInfo<ApprovalProjectVO> pageApprovalProject(@Validated @ParameterObject PageApprovalProjectDTO dto) {
        List<ApprovalProjectVO> list = workHourApprovalService.pageApprovalProject(dto);
        return getDataTable(list);
    }
}
