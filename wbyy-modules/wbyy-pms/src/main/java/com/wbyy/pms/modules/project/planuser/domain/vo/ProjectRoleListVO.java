package com.wbyy.pms.modules.project.planuser.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

@Data
public class ProjectRoleListVO implements Serializable {
    @Serial
    private static final long serialVersionUID = -8911277886063749574L;
    @Schema(description = "项目角色ID")
    private Long projectRoleId;
    @Schema(description = "项目角色名称")
    private String projectRoleName;
}
