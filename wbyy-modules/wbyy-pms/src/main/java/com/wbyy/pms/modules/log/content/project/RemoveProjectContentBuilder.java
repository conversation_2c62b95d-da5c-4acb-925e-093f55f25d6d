package com.wbyy.pms.modules.log.content.project;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.wbyy.log.builder.IContentBuilder;
import com.wbyy.log.model.LogRecord;
import com.wbyy.pms.modules.project.base.domain.ProjectBase;

import java.util.List;

/**
 * @author: 王金都
 * @date: 2025/7/4 16:37
 */
public class RemoveProjectContentBuilder implements IContentBuilder<List<ProjectBase>> {
    @Override
    public String buildContent(LogRecord logRecord, List<ProjectBase> originalData, List<ProjectBase> newData) {
        String userName = "【" + logRecord.getUserName() + "(" + logRecord.getWorkNumber() + ")】";
        StringBuilder content = new StringBuilder();
        if (CollectionUtil.isNotEmpty(newData)){
            ProjectBase data = newData.get(0);
            logRecord.setBizCode(data.getNumber());
            logRecord.setBizName(data.getName());
            logRecord.setBizId(data.getId().toString());
        }
        for (ProjectBase data : newData) {
            if (StrUtil.isNotBlank(content)){
                content.append("。");
            }
            content.append("将项目【")
                    .append(logRecord.getDataModule())
                    .append("】删除，删除原因：【")
                    .append(data.getDelReason())
                    .append("】");
        }
        if (StrUtil.isBlank(content)) return "";
        return userName+content;
    }
}
