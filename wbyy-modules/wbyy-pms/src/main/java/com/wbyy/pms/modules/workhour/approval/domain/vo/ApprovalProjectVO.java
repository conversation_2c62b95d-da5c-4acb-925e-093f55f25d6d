package com.wbyy.pms.modules.workhour.approval.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/6/5  15:16
 * @description 查询已提交审核的项目
 */
@Data
public class ApprovalProjectVO implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "项目id")
    private Long id;

    @Schema(description = "名称")
    private String name;

    @Schema(description = "编号")
    private String number;
}
