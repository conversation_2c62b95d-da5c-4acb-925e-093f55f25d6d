package com.wbyy.pms.modules.contract.nodeplan.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wbyy.pms.modules.contract.nodeplan.domain.ContractNodePlan;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 合同节点与任务关联Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-25
 */
public interface ContractNodePlanMapper extends BaseMapper<ContractNodePlan> {
    List<ContractNodePlan> selectPlanIdsByNodeId(@Param("contractNodeId")Long contractNodeId);

    List<ContractNodePlan> listByIds(@Param("ids") List<Long> ids);

    List<ContractNodePlan> selectContractNodeRelationByIds(@Param("ids") List<Long> ids);

    List<ContractNodePlan> selectByContractId(@Param("contractId") Long contractId);
}
