package com.wbyy.pms.modules.configuration.weekofyear.controller;

import com.wbyy.pms.modules.configuration.weekofyear.domain.vo.WeekDetailVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;

import java.util.List;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.wbyy.common.core.domain.R;
import com.wbyy.common.log.annotation.Log;
import com.wbyy.common.log.enums.BusinessType;
import com.wbyy.common.security.annotation.RequiresPermissions;
import com.wbyy.pms.modules.configuration.weekofyear.domain.WeekOfYear;
import com.wbyy.pms.modules.configuration.weekofyear.service.IWeekOfYearService;
import com.wbyy.common.core.web.controller.BaseController;
import com.wbyy.common.core.utils.poi.ExcelUtil;
import com.wbyy.common.core.web.page.TableDataInfo;

/**
 * 周年Controller
 * 
 * <AUTHOR>
 * @date 2025-06-05
 */
@RestController
@RequiredArgsConstructor
@Tag(name = "周年")
@RequestMapping("/week-of-year")
public class WeekOfYearController extends BaseController
{

    private final IWeekOfYearService weekOfYearService;

    @Operation(summary = "分页列表")
    @RequiresPermissions("weekofyear:weekofyear:list")
    @GetMapping("/page")
    public TableDataInfo<WeekOfYear> page(@ParameterObject WeekOfYear weekOfYear)
    {
        startPage();
        List<WeekOfYear> list = weekOfYearService.selectList(weekOfYear);
        return getDataTable(list);
    }

    @Operation(summary = "列表")
    @RequiresPermissions("weekofyear:weekofyear:list")
    @GetMapping("/list")
    public R<List<WeekOfYear>> list(@RequestParam("yearNumber") @NotBlank(message = "年不得为空") String yearNumber){
        return R.ok(weekOfYearService.list(yearNumber));
    }

    @Operation(summary = "周详情（表头）")
    @RequiresPermissions("weekofyear:weekofyear:list")
    @GetMapping("/detail")
    public R<List<WeekDetailVo>> detail(@RequestParam("yearNumber") @NotBlank(message = "年不得为空") String yearNumber,
                                        @RequestParam("weekNumber") @NotNull(message = "周不得为空")Integer weekNumber){
        return R.ok(weekOfYearService.detail(yearNumber,weekNumber));
    }

    @Operation(summary = "周详情（表头）-app")
    @RequiresPermissions("weekofyear:weekofyear:list")
    @GetMapping("/app-detail")
    public R<List<WeekDetailVo>> appDetail(@RequestParam("yearNumber") @NotBlank(message = "年不得为空") String yearNumber,
                                           @RequestParam("weekNumber") @NotNull(message = "周不得为空")Integer weekNumber){
        return R.ok(weekOfYearService.appDetail(yearNumber,weekNumber));
    }

    /**
     * @Description: 同步数据
     * @param year
     * @return: R<List<WeekDetailVo>>
     * @author: 王金都
     * @Date: 2025/6/23 10:35
     */
    @GetMapping("/async-data")
    public R<List<WeekDetailVo>> asyncData(@RequestParam("year")String year){
        weekOfYearService.asyncData(year);
        return R.ok();
    }

    @Operation(summary = "导出列表")
    @RequiresPermissions("weekofyear:weekofyear:export")
    @Log(title = "周年", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, WeekOfYear weekOfYear)
    {
        List<WeekOfYear> list = weekOfYearService.selectList(weekOfYear);
        ExcelUtil<WeekOfYear> util = new ExcelUtil<WeekOfYear>(WeekOfYear.class);
        util.exportExcel(response, list, "周年数据");
    }

    @Operation(summary = "获取详细信息")
    @RequiresPermissions("weekofyear:weekofyear:query")
    @GetMapping(value = "/{id}")
    public R<WeekOfYear> getInfo(@PathVariable("id") Long id)
    {
        return R.ok(weekOfYearService.selectById(id));
    }

    @Operation(summary = "新增")
    @RequiresPermissions("weekofyear:weekofyear:add")
    @Log(title = "周年", businessType = BusinessType.INSERT)
    @PostMapping
    public R<Boolean> add(@RequestBody @Validated WeekOfYear weekOfYear)
    {
        return R.ok(weekOfYearService.insert(weekOfYear));
    }

    @Operation(summary = "修改")
    @RequiresPermissions("weekofyear:weekofyear:edit")
    @Log(title = "周年", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<Boolean> edit(@RequestBody @Validated WeekOfYear weekOfYear)
    {
        return R.ok(weekOfYearService.update(weekOfYear));
    }

    @Operation(summary = "删除")
    @RequiresPermissions("weekofyear:weekofyear:remove")
    @Log(title = "周年", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public R<Boolean> remove(@PathVariable Long[] ids)
    {
        return R.ok(weekOfYearService.deleteByIds(ids));
    }
}
