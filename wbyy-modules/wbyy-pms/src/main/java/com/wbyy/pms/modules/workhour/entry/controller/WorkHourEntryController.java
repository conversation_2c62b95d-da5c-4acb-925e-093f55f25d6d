package com.wbyy.pms.modules.workhour.entry.controller;

import com.wbyy.pms.modules.workhour.detail.service.IWorkHourDetailService;
import com.wbyy.common.core.domain.R;
import com.wbyy.common.core.utils.poi.ExcelUtil;
import com.wbyy.common.core.web.controller.BaseController;
import com.wbyy.common.core.web.page.TableDataInfo;
import com.wbyy.common.log.annotation.Log;
import com.wbyy.common.log.enums.BusinessType;
import com.wbyy.common.security.annotation.RequiresPermissions;
import com.wbyy.pms.modules.workhour.entry.domain.WorkHourEntry;
import com.wbyy.pms.modules.workhour.entry.domain.dto.CopyLastWeekDto;
import com.wbyy.pms.modules.workhour.entry.service.IWorkHourEntryService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 工时条目Controller
 * 
 * <AUTHOR>
 * @date 2025-06-05
 */
@RestController
@RequiredArgsConstructor
@Tag(name = "工时条目")
@RequestMapping("/work-hour-entry")
public class WorkHourEntryController extends BaseController
{

    private final IWorkHourEntryService workHourEntryService;

    @Operation(summary = "分页列表")
    @RequiresPermissions("workhour:entry:list")
    @GetMapping("/page")
    public TableDataInfo<WorkHourEntry> page(@ParameterObject WorkHourEntry workHourEntry)
    {
        startPage();
        List<WorkHourEntry> list = workHourEntryService.selectList(workHourEntry);
        return getDataTable(list);
    }

    @Operation(summary = "用户周工时详情")
    @RequiresPermissions("workhour:entry:list")
    @GetMapping("/user-week-detail")
    public R<List<WorkHourEntry>> userWeekDetail(@RequestParam("yearNumber") @NotBlank(message = "年不得为空") String yearNumber,
                                          @RequestParam("weekNumber") @NotNull(message = "周不得为空")Integer weekNumber){
        return R.ok(workHourEntryService.userWeekDetail(yearNumber,weekNumber));
    }

    @Operation(summary = "批量新增工时条目")
    @RequiresPermissions("workhour:entry:add")
    @Log(title = "工时条目", businessType = BusinessType.INSERT)
    @PostMapping("/add-batch")
    public R<Boolean> addBatch(@RequestBody List<WorkHourEntry> dto){
        workHourEntryService.addBatch(dto);
        return R.ok(true);
    }

    @Operation(summary = "复制上周工时条目/内容")
    @RequiresPermissions("workhour:entry:add")
    @Log(title = "工时条目", businessType = BusinessType.INSERT)
    @PostMapping("/copy-last-week")
    public R<Boolean> copyLastWeek(@Validated @RequestBody CopyLastWeekDto dto){
        workHourEntryService.copyLastWeek(dto);
        return R.ok(true);
    }

    @Operation(summary = "导出列表")
    @RequiresPermissions("workhour:entry:export")
    @Log(title = "工时条目", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, WorkHourEntry workHourEntry)
    {
        List<WorkHourEntry> list = workHourEntryService.selectList(workHourEntry);
        ExcelUtil<WorkHourEntry> util = new ExcelUtil<WorkHourEntry>(WorkHourEntry.class);
        util.exportExcel(response, list, "工时条目数据");
    }

    @Operation(summary = "获取详细信息")
    @RequiresPermissions("workhour:entry:query")
    @GetMapping(value = "/{id}")
    public R<WorkHourEntry> getInfo(@PathVariable("id") Long id)
    {
        return R.ok(workHourEntryService.selectById(id));
    }

    @Operation(summary = "新增")
    @RequiresPermissions("workhour:entry:add")
    @Log(title = "工时条目", businessType = BusinessType.INSERT)
    @PostMapping
    public R<Boolean> add(@RequestBody @Validated WorkHourEntry workHourEntry)
    {
        return R.ok(workHourEntryService.insert(workHourEntry));
    }

    @Operation(summary = "修改")
    @RequiresPermissions("workhour:entry:edit")
    @Log(title = "工时条目", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<Boolean> edit(@RequestBody @Validated WorkHourEntry workHourEntry)
    {
        return R.ok(workHourEntryService.update(workHourEntry));
    }

    @Operation(summary = "删除")
    @RequiresPermissions("workhour:entry:remove")
    @Log(title = "工时条目", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public R<Boolean> remove(@PathVariable Long[] ids)
    {
        workHourEntryService.deleteByIds(Arrays.asList(ids));
        return R.ok(true);
    }
}
