package com.wbyy.pms.modules.workhour.entry.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.wbyy.common.core.annotation.Excel;
import com.wbyy.orm.core.domain.BaseAuditEntity;
import com.wbyy.pms.common.enums.WorkHourStatusEnum;
import com.wbyy.pms.modules.workhour.detail.domain.WorkHourDetail;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serial;

/**
 * 工时条目对象 work_hour_entry
 * 
 * <AUTHOR>
 * @date 2025-06-05
 */
@Data
@SuperBuilder
@NoArgsConstructor
@Accessors(chain = true)
@TableName("work_hour_entry")
@Schema(description = "工时条目实体类")
@EqualsAndHashCode(callSuper = true)
public class WorkHourEntry extends BaseAuditEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /** 主键id */
    @Schema(description = "主键id（新增：不传；修改：必传）")
    private Long id;

    /** 项目id(项目任务条目时不得为空，公共条目时为空) */
    @Excel(name = "项目id(项目任务条目时不得为空，公共条目时为空)")
    @Schema(description = "项目id(项目任务条目时不得为空，公共条目时为空)")
    private Long projectId;

    /** 项目名称(项目任务条目时不得为空，公共条目时为空) */
    @Excel(name = "项目名称(项目任务条目时不得为空，公共条目时为空)")
    @Schema(description = "项目名称(项目任务条目时不得为空，公共条目时为空)")
    @Size(max = 100, message = "项目名称(项目任务条目时不得为空，公共条目时为空)不能超过 100 个字符")
    private String projectName;

    /** 项目编码(项目任务条目时不得为空，公共条目时为空) */
    @Excel(name = "项目编码(项目任务条目时不得为空，公共条目时为空)")
    @Schema(description = "项目编码(项目任务条目时不得为空，公共条目时为空)")
    @Size(max = 100, message = "项目编码(项目任务条目时不得为空，公共条目时为空)不能超过 100 个字符")
    private String projectNumber;

    /** 任务id(项目任务条目时不得为空，公共条目时为空) */
    @Excel(name = "任务id(项目任务条目时不得为空，公共条目时为空)")
    @Schema(description = "任务id(项目任务条目时不得为空，公共条目时为空)")
    private Long planId;

    /** 条目名称(选择公共条目时，作为公共条目名称),集合，完整的任务从属路径,/拼接 */
    @Excel(name = "条目名称(选择公共条目时，作为公共条目名称),集合，完整的任务从属路径,/拼接")
    @Schema(description = "条目名称(选择公共条目时，作为公共条目名称),集合，完整的任务从属路径,/拼接", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "条目名称(选择公共条目时，作为公共条目名称),集合，完整的任务从属路径,/拼接不能为空")
    private String entryName;

    /** 所属年 */
    @Excel(name = "所属年")
    @Schema(description = "所属年", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "所属年不能为空")
    @Size(max = 4, message = "所属年不能超过 4 个字符")
    private String yearNumber;

    /** 所属周 */
    @Excel(name = "所属周")
    @Schema(description = "所属周", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "所属周不能为空")
    private Integer weekNumber;

    /** 用户名 */
    @Excel(name = "用户名")
    @Schema(description = "用户名")
    @Size(max = 30, message = "用户名不能超过 30 个字符")
    private String realName;

    /** 用户工号 */
    @Excel(name = "用户工号")
    @Schema(description = "用户工号")
    @Size(max = 16, message = "用户工号不能超过 16 个字符")
    private String workNumber;

    /** 删除标志（0代表存在 1代表删除） */
    @Schema(description = "删除标志（0代表存在 1代表删除）")
    @TableLogic
    private Integer delFlag;

    @TableField(exist = false)
    @Schema(description = "条目周工时详情")
    private WorkHourDetail day1 = new WorkHourDetail();
    @TableField(exist = false)
    @Schema(description = "条目周工时详情")
    private WorkHourDetail day2 = new WorkHourDetail();
    @TableField(exist = false)
    @Schema(description = "条目周工时详情")
    private WorkHourDetail day3 = new WorkHourDetail();
    @TableField(exist = false)
    @Schema(description = "条目周工时详情")
    private WorkHourDetail day4 = new WorkHourDetail();
    @TableField(exist = false)
    @Schema(description = "条目周工时详情")
    private WorkHourDetail day5 = new WorkHourDetail();
    @TableField(exist = false)
    @Schema(description = "条目周工时详情")
    private WorkHourDetail day6 = new WorkHourDetail();
    @TableField(exist = false)
    @Schema(description = "条目周工时详情")
    private WorkHourDetail day7 = new WorkHourDetail();

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("projectId", getProjectId())
            .append("projectName", getProjectName())
            .append("projectNumber", getProjectNumber())
            .append("planId", getPlanId())
            .append("entryName", getEntryName())
            .append("yearNumber", getYearNumber())
            .append("weekNumber", getWeekNumber())
            .append("realName", getRealName())
            .append("workNumber", getWorkNumber())
            .append("delFlag", getDelFlag())
            .append("createBy", getCreateBy())
            .append("createNameBy", getCreateNameBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateNameBy", getUpdateNameBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
    /**
     * @Description: 数据归档
     * @param
     * @return: void
     * @author: 王金都
     * @Date: 2025/6/11 10:07
     */
    public void archive(){
        if (null!=this.day1)this.day1.setStatus(WorkHourStatusEnum.ARCHIVE.getCode());
        if (null!=this.day2)this.day2.setStatus(WorkHourStatusEnum.ARCHIVE.getCode());
        if (null!=this.day3)this.day3.setStatus(WorkHourStatusEnum.ARCHIVE.getCode());
        if (null!=this.day4)this.day4.setStatus(WorkHourStatusEnum.ARCHIVE.getCode());
        if (null!=this.day5)this.day5.setStatus(WorkHourStatusEnum.ARCHIVE.getCode());
        if (null!=this.day6)this.day6.setStatus(WorkHourStatusEnum.ARCHIVE.getCode());
        if (null!=this.day7)this.day7.setStatus(WorkHourStatusEnum.ARCHIVE.getCode());
    }

    /**
     * @Description: 数据化工时详情数据
     * @param
     * @return: void
     * @author: 王金都
     * @Date: 2025/6/11 17:30
     */
    public void initDetailData(){
        if (null!=this.day1){
            this.day1.setPlanId(this.getPlanId());
            this.day1.setEntryId(this.id);
        }
        if (null!=this.day2){
            this.day2.setPlanId(this.getPlanId());
            this.day2.setEntryId(this.id);
        }
        if (null!=this.day3){
            this.day3.setPlanId(this.getPlanId());
            this.day3.setEntryId(this.id);
        }
        if (null!=this.day4){
            this.day4.setPlanId(this.getPlanId());
            this.day4.setEntryId(this.id);
        }
        if (null!=this.day5){
            this.day5.setPlanId(this.getPlanId());
            this.day5.setEntryId(this.id);
        }
        if (null!=this.day6){
            this.day6.setPlanId(this.getPlanId());
            this.day6.setEntryId(this.id);
        }
        if (null!=this.day7){
            this.day7.setPlanId(this.getPlanId());
            this.day7.setEntryId(this.id);
        }
    }
}
