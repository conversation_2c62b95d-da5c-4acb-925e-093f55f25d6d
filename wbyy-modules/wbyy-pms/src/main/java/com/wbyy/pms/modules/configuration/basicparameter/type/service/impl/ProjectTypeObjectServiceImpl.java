package com.wbyy.pms.modules.configuration.basicparameter.type.service.impl;

import java.util.List;

import com.wbyy.common.redis.helper.SimpleLockHelper;
import com.wbyy.pms.common.constant.lockkey.LockKeyOfBasicParameter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import com.wbyy.pms.modules.configuration.basicparameter.type.dao.IProjectTypeObjectDao;
import com.wbyy.pms.modules.configuration.basicparameter.type.domain.ProjectTypeObjectPO;
import com.wbyy.pms.modules.configuration.basicparameter.type.service.IProjectTypeObjectService;
import org.springframework.transaction.annotation.Transactional;

/**
 * 项目类型(业务类型)与对象关系Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-15
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ProjectTypeObjectServiceImpl implements IProjectTypeObjectService {
    private final IProjectTypeObjectDao projectTypeObjectDao;
    private final SimpleLockHelper simpleLockHelper;


    @Override
    public ProjectTypeObjectPO selectById(Long id) {

        return projectTypeObjectDao.selectById(id);
    }

    @Override
    public List<ProjectTypeObjectPO> selectList(ProjectTypeObjectPO po) {

        return projectTypeObjectDao.selectList(po);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean insert(ProjectTypeObjectPO po) {

        return projectTypeObjectDao.insert(po);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean update(ProjectTypeObjectPO po) {

        return projectTypeObjectDao.update(po);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByIds(Long[] ids) {

        return projectTypeObjectDao.deleteByIds(ids);
    }

    @Override
    public void binding(ProjectTypeObjectPO po) {
        Long projectTypeId = po.getProjectTypeId();
        String lockKey = String.format(LockKeyOfBasicParameter.PROJECT_TYPE_OBJECT_BINDING_LOCK, projectTypeId);
        simpleLockHelper.execute(lockKey,()->{
            // 库中是否有
            ProjectTypeObjectPO model = projectTypeObjectDao.getByProjectTypeId(projectTypeId);
            if (null==model){
                model = new ProjectTypeObjectPO();
                model.setProjectTypeId(projectTypeId);
            }
            model.setObjectInfoId(po.getObjectInfoId());
            projectTypeObjectDao.saveOrUpdate(model);
            return null;
        });
    }
}
