package com.wbyy.pms;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import com.wbyy.common.security.annotation.EnableCustomConfig;
import com.wbyy.common.security.annotation.EnableRyFeignClients;

/**
 * CRM
 * 
 * <AUTHOR>
 */
@Slf4j
@EnableCustomConfig
@EnableRyFeignClients
@SpringBootApplication(scanBasePackages = {"com.wbyy.pms", "com.wbyy.biz"})
public class WbyyPmsApplication
{
    public static void main(String[] args)
    {
        SpringApplication.run(WbyyPmsApplication.class, args);
        System.out.println("(♥◠‿◠)ﾉﾞ  PMS模块启动成功   ლ(´ڡ`ლ)ﾞ  \n");
        log.info("(♥◠‿◠)ﾉﾞ  PMS模块启动成功   ლ(´ڡ`ლ)ﾞ");
    }
}
