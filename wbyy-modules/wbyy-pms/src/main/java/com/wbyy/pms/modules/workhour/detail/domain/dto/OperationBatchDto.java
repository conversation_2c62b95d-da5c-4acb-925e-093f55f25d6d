package com.wbyy.pms.modules.workhour.detail.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.wbyy.pms.modules.workhour.detail.domain.WorkHourDetail;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.*;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * @author: 王金都
 * @date: 2025/6/17 10:27
 */
@Data
public class OperationBatchDto {

    @Schema(description = "年")
    @NotBlank(message = "年不得为空")
    private String yearNumber;

    @Schema(description = "周")
    @NotNull(message = "周不得为空")
    private Integer weekNumber;

    @Schema(description = "填报日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @NotNull(message = "填报日期不得为空")
    private Date fillDate;

    @Schema(description = "操作类型（1:暂存；2:提交）")
    @Min(value = 1,message = "非法的操作类型")
    @Max(value = 2,message = "非法的操作类型")
    @NotNull(message = "操作类型不得为空")
    private Integer operationType;

    @Schema(description = "工时详情")
    private List<WorkHourDetail> details;
}
