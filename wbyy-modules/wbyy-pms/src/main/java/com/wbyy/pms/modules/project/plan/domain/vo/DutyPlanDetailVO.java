package com.wbyy.pms.modules.project.plan.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025/5/26  17:43
 * @description 参与任务返回值
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class DutyPlanDetailVO extends DutyPlanVO {

    @Schema(description = "实际工时")
    private BigDecimal realityWorkHours;

    @Schema(description = "用户id")
    private Long userId;

    @Schema(description = "工号")
    private String workNumber;

    @Schema(description = "名字")
    private String realName;

    @Schema(description = "实际进度")
    private Integer realityProgress;
}
