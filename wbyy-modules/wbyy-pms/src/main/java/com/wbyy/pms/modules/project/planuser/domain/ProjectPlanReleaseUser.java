package com.wbyy.pms.modules.project.planuser.domain;

import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.wbyy.common.core.annotation.Excel;
import com.wbyy.orm.core.domain.BaseAuditEntity;

import java.io.Serial;

/**
 * 项目计划责任人-跟踪对象 project_plan_release_user
 * 
 * <AUTHOR>
 * @date 2025-05-13
 */
@Data
@TableName("project_plan_release_user")
@Schema(description = "项目计划责任人-跟踪实体类")
@EqualsAndHashCode(callSuper = true)
@SuperBuilder
@NoArgsConstructor
public class ProjectPlanReleaseUser extends BaseAuditEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /** 主键id */
    @Schema(description = "主键id（新增：不传；修改：必传）")
    private Long id;

    /** 关联项目id */
    @Excel(name = "关联项目id")
    @Schema(description = "关联项目id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "关联项目id不能为空")
    private Long projectId;

    /** 关联计划id */
    @Excel(name = "关联计划id")
    @Schema(description = "关联计划id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "关联计划id不能为空")
    private Long projectPlanId;

    /** 责任人id */
    @Excel(name = "责任人id")
    @Schema(description = "责任人id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "责任人id不能为空")
    private Long userId;


    @Schema(description = "用户工号")
    private String workNumber;

    @Schema(description = "真实姓名")
    private String realName;

    @TableField(exist = false)
    @Schema(description = "责任角色(已翻译、直接使用)")
    private String dutyRuleNames;

    /** 工时，默认取工期内的自然日天数 */
    @Excel(name = "工时，默认取工期内的自然日天数")
    @Schema(description = "工时，默认取工期内的自然日天数", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "工时不能为空")
    private BigDecimal workHour;


    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("projectId", getProjectId())
            .append("projectPlanId", getProjectPlanId())
            .append("userId", getUserId())
            .append("workHour", getWorkHour())
            .append("createBy", getCreateBy())
            .append("createNameBy", getCreateNameBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateNameBy", getUpdateNameBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
