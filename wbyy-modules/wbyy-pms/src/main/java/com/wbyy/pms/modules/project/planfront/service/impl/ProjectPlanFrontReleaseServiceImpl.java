package com.wbyy.pms.modules.project.planfront.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wbyy.common.core.constant.HttpMagConstants;
import com.wbyy.common.core.exception.ServiceException;
import com.wbyy.common.core.utils.pool.CommonFutureUtils;
import com.wbyy.pms.modules.project.plan.domain.ProjectPlanRelease;
import com.wbyy.pms.modules.project.plan.service.IProjectPlanReleaseService;
import com.wbyy.pms.modules.project.planfront.domain.ProjectPlanFrontRelease;
import com.wbyy.pms.modules.project.planfront.mapper.ProjectPlanFrontReleaseMapper;
import com.wbyy.pms.modules.project.planfront.service.IProjectPlanFrontReleaseService;
import com.wbyy.pms.modules.project.planuser.service.IProjectPlanReleaseUserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

/**
 * 项目前置计划 - 跟踪Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-13
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ProjectPlanFrontReleaseServiceImpl extends ServiceImpl<ProjectPlanFrontReleaseMapper, ProjectPlanFrontRelease> implements IProjectPlanFrontReleaseService {
    private final ProjectPlanFrontReleaseMapper projectPlanFrontReleaseMapper;
    private final IProjectPlanReleaseUserService projectPlanReleaseUserService;
    private final IProjectPlanReleaseService projectPlanReleaseService;
    /**
     * 查询项目前置计划 - 跟踪
     *
     * @param id 项目前置计划 - 跟踪主键
     * @return 项目前置计划 - 跟踪
     */
    @Override
    public ProjectPlanFrontRelease selectById(Long id) {
        return this.getById(id);
    }

    @Override
    public Map<Long, String> findMapByProjectPlanIds(List<Long> ids) {
        List<ProjectPlanFrontRelease> list = this.findByProjectPlanIds(ids);
        if (CollUtil.isEmpty(list)) {
            return Collections.emptyMap();
        }
        Map<Long, List<ProjectPlanFrontRelease>> palnMap = list.stream().collect(Collectors.groupingBy(ProjectPlanFrontRelease::getProjectPlanId));

        Map<Long, String> resMap = new HashMap<>(palnMap.size());
        palnMap.forEach((projectPlanId, frontTaskPlans) ->
                resMap.put(projectPlanId, CollUtil.join(frontTaskPlans.stream()
                        .map(ProjectPlanFrontRelease::getFrontPlanSort).toList(), ", "))
        );
        return resMap;
    }

    @Override
    public List<ProjectPlanFrontRelease> findByProjectPlanIds(List<Long> projectPlanIds) {
        if (CollUtil.isEmpty(projectPlanIds)) {
            return List.of();
        }
        LambdaQueryWrapper<ProjectPlanFrontRelease> wrapper = Wrappers.lambdaQuery();
        wrapper.in(ProjectPlanFrontRelease::getProjectPlanId, projectPlanIds);
        return this.list(wrapper);
    }

    /**
     * 查询项目前置计划 - 跟踪列表
     *
     * @param projectPlanFrontRelease 项目前置计划 - 跟踪
     * @return 项目前置计划 - 跟踪
     */
    @Override
    public List<ProjectPlanFrontRelease> selectList(ProjectPlanFrontRelease projectPlanFrontRelease) {
        LambdaQueryWrapper<ProjectPlanFrontRelease> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(ProjectPlanFrontRelease::getProjectId, projectPlanFrontRelease.getProjectId())
                .eq(ProjectPlanFrontRelease::getProjectPlanId, projectPlanFrontRelease.getProjectPlanId())
                .orderByAsc(ProjectPlanFrontRelease::getFrontPlanSort);

        List<ProjectPlanFrontRelease> list = this.list(wrapper);
        if (CollUtil.isEmpty(list)) {
            return Collections.emptyList();
        }

        List<Long> planIds = list.stream().map(ProjectPlanFrontRelease::getFrontPlanId).collect(Collectors.toList());
        // 查询责任人
        Map<Long, String> projectPlanReleaseUserMap = projectPlanReleaseUserService.findMapByProjectIdAndProjectPlanIds(projectPlanFrontRelease.getProjectId(), planIds);
        // 查询计划名称
        Map<Long, ProjectPlanRelease> planNameMap = projectPlanReleaseService.findNameMapByProjectIdAndPlanIds(projectPlanFrontRelease.getProjectId(), planIds);

        list.forEach(item -> {
            item.setFrontPlanUserNames(projectPlanReleaseUserMap.get(item.getFrontPlanId()))
                    .setFrontPlanName(planNameMap.get(item.getFrontPlanId()).getName())
                    .setFrontPlanPlanEndDate(planNameMap.get(item.getFrontPlanId()).getPlanEndDate());
        });
        return list;
    }

    /**
     * 新增项目前置计划 - 跟踪
     *
     * @param projectPlanFrontRelease 项目前置计划 - 跟踪
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean insert(ProjectPlanFrontRelease projectPlanFrontRelease) {
        return this.save(projectPlanFrontRelease);
    }

    /**
     * 修改项目前置计划 - 跟踪
     *
     * @param projectPlanFrontRelease 项目前置计划 - 跟踪
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean update(ProjectPlanFrontRelease projectPlanFrontRelease) {
        return this.updateById(projectPlanFrontRelease);
    }

    /**
     * 批量删除项目前置计划 - 跟踪
     *
     * @param ids 需要删除的项目前置计划 - 跟踪主键
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByIds(Long[] ids) {
        return this.removeBatchByIds(Arrays.asList(ids));
    }

    @Override
    public boolean deleteByProjectId(Long projectId) {
        if (null == projectId) return false;
        LambdaQueryWrapper<ProjectPlanFrontRelease> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(ProjectPlanFrontRelease::getProjectId, projectId);
        return this.remove(wrapper);
    }

    /**
     * 查询计划的前置计划的列表,包含实际开始时间、进度等
     *
     * @param projectId 项目id
     * @param planId    计划id
     */
    @Override
    public List<ProjectPlanRelease> listFrontPlan(Long projectId, Long planId) {
        List<ProjectPlanRelease> projectPlanReleases = baseMapper.selectFrontPlanByPlanIdAndProjectId(projectId, planId);
        // 翻译责任人
        Future<Map<Long, String>> planIdUserNamesMap = CommonFutureUtils.taskAsync(() ->
                projectPlanReleaseUserService.findMapByProjectIdAndProjectPlanIds(projectId, projectPlanReleases.
                        stream().map(ProjectPlanRelease::getId).collect(Collectors.toList())));
        try {
            Map<Long, String> map = planIdUserNamesMap.get();
            projectPlanReleases.forEach(item -> {
                item.setProjectPlanUserNames(map.get(item.getId()));
            });
        } catch (ExecutionException | InterruptedException e) {
            throw new ServiceException(StrUtil.format(HttpMagConstants.DATA_CONVERSION_ERR, e.getMessage()));
        }
        return projectPlanReleases;
    }
}
