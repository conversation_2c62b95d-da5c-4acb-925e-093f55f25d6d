package com.wbyy.pms.modules.configuration.weekofyear.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * @author: 王金都
 * @date: 2025/6/5 13:50
 */
@Data
@Schema(description = "周详情实体类")
public class WeekDetailVo {

    @Schema(description = "周一、周二。。。")
    private String week;

    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date dateTime;

    @Schema(description = "5-12、5-13。。。")
    private String date;

    @Schema(description = "6月9日、6月10日")
    private String dateStr;

    private Integer day;

    @Schema(description = "考勤开始时间：8:15")
    private String attendStartTime;

    @Schema(description = "打卡签到时间：yyyy-MM-dd HH:mm")
    private String signInTime;

    @Schema(description = "考勤结束时间：17:30")
    private String attendEndTime;

    @Schema(description = "打开签退时间：yyyy-MM-dd HH:mm")
    private String signOutTime;

    @Schema(description = "工作时长：7.5H")
    private String hour;

    @Schema(description = "考勤时长：xhxxmin")
    private String hourStr;

    @Schema(description = "休假时长：休假1H")
    private String vacationHour;

    @Schema(description = "数据状态（1:一般数据（暂存、提交、归档）；2:待审核；3:审核未通过）")
    private Integer dataStatus;

}
