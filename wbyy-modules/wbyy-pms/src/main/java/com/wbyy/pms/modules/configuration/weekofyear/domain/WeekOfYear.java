package com.wbyy.pms.modules.configuration.weekofyear.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.wbyy.common.core.annotation.Excel;
import com.wbyy.common.core.handler.mybatis.StringListTypeHandler;
import com.wbyy.orm.core.domain.BaseAuditEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serial;
import java.util.Date;
import java.util.List;

/**
 * 周年对象 week_of_year
 * 
 * <AUTHOR>
 * @date 2025-06-05
 */
@Data
@SuperBuilder
@NoArgsConstructor
@Accessors(chain = true)
@TableName(value = "week_of_year", autoResultMap = true)
@Schema(description = "周年实体类")
@EqualsAndHashCode(callSuper = true)
public class WeekOfYear extends BaseAuditEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /** 主键id */
    @Schema(description = "主键id（新增：不传；修改：必传）")
    private Long id;

    /** 年 */
    @Excel(name = "年")
    @Schema(description = "年", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "年不能为空")
    @Size(max = 4, message = "年不能超过 4 个字符")
    private String yearNumber;

    /** 第几周 */
    @Excel(name = "第几周")
    @Schema(description = "第几周", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "第几周不能为空")
    private Integer weekNumber;

    /** 周开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "周开始时间", width = 30, dateFormat = "yyyy-MM-dd")
    @Schema(description = "周开始时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "周开始时间不能为空")
    private Date startDate;

    /** 周结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "周结束时间", width = 30, dateFormat = "yyyy-MM-dd")
    @Schema(description = "周结束时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "周结束时间不能为空")
    private Date endDate;

    /** 周内日期详情 */
    @Excel(name = "周内日期详情")
    @Schema(description = "周内日期详情", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "周内日期详情不能为空")
    @TableField(typeHandler = StringListTypeHandler.class)
    private List<String> dateDetail;

    /** 删除标志（0代表存在 1代表删除） */
    @Schema(description = "删除标志（0代表存在 1代表删除）", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "周内日期详情不能为空")
    @TableLogic
    private Integer delFlag;


    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("yearNumber", getYearNumber())
            .append("weekNumber", getWeekNumber())
            .append("startDate", getStartDate())
            .append("endDate", getEndDate())
            .append("dateDetail", getDateDetail())
            .append("delFlag", getDelFlag())
            .append("createBy", getCreateBy())
            .append("createNameBy", getCreateNameBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateNameBy", getUpdateNameBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
