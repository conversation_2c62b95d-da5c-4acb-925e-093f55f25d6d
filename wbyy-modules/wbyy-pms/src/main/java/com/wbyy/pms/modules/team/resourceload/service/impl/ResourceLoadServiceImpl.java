package com.wbyy.pms.modules.team.resourceload.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.github.pagehelper.PageInfo;
import com.wbyy.common.core.constant.HttpMagConstants;
import com.wbyy.common.core.constant.SecurityConstants;
import com.wbyy.common.core.domain.R;
import com.wbyy.common.core.exception.ServiceException;
import com.wbyy.common.core.utils.PageUtils;
import com.wbyy.common.core.utils.ServletUtils;
import com.wbyy.common.core.web.page.TableDataInfo;
import com.wbyy.common.core.web.page.TableSupport;
import com.wbyy.pms.common.constant.PMSConst;
import com.wbyy.biz.common.drag.model.TimeRangeModel;
import com.wbyy.pms.common.utils.PlanUtils;
import com.wbyy.pms.modules.project.plan.domain.ProjectPlanRelease;
import com.wbyy.pms.modules.project.plan.domain.vo.DutyPlanDailyVO;
import com.wbyy.pms.modules.project.plan.domain.vo.ProjectPlanDetailVO;
import com.wbyy.pms.modules.project.plan.domain.vo.StatusVO;
import com.wbyy.pms.modules.project.plan.mapper.ProjectPlanReleaseMapper;
import com.wbyy.pms.modules.project.plan.service.IProjectPlanReleaseService;
import com.wbyy.pms.modules.project.teamperson.domain.ProjectTeamPersonPO;
import com.wbyy.pms.modules.project.teamperson.service.IProjectTeamPersonService;
import com.wbyy.pms.modules.team.resourceload.domain.dto.DetailDto;
import com.wbyy.pms.modules.team.resourceload.domain.dto.ResourceLoadDTO;
import com.wbyy.pms.modules.team.resourceload.domain.vo.DetailPlanVo;
import com.wbyy.pms.modules.team.resourceload.domain.vo.DetailWorkHourVo;
import com.wbyy.pms.modules.team.resourceload.domain.vo.ResourceLoadVO;
import com.wbyy.pms.modules.team.resourceload.service.IResourceLoadService;
import com.wbyy.pms.modules.workhour.detail.domain.WorkHourDetail;
import com.wbyy.pms.modules.workhour.detail.mapper.WorkHourDetailMapper;
import com.wbyy.pms.modules.workhour.detail.service.IWorkHourDetailService;
import com.wbyy.system.api.UserApi;
import com.wbyy.system.api.domain.ApiUser;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/7/4  11:40
 * @description 资源负荷服务实现类
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ResourceLoadServiceImpl implements IResourceLoadService {
    private final ProjectPlanReleaseMapper projectPlanReleaseMapper;
    private final WorkHourDetailMapper workHourDetailMapper;
    private final UserApi userApi;
    private final IWorkHourDetailService workHourDetailService;
    private final IProjectPlanReleaseService projectPlanReleaseService;
    private final IProjectTeamPersonService projectTeamPersonService;

    @Override
    public List<DetailPlanVo> listDetailPlan(DetailDto dto) {
        // 查询时间
        String day = dto.getDay();
        String yearMonth = dto.getYearMonth();
        TimeRangeModel timeRange = this.getTimeRange(yearMonth, day);
        List<ProjectPlanRelease> plans = projectPlanReleaseMapper.selectByUserIdByProjectIdByTimeRange(dto.getUserId(),
                dto.getProjectId(),
                timeRange.getStartTime(),
                timeRange.getEndTime());
        return plans.stream()
                // 按任务去重，同一个任务有相同的责任人
                .collect(Collectors.toMap(
                        ProjectPlanRelease::getId,
                        Function.identity(),
                        (existing, replacement) -> existing
                ))
                .values().stream()
                // 构造返回结果
                .map(item -> {
                    StatusVO statusVO = PlanUtils.mackStatus(item);
                    return DetailPlanVo.builder()
                            .status(statusVO)
                            .planName(item.getName())
                            .planId(item.getId())
                            .projectName(item.getProjectName())
                            .projectNumber(item.getProjectNumber())
                            .projectId(item.getProjectId())
                            .planStartDate(item.getPlanStartDate())
                            .planEndDate(item.getPlanEndDate())
                            .progress(item.getRealityProgress())
                            .build();
                })
                .toList();
    }

    @Override
    public List<DetailWorkHourVo> listDetailWorkHour(DetailDto dto) {
        // 查询时间
        String day = dto.getDay();
        String yearMonth = dto.getYearMonth();
        TimeRangeModel timeRange = this.getTimeRange(yearMonth, day);
        List<WorkHourDetail> details = workHourDetailMapper.selectByUserIdByProjectIdByTimeRange(dto.getUserId(),
                dto.getProjectId(),
                timeRange.getStartTime(),
                timeRange.getEndTime());
        if (CollectionUtil.isEmpty(details)) return List.of();
        List<DetailWorkHourVo> list = new ArrayList<>();
        // 任务条目
        List<WorkHourDetail> planDetails = details.stream()
                .filter(item -> null != item.getPlanId()).toList();
        if (CollectionUtil.isNotEmpty(planDetails)) {
            // 填报的工时按任务id分组
            Map<Long, List<WorkHourDetail>> workHourGroupByPlanId = planDetails.stream()
                    .collect(Collectors.groupingBy(WorkHourDetail::getPlanId));
            List<Long> planIds = workHourGroupByPlanId.keySet().stream().toList();
            // 查询任务信息
            List<ProjectPlanDetailVO> plans = projectPlanReleaseMapper.selectPlanVoByIds(planIds);
            Map<Long, ProjectPlanDetailVO> planMapById = plans.stream()
                    .collect(Collectors.toMap(ProjectPlanDetailVO::getId,
                            Function.identity(),
                            (o1, o2) -> {
                                // 合并一个任务分配重复责任人的工时
                                BigDecimal o1WorkHour = Optional.ofNullable(o1.getWorkHour()).orElse(BigDecimal.ZERO);
                                BigDecimal o2WorkHour = Optional.ofNullable(o2.getWorkHour()).orElse(BigDecimal.ZERO);
                                o1.setWorkHour((o1WorkHour.add(o2WorkHour)));
                                return o1;
                            }));
            for (Long planId : workHourGroupByPlanId.keySet()) {
                // 获取任务
                ProjectPlanDetailVO plan = planMapById.get(planId);
                // 获取任务填报的工时
                List<WorkHourDetail> workHoursByPlanId = workHourGroupByPlanId.get(planId);
                // 填报的工时
                DetailWorkHourVo item = new DetailWorkHourVo();
                item.setFillWorkHour(getFillWorkHour(workHoursByPlanId));
                if (null != plan) {
                    fillFieldByPlan(plan, item);
                }else {
                    WorkHourDetail workHour = workHoursByPlanId.get(0);
                    fillFieldByWorkHour(item, workHour);
                }
                list.add(item);
            }

        }
        // 公共条目
        List<WorkHourDetail> commonDetails = details.stream()
                .filter(item -> null == item.getPlanId()).toList();
        // 根据条目名称分组
        Map<String, List<WorkHourDetail>> workHourGroupByEntryName = commonDetails.stream()
                .collect(Collectors.groupingBy(WorkHourDetail::getEntryName));
        for (String entryName : workHourGroupByEntryName.keySet()) {
            List<WorkHourDetail> workHoursByEntryName = workHourGroupByEntryName.get(entryName);
            // 填报的工时
            DetailWorkHourVo item = new DetailWorkHourVo();
            item.setFillWorkHour(getFillWorkHour(workHoursByEntryName));
            WorkHourDetail workHour = workHoursByEntryName.get(0);
            fillFieldByWorkHour(item, workHour);
            list.add(item);
        }
        return list;
    }

    /**
     * 获取填报工时
     * @param workHours
     * @return BigDecimal
     * <AUTHOR>
     * @date 2025/7/5 21:44
     */
    private BigDecimal getFillWorkHour(List<WorkHourDetail> workHours){
        return workHours.stream()
                .map(item -> Optional.ofNullable(item.getWorkHour())
                        .orElse(BigDecimal.ZERO))
                .reduce(BigDecimal.ZERO, BigDecimal::add)
                .setScale(1,RoundingMode.HALF_UP);
    }

    /**
     * 通过工时填报填充属性
     * @param item
     * @param workHour
     * @return void
     * <AUTHOR>
     * @date 2025/7/5 21:36
     */
    private void fillFieldByWorkHour(DetailWorkHourVo item, WorkHourDetail workHour) {
        item.setEntryName(workHour.getEntryName());
        item.setPlanId(workHour.getPlanId());
        item.setProjectName(workHour.getProjectName());
        item.setProjectNumber(workHour.getProjectNumber());
        item.setProjectId(workHour.getProjectId());
    }

    /**
     * 通过任务填充属性
     * @param plan
     * @param item
     * @return void
     * <AUTHOR>
     * @date 2025/7/5 21:37
     */
    private void fillFieldByPlan(ProjectPlanDetailVO plan, DetailWorkHourVo item) {
        StatusVO status = PlanUtils.mackStatus(plan);
        item.setStatus(status);
        item.setEntryName(plan.getName());
        item.setPlanId(plan.getId());
        item.setProjectName(plan.getProjectName());
        item.setProjectNumber(plan.getProjectNumber());
        item.setProjectId(plan.getProjectId());
        item.setProgress(plan.getRealityProgress());
        item.setPlanWorkHour(plan.getWorkHour()
                // 转小时 *7.5
                .multiply(PMSConst.WORKING_HOURS_OF_DAY).setScale(1, RoundingMode.HALF_UP));
    }

    /**
     * 获取查询开始结束时间
     *
     * @param yearMonth
     * @param day
     * @return TimeRangeModel
     * <AUTHOR>
     * @date 2025/7/5 20:18
     */
    private TimeRangeModel getTimeRange(String yearMonth, String day) {
        String timeStr = StrUtil.isNotBlank(day) ?
                yearMonth + "-" + day :
                yearMonth;
        DateTime date = StrUtil.isNotBlank(day) ?
                DateUtil.parse(timeStr, "yyyy-MM-d") :
                DateUtil.parse(timeStr, "yyyy-MM");
        Date startTime = StrUtil.isNotBlank(day) ?
                DateUtil.beginOfDay(date) :
                DateUtil.beginOfMonth(date);
        Date endTime = StrUtil.isNotBlank(day) ?
                DateUtil.endOfDay(date) :
                DateUtil.endOfMonth(date);
        return TimeRangeModel.builder()
                .startTime(startTime)
                .endTime(endTime)
                .build();
    }

    /**
     * 资源负荷列表
     *
     * @param dto 入参
     * @return 结果
     */
    @Override
    public List<ResourceLoadVO> selectList(ResourceLoadDTO dto, AtomicInteger total) {
        if (0 != Boolean.compare(null != dto.getProjectId(), null != dto.getProjectType()))
            throw new ServiceException(HttpMagConstants.PARAMS_NOT_NULL);
        List<ApiUser> apiUserList = null;
        // 项目负荷，参数校验
        if (null != dto.getProjectId()) { // 项目负荷,人员是本项目成员
            PageUtils.startPage("userId", "asc"); // 重新激活分页
            List<ProjectTeamPersonPO> personList = projectTeamPersonService.findByProjectId(dto.getProjectId());
            if (CollUtil.isEmpty(personList)) return List.of();
            apiUserList = personList.stream().map(projectTeamPerson -> {
                ApiUser apiUser = new ApiUser();
                apiUser.setUserId(projectTeamPerson.getUserId());
                apiUser.setUserName(projectTeamPerson.getRealName());
                apiUser.setWorkNumber(projectTeamPerson.getWorkNumber());
                return apiUser;
            }).collect(Collectors.toList());
            total.set(Math.toIntExact(new PageInfo<>(personList).getTotal()));
        } else { // 组织人员
            TableDataInfo<ApiUser> result = userApi.pageAllUserByDept(dto.getDeptId(), null, ServletUtils.getParameter(TableSupport.PAGE_NUM)
                    , ServletUtils.getParameter(TableSupport.PAGE_SIZE), SecurityConstants.INNER);
            if (R.FAIL == result.getCode()) {
                throw new ServiceException("用户查询失败:" + result.getMsg());
            }
            apiUserList = result.getRows();
            if (CollUtil.isEmpty(apiUserList)) return List.of();
            total.set((int) result.getTotal());
        }
        DateTime yyyyMM = DateUtil.parse(dto.getMonthNumber(), DateTimeFormatter.ofPattern("yyyy-MM"));
        Date start = DateUtil.beginOfMonth(yyyyMM), end = DateUtil.endOfMonth(yyyyMM);
        // 该月最大天数
        Long days = DateUtil.betweenDay(start, end, true) + 1;
        Map<Long, ApiUser> collect = apiUserList.stream().collect(Collectors.toMap(ApiUser::getUserId, Function.identity()));
        // 查询本项目则过滤，全部项目则不过滤
        if (1 == dto.getMode()) { // 查询工时数
            return this.getWorkHour(collect, start, end, days, dto.getProjectId(), dto.getProjectType());
        }
        return this.getPlans(collect, start, end, days, dto.getProjectId(), dto.getProjectType());
    }

    /**
     * 查询工时
     * @param collect 用户数据
     * @param start 开始时间
     * @param end 结束时间
     * @param days 月天数
     * @param projectId 项目id
     * @param projectType 项目查询类型
     * @return 工时
     */
    private List<ResourceLoadVO> getWorkHour(Map<Long, ApiUser> collect,Date start,Date end,Long days,Long projectId, Integer projectType) {
        List<ResourceLoadVO> loadVOList = new ArrayList<>();
        List<WorkHourDetail> workHourDetailList = workHourDetailService.findByUserIdsAndTime(collect.keySet(), start, end,projectId,projectType);
        Map<Long, List<WorkHourDetail>> workHourMap = workHourDetailList.stream().collect(Collectors.groupingBy(WorkHourDetail::getCreateBy));
        for (Map.Entry<Long, ApiUser> entry : collect.entrySet()) {
            ApiUser apiUser = entry.getValue();
            Map<Integer, BigDecimal> numberHourMap = new HashMap<>();
            if (workHourMap.containsKey(entry.getKey())) {
                numberHourMap = this.buildDayWorkHour(workHourMap.get(entry.getKey()));
            }
            this.fillData(days,numberHourMap,loadVOList,apiUser, null);
        }
        return loadVOList;
    }

    /**
     * 构建每日的工时数据
     * @param workHourDetailList 工时数据
     * @return 每日的工时数据
     */
    private Map<Integer,BigDecimal> buildDayWorkHour(List<WorkHourDetail> workHourDetailList) {
        return workHourDetailList.stream().collect(Collectors.groupingBy(d->DateUtil.dayOfMonth(d.getFillDate()),
                Collectors.reducing(BigDecimal.ZERO, WorkHourDetail::getWorkHour, BigDecimal::add)));
    }

    /**
     * 查询任务
     * @param collect 用户数据
     * @param start 开始时间
     * @param end 结束时间
     * @param days 月天数
     * @param projectId 项目id
     * @return 任务
     */
    private List<ResourceLoadVO> getPlans(Map<Long, ApiUser> collect,Date start,Date end,Long days,Long projectId, Integer projectType) {
        List<ResourceLoadVO> loadVOList = new ArrayList<>();
        List<DutyPlanDailyVO> planDailyVOList = projectPlanReleaseService.findByUserIdsAndTime(collect.keySet(), start, end, projectId, projectType);
        Map<Long, List<DutyPlanDailyVO>> userPlanMap = planDailyVOList.stream().collect(Collectors.groupingBy(DutyPlanDailyVO::getUserId));
        for (Map.Entry<Long, ApiUser> entry : collect.entrySet()) {
            ApiUser apiUser = entry.getValue();
            Map<Integer, BigDecimal> numberPlanMap = new HashMap<>();
            BigDecimal total = BigDecimal.ZERO;
            if (userPlanMap.containsKey(entry.getKey())) {
                List<DutyPlanDailyVO> dutyPlanDailyVOS = userPlanMap.get(entry.getKey());
                total = BigDecimal.valueOf(dutyPlanDailyVOS.size());
                numberPlanMap = this.buildDayPlans(dutyPlanDailyVOS, start, days);
            }
            this.fillData(days,numberPlanMap,loadVOList,apiUser,total);
        }
        return loadVOList;
    }

    /**
     * 构建每日的任务数据
     * @param planDailyVOList 任务数据
     * @return 每日的任务数据
     */
    private Map<Integer, BigDecimal> buildDayPlans(List<DutyPlanDailyVO> planDailyVOList, Date start, Long days) {
        Map<Integer, BigDecimal> numberPlanMap = new HashMap<>();
        for (int i = 0; i < days; i++) {
            DateTime dateTime = DateUtil.offsetDay(start, i);
            numberPlanMap.put(i + 1, BigDecimal.valueOf(planDailyVOList.stream().filter(plan ->
                    !dateTime.before(plan.getPlanStartDate()) &&  // 相当于 >=
                            !dateTime.after(plan.getPlanEndDate())        // 相当于 <=
            ).count()));
        }
        return numberPlanMap;
    }

    /**
     * 填充数据
     * @param days 月总天数
     * @param numberHourMap 每天的值
     * @param loadVOList 返回结果
     * @param apiUser 用户
     */
    private void fillData(Long days,Map<Integer, BigDecimal> numberHourMap,List<ResourceLoadVO> loadVOList,ApiUser apiUser,BigDecimal total){
        boolean needCalc = false;
        if (null == total) {
            total = BigDecimal.ZERO;
            needCalc = true;
        }
        Map<String, ResourceLoadVO.DayData> hourMap = new HashMap<>(32);
        for (int i = 1; i <= days; i++) {
            BigDecimal value = numberHourMap.getOrDefault(i, null);
            hourMap.put("day"+i,  ResourceLoadVO.DayData.builder().day(i).value(value).build());
            if (needCalc) {
                total = total.add(null == value ? BigDecimal.ZERO : value);
            }
        }

        loadVOList.add(ResourceLoadVO.builder()
                .userId(apiUser.getUserId())
                .userName(apiUser.getUserName())
                .workNumber(apiUser.getWorkNumber())
                .data(hourMap)
                .total(total)
                .build());
    }
}
