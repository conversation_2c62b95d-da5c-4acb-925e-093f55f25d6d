package com.wbyy.pms.modules.project.planfront.service;

import java.util.List;
import java.util.Map;

import com.wbyy.pms.modules.project.planfront.domain.ProjectPlanFrontVersion;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 项目前置计划 - 版本Service接口
 *
 * <AUTHOR>
 * @date 2025-05-13
 */
public interface IProjectPlanFrontVersionService  extends IService<ProjectPlanFrontVersion> {
    /**
     * 查询项目前置计划 - 版本
     *
     * @param id 项目前置计划 - 版本主键
     * @return 项目前置计划 - 版本
     */
    ProjectPlanFrontVersion selectById(Long id);

    /**
     * 查询项目前置计划 - 版本列表
     *
     * @param projectPlanFrontVersion 项目前置计划 - 版本
     * @return 项目前置计划 - 版本集合
     */
    List<ProjectPlanFrontVersion> selectList(ProjectPlanFrontVersion projectPlanFrontVersion);

    /**
     * 新增项目前置计划 - 版本
     *
     * @param projectPlanFrontVersion 项目前置计划 - 版本
     * @return 结果
     */
    boolean insert(ProjectPlanFrontVersion projectPlanFrontVersion);

    /**
     * 修改项目前置计划 - 版本
     *
     * @param projectPlanFrontVersion 项目前置计划 - 版本
     * @return 结果
     */
    boolean update(ProjectPlanFrontVersion projectPlanFrontVersion);

    /**
     * 批量删除项目前置计划 - 版本
     *
     * @param ids 需要删除的项目前置计划 - 版本主键集合
     * @return 结果
     */
    boolean deleteByIds(Long[] ids);

    /**
     * 根据计划ID version，查询前置任务
     * @param projectId 项目ID
     * @param version 版本
     * @param planIds 计划ids
     * @return
     */
    Map<Long, String> findMapByProjectPlanIds(Long projectId, Integer version, List<Long> planIds);

    /**
     * 根据计划ID version，查询前置任务
     * @param projectId 项目ID
     * @param version 版本
     * @param planIds 计划ids
     * @return
     */
    public List<ProjectPlanFrontVersion> findByProjectPlanIds(Long projectId, Integer version, List<Long> planIds) ;
}
