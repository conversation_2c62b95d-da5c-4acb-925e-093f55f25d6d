package com.wbyy.pms.modules.project.plan.domain.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @author: 王金都
 * @date: 2025/6/5 14:44
 */
@Data
@Schema(description = "用户任务")
public class UserPlanDto {
    @Schema(description = "所属项目id")
    private Long projectId;

    @Schema(description = "任务/项目名称")
    private String searchValue;

    @Schema(description = "搜索类型（0:所有；1:计划完成前后7天；2:计划完成前后15天；3:计划完成前后30天；4:计划完成前后90天）")
    private Integer searchType;

}
