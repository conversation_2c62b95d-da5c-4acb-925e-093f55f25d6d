package com.wbyy.pms.api.project;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.wbyy.common.core.domain.R;
import com.wbyy.common.core.utils.TreeUtil;
import com.wbyy.common.security.annotation.InnerAuth;
import com.wbyy.pms.api.ProjectTypeApi;
import com.wbyy.pms.api.domain.ApiProjectType;
import com.wbyy.pms.modules.configuration.basicparameter.type.domain.ProjectType;
import com.wbyy.pms.modules.configuration.basicparameter.type.service.IProjectTypeService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/7/28 18:16
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/project/type")
@InnerAuth
public class ProjectTypeApiImpl implements ProjectTypeApi {

    private final IProjectTypeService projectTypeService;

    @GetMapping("/simple-tree")
    @Override
    public R<JSONArray> simpleTree(String source) {
        List<ApiProjectType> list = projectTypeService.listByDisable(0).stream()
                .map(item -> {
                    ApiProjectType type = new ApiProjectType();
                    type.setId(item.getId());
                    type.setParentId(item.getParentId());
                    type.setName(item.getName());
                    return type;
                }).toList();
        if (CollectionUtil.isEmpty(list)) return R.ok();
        return R.ok(TreeUtil.buildTree(JSONArray.parse(JSON.toJSONString(list)),"id","parentId","children"));
    }
}
