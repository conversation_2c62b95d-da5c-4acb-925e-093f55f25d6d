package com.wbyy.pms.modules.project.planfile.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.wbyy.pms.modules.project.planfile.domain.ProjectPlanFile;
import com.wbyy.pms.modules.project.planfile.domain.vo.ProjectPlanFileVO;

import java.util.Collection;
import java.util.List;

/**
 * 项目计划文件Service接口
 *
 * <AUTHOR>
 * @date 2025-05-14
 */
public interface IProjectPlanFileService extends IService<ProjectPlanFile> {
    /**
     * 查询项目计划文件
     *
     * @param id 项目计划文件主键
     * @return 项目计划文件
     */
    ProjectPlanFile selectById(Long id);

    /**
     * 查询项目计划文件列表
     *
     * @param projectPlanFile 项目计划文件
     * @return 项目计划文件集合
     */
    List<ProjectPlanFile> selectList(ProjectPlanFile projectPlanFile);

    /**
     * 新增项目计划文件
     *
     * @param projectPlanFile 项目计划文件
     * @return 结果
     */
    boolean insert(ProjectPlanFile projectPlanFile);

    /**
     * 修改项目计划文件
     *
     * @param projectPlanFile 项目计划文件
     * @return 结果
     */
    boolean update(ProjectPlanFile projectPlanFile);

    /**
     * 批量删除项目计划文件
     *
     * @param ids 需要删除的项目计划文件主键集合
     * @return 结果
     */
    boolean deleteByIds(Long[] ids);

    /**
     * 根据项目ID 删除附件
     *
     * @param projectId 项目ID
     * @param planIds   计划IDs
     * @return 结果
     */
    boolean deleteByProjectPlanIds(Long projectId, List<Long> planIds);

    /**
     * 查询项目计划附件
     *
     * @param projectId 项目ID
     * @return 结果
     */
    List<ProjectPlanFile> findByProjectId(Long projectId);


    /**
     * 查询计划已上传的文档
     * @param projectId 项目id
     * @param planId 计划id
     * @return 文档
     */
    List<ProjectPlanFileVO> listExpectedDocs(Long projectId, Long planId);


    /**
     * 删除附件
     *
     * @param planIds   计划IDs
     * @return 结果
     */
    boolean deleteByPlanIds(Collection<Long> planIds);

    /**
     * 根据任务id查任务文件
     *       
     * @param planId
     * @return List<ProjectPlanFile>
     * <AUTHOR>
     * @date 2025/7/8 19:56
     */
    List<ProjectPlanFile> listByPlanId(Long planId);
}
