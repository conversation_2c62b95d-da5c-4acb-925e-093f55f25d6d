package com.wbyy.pms.modules.project.planfeedback.domain;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.wbyy.log.annotation.LogField;
import com.wbyy.pms.modules.project.planfile.domain.ProjectPlanFile;
import com.wbyy.system.api.domain.ApiSysFile;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.wbyy.common.core.annotation.Excel;
import com.wbyy.orm.core.domain.BaseAuditEntity;

import java.io.Serial;
import java.util.List;

/**
 * 项目任务反馈对象 project_plan_feedback
 * 
 * <AUTHOR>
 * @date 2025-05-13
 */
@Data
@TableName("project_plan_feedback")
@Schema(description = "项目任务反馈实体类")
@EqualsAndHashCode(callSuper = true)
@SuperBuilder
@NoArgsConstructor
public class ProjectPlanFeedback extends BaseAuditEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /** 主键id */
    @Schema(description = "主键id（新增：不传；修改：必传）")
    private Long id;

    /** 关联的项目id */
    @Excel(name = "关联的项目id")
    @Schema(description = "关联的项目id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "关联的项目id不能为空")
    private Long projectId;

    /** 关联的计划任务id */
    @Excel(name = "关联的计划任务id")
    @Schema(description = "关联的计划任务id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "关联的计划任务id不能为空")
    private Long projectPlanId;

    @TableField(exist = false)
    private String projectPlanName;

    /** 任务实际开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "任务实际开始时间", width = 30, dateFormat = "yyyy-MM-dd")
    @Schema(description = "任务实际开始时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @LogField(name = "实际开始时间",dateFormat = "yyyy-MM-dd")
    private Date startTime;

    /** 任务实际结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "任务实际结束时间", width = 30, dateFormat = "yyyy-MM-dd")
    @Schema(description = "任务实际结束时间")
    @LogField(name = "实际结束时间",dateFormat = "yyyy-MM-dd")
    private Date endTime;

    /** 进度，0-100,100表示任务结束 */
    @Excel(name = "进度，0-100,100表示任务结束")
    @Schema(description = "进度，0-100,100表示任务结束", requiredMode = Schema.RequiredMode.REQUIRED)
    @LogField(name = "进度")
    private Integer realityProgress;

    /** 延迟原因(字典project_plan_delay_reason) */
    @Excel(name = "延迟原因", readConverterExp = "1=预测不准,2=难度大,3=资源冲突,4=其他")
    @Schema(description = "延迟原因(字典project_plan_delay_reason)")
    @Size(max = 100, message = "延迟原因不能超过 100 个字符")
    @LogField(name = "延迟原因")
    private String delayReason;

    /** 进展说明 */
    @Excel(name = "进展说明")
    @Schema(description = "进展说明")
    @Size(max = 500, message = "进展说明不能超过 500 个字符")
    @LogField(name = "进展说明")
    private String progressNote;

    /** 删除标志（0代表存在 1代表删除） */
    @Schema(description = "删除标志（0代表存在 1代表删除）")
    @TableLogic
    private Integer delFlag;

    @Schema(description = "交付文档")
    @TableField(exist = false)
    private List<ApiSysFile> fileList;

    @TableField(exist = false)
    private String fileNames;


    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("projectId", getProjectId())
            .append("planId", getProjectPlanId())
            .append("startTime", getStartTime())
            .append("endTime", getEndTime())
            .append("realityProgress", getRealityProgress())
            .append("delayReason", getDelayReason())
            .append("progressNote", getProgressNote())
            .append("delFlag", getDelFlag())
            .append("createBy", getCreateBy())
            .append("createNameBy", getCreateNameBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateNameBy", getUpdateNameBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
