package com.wbyy.pms.modules.workhour.statistics.controller;

import com.alibaba.fastjson2.JSON;
import com.wbyy.common.core.domain.R;
import com.wbyy.common.core.utils.poi.ExcelUtil;
import com.wbyy.common.core.web.controller.BaseController;
import com.wbyy.common.core.web.page.TableDataInfo;
import com.wbyy.common.log.annotation.Log;
import com.wbyy.common.log.enums.BusinessType;
import com.wbyy.common.security.annotation.RequiresPermissions;
import com.wbyy.pms.modules.workhour.detail.domain.WorkHourDetail;
import com.wbyy.pms.modules.workhour.statistics.domain.WorkHourStatistics;
import com.wbyy.pms.modules.workhour.statistics.domain.dto.ByProjectDTO;
import com.wbyy.pms.modules.workhour.statistics.domain.dto.SearchDTO;
import com.wbyy.pms.modules.workhour.statistics.domain.dto.StatisticsDTO;
import com.wbyy.pms.modules.workhour.statistics.domain.vo.*;
import com.wbyy.pms.modules.workhour.statistics.service.IWorkHourStatisticsService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 工时统计Controller
 *
 * <AUTHOR>
 * @date 2025-06-05
 */
@RestController
@RequiredArgsConstructor
@Tag(name = "工时统计")
@RequestMapping("/work-hour-statistics")
public class WorkHourStatisticsController extends BaseController {

    private final IWorkHourStatisticsService workHourStatisticsService;

    @Operation(summary = "分页列表")
    @RequiresPermissions("workhour:statistics:list")
    @GetMapping("/page")
    public TableDataInfo<WorkHourStatistics> page(@ParameterObject WorkHourStatistics workHourStatistics) {
        startPage();
        List<WorkHourStatistics> list = workHourStatisticsService.selectList(workHourStatistics);
        return getDataTable(list);
    }

    @Operation(summary = "按部门统计")
    @RequiresPermissions("workhour:statistics:list")
    @GetMapping("/by-dept")
    public R<List<ByDeptVo>> byDept(StatisticsDTO dto) {
        return R.ok(workHourStatisticsService.byDept(dto));
    }

    @Operation(summary = "按部门统计详情")
    @RequiresPermissions("workhour:statistics:list")
    @GetMapping("/by-dept-detail")
    public TableDataInfo<ByDeptDetailVo> byDeptDetail(StatisticsDTO dto) {
        startPage();
        List<ByDeptDetailVo> byDeptDetailVos = workHourStatisticsService.byDeptDetail(dto);
        return getDataTable(byDeptDetailVos);
    }

    @Operation(summary = "按项目统计")
    @RequiresPermissions("workhour:statistics:list")
    @GetMapping("/by-project")
    public TableDataInfo<ByProjectVo> byProject(@Validated @ParameterObject ByProjectDTO dto) {
        List<ByProjectVo> result = workHourStatisticsService.byProject(dto);
        return getDataTable(result);
    }

    @Operation(summary = "按人员统计")
    @RequiresPermissions("workhour:statistics:list")
    @GetMapping("/by-user")
    public TableDataInfo<ByUserVo> byUser(StatisticsDTO dto) {
        startPage();
        List<ByUserVo> result = workHourStatisticsService.byUser(dto);
        return getDataTable(result);
    }

    @Operation(summary = "按人员统计详情")
    @RequiresPermissions("workhour:statistics:list")
    @GetMapping("/by-user-detail")
    public TableDataInfo<WorkHourDetail> byUserDetail(@Validated StatisticsDTO dto) {
        startPage();
        List<WorkHourDetail> result = workHourStatisticsService.byUserDetail(dto);
        return getDataTable(result);
    }

    @Operation(summary = "导出列表")
    @RequiresPermissions("workhour:statistics:export")
    @Log(title = "工时统计", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, WorkHourStatistics workHourStatistics) {
        List<WorkHourStatistics> list = workHourStatisticsService.selectList(workHourStatistics);
        ExcelUtil<WorkHourStatistics> util = new ExcelUtil<WorkHourStatistics>(WorkHourStatistics.class);
        util.exportExcel(response, list, "工时统计数据");
    }

    @Operation(summary = "获取详细信息")
    @RequiresPermissions("workhour:statistics:query")
    @GetMapping(value = "/{id}")
    public R<WorkHourStatistics> getInfo(@PathVariable("id") Long id) {
        return R.ok(workHourStatisticsService.selectById(id));
    }

    @Operation(summary = "新增")
    @RequiresPermissions("workhour:statistics:add")
    @Log(title = "工时统计", businessType = BusinessType.INSERT)
    @PostMapping
    public R<Boolean> add(@RequestBody @Validated WorkHourStatistics workHourStatistics) {
        return R.ok(workHourStatisticsService.insert(workHourStatistics));
    }

    @Operation(summary = "修改")
    @RequiresPermissions("workhour:statistics:edit")
    @Log(title = "工时统计", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<Boolean> edit(@RequestBody @Validated WorkHourStatistics workHourStatistics) {
        return R.ok(workHourStatisticsService.update(workHourStatistics));
    }

    @Operation(summary = "删除")
    @RequiresPermissions("workhour:statistics:remove")
    @Log(title = "工时统计", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Boolean> remove(@PathVariable Long[] ids) {
        return R.ok(workHourStatisticsService.deleteByIds(ids));
    }

    @Operation(summary = "填报情况")
    @RequiresPermissions("workhour:statistics:list")
    @GetMapping("/fill-record/page")
    public TableDataInfo<FillRecordVO> pageFillRecord(@ParameterObject SearchDTO dto) {
        startPage();
        List<FillRecordVO> list = workHourStatisticsService.pageFillRecord(dto);
        return getDataTable(list);
    }

    @GetMapping("/async-data")
    public R<Void> asyncData() {
        workHourStatisticsService.asyncData();
        return R.ok();
    }
}
