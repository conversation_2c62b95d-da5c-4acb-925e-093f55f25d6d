package com.wbyy.pms.modules.contract.nodeplan.controller;

import com.alibaba.fastjson2.JSON;
import com.wbyy.common.core.utils.TreeUtil;
import com.wbyy.pms.modules.contract.nodeplan.domain.dto.SaveNodePlanDto;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;

import java.util.List;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.wbyy.common.core.domain.R;
import com.wbyy.common.log.annotation.Log;
import com.wbyy.common.log.enums.BusinessType;
import com.wbyy.common.security.annotation.RequiresPermissions;
import com.wbyy.pms.modules.contract.nodeplan.domain.ContractNodePlan;
import com.wbyy.pms.modules.contract.nodeplan.service.IContractNodePlanService;
import com.wbyy.common.core.web.controller.BaseController;
import com.wbyy.common.core.utils.poi.ExcelUtil;
import com.wbyy.common.core.web.page.TableDataInfo;

/**
 * 合同节点与任务关联Controller
 * 
 * <AUTHOR>
 * @date 2025-06-25
 */
@RestController
@RequiredArgsConstructor
@Tag(name = "合同节点与任务关联")
@RequestMapping("/contract/node/plan")
public class ContractNodePlanController extends BaseController
{

    private final IContractNodePlanService contractNodePlanService;

    @Operation(summary = "分页列表")
    @RequiresPermissions("contract:nodeplan:list")
    @GetMapping("/page")
    public TableDataInfo<ContractNodePlan> page(@ParameterObject ContractNodePlan contractNodePlan)
    {
        startPage();
        List<ContractNodePlan> list = contractNodePlanService.selectList(contractNodePlan);
        return getDataTable(list);
    }


    @Operation(summary = "任务关联列表")
    @RequiresPermissions("contract:nodeplan:list")
    @GetMapping("/list")
    public R<List<ContractNodePlan>> list(@RequestParam("contractId")Long contractId){
        List<ContractNodePlan> result = contractNodePlanService.list(contractId);
        List<ContractNodePlan> tree = TreeUtil.buildTree(JSON.parseArray(JSON.toJSONString(result)), ContractNodePlan.class);
        return R.ok(tree);
    }

    @Operation(summary = "导出列表")
    @RequiresPermissions("contract:nodeplan:export")
    @Log(title = "合同节点与任务关联", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ContractNodePlan contractNodePlan)
    {
        List<ContractNodePlan> list = contractNodePlanService.selectList(contractNodePlan);
        ExcelUtil<ContractNodePlan> util = new ExcelUtil<ContractNodePlan>(ContractNodePlan.class);
        util.exportExcel(response, list, "合同节点与任务关联数据");
    }

    @Operation(summary = "获取详细信息")
    @RequiresPermissions("contract:nodeplan:query")
    @GetMapping(value = "/{id}")
    public R<ContractNodePlan> getInfo(@PathVariable("id") Long id)
    {
        return R.ok(contractNodePlanService.selectById(id));
    }

    @Operation(summary = "新增")
    @RequiresPermissions("contract:nodeplan:add")
    @Log(title = "合同节点与任务关联", businessType = BusinessType.INSERT)
    @PostMapping
    public R<Boolean> add(@RequestBody @Validated ContractNodePlan contractNodePlan)
    {
        return R.ok(contractNodePlanService.insert(contractNodePlan));
    }

    @Operation(summary = "修改")
    @RequiresPermissions("contract:nodeplan:edit")
    @Log(title = "合同节点与任务关联", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<Boolean> edit(@RequestBody @Validated ContractNodePlan contractNodePlan)
    {
        return R.ok(contractNodePlanService.update(contractNodePlan));
    }

    @Operation(summary = "删除")
    @RequiresPermissions("contract:nodeplan:remove")
    @Log(title = "合同节点与任务关联", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public R<Boolean> remove(@PathVariable Long[] ids)
    {
        contractNodePlanService.deleteByIds(ids);
        return R.ok(true);
    }

    @Operation(summary = "新增合同节点与任务的关联")
    @RequiresPermissions("contract:nodeplan:add")
    @Log(title = "合同节点与任务关联", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    public R<Boolean> saveNew(@RequestBody @Validated SaveNodePlanDto dto){
        contractNodePlanService.saveNew(dto);
        return R.ok(true);
    }
}
