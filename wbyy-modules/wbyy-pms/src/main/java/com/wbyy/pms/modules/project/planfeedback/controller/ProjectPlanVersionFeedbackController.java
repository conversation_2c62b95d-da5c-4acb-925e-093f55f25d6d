package com.wbyy.pms.modules.project.planfeedback.controller;

import com.wbyy.common.core.domain.R;
import com.wbyy.common.core.utils.poi.ExcelUtil;
import com.wbyy.common.core.web.controller.BaseController;
import com.wbyy.common.core.web.page.TableDataInfo;
import com.wbyy.common.log.annotation.Log;
import com.wbyy.common.log.enums.BusinessType;
import com.wbyy.common.security.annotation.RequiresPermissions;
import com.wbyy.pms.modules.project.planfeedback.domain.ProjectPlanVersionFeedback;
import com.wbyy.pms.modules.project.planfeedback.service.IProjectPlanVersionFeedbackService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 项目任务反馈-版本Controller
 *
 * <AUTHOR>
 * @date 2025-05-13
 */
@RestController
@RequiredArgsConstructor
@Tag(name = "项目任务反馈-版本")
@RequestMapping("/project/plan/version/feedback")
public class ProjectPlanVersionFeedbackController extends BaseController
{

    private final IProjectPlanVersionFeedbackService projectPlanVersionFeedbackService;

    @Operation(summary = "分页列表")
    @RequiresPermissions("project:planFeedback:list")
    @GetMapping("/page")
    public TableDataInfo<ProjectPlanVersionFeedback> page(@ParameterObject ProjectPlanVersionFeedback projectPlanVersionFeedback)
    {
        startPage();
        List<ProjectPlanVersionFeedback> list = projectPlanVersionFeedbackService.selectList(projectPlanVersionFeedback);
        return getDataTable(list);
    }

    @Operation(summary = "导出列表")
    @RequiresPermissions("project:planFeedback:export")
    @Log(title = "项目任务反馈-版本", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ProjectPlanVersionFeedback projectPlanVersionFeedback)
    {
        List<ProjectPlanVersionFeedback> list = projectPlanVersionFeedbackService.selectList(projectPlanVersionFeedback);
        ExcelUtil<ProjectPlanVersionFeedback> util = new ExcelUtil<ProjectPlanVersionFeedback>(ProjectPlanVersionFeedback.class);
        util.exportExcel(response, list, "项目任务反馈-版本数据");
    }

    @Operation(summary = "获取详细信息")
    @RequiresPermissions("project:planFeedback:query")
    @GetMapping(value = "/{id}")
    public R<ProjectPlanVersionFeedback> getInfo(@PathVariable("id") Long id)
    {
        return R.ok(projectPlanVersionFeedbackService.selectById(id));
    }

    @Operation(summary = "新增")
    @RequiresPermissions("project:planFeedback:add")
    @Log(title = "项目任务反馈-版本", businessType = BusinessType.INSERT)
    @PostMapping
    public R<Boolean> add(@RequestBody @Validated ProjectPlanVersionFeedback projectPlanVersionFeedback)
    {
        return R.ok(projectPlanVersionFeedbackService.insert(projectPlanVersionFeedback));
    }

    @Operation(summary = "修改")
    @RequiresPermissions("project:planFeedback:edit")
    @Log(title = "项目任务反馈-版本", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<Boolean> edit(@RequestBody @Validated ProjectPlanVersionFeedback projectPlanVersionFeedback)
    {
        return R.ok(projectPlanVersionFeedbackService.update(projectPlanVersionFeedback));
    }

    @Operation(summary = "删除")
    @RequiresPermissions("project:planFeedback:remove")
    @Log(title = "项目任务反馈-版本", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public R<Boolean> remove(@PathVariable Long[] ids)
    {
        return R.ok(projectPlanVersionFeedbackService.deleteByIds(ids));
    }
}
