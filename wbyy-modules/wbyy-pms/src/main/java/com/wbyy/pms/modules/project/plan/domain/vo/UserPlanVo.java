package com.wbyy.pms.modules.project.plan.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * @author: 王金都
 * @date: 2025/6/5 15:02
 */
@Data
public class UserPlanVo {

    @Schema(description = "项目id")
    private Long projectId;

    @Schema(description = "项目名称")
    private String projectName;

    @Schema(description = "任务id")
    private String planId;

    @Schema(description = "任务名称,/分割的全任务路径名称")
    private String planName;

    @Schema(description = "任务计划完成时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date planEndDate;

    @Schema(description = "项目编码")
    private String projectNumber;
}
