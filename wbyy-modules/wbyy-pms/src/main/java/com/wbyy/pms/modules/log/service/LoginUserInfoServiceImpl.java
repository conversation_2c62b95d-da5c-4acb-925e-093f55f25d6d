package com.wbyy.pms.modules.log.service;

import cn.hutool.core.collection.CollectionUtil;
import com.wbyy.common.security.utils.SecurityUtils;
import com.wbyy.log.model.UserInfo;
import com.wbyy.log.service.UserInfoService;
import com.wbyy.system.api.domain.ApiDept;
import com.wbyy.system.api.domain.ApiUser;
import com.wbyy.system.api.model.LoginUser;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@Slf4j
public class LoginUserInfoServiceImpl implements UserInfoService {

    /**
     * 获取用户信息
     */
    @Override
    public UserInfo getUserInfo(){
        UserInfo userInfo = new UserInfo();
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if (null==loginUser) return userInfo;
        ApiUser sysUser = loginUser.getSysUser();
        if (null==sysUser) return userInfo;
        userInfo.setUserId(sysUser.getUserId().toString());
        userInfo.setUserName(sysUser.getUserName());
        userInfo.setWorkNumber(sysUser.getWorkNumber());
        List<ApiDept> depts = sysUser.getDepts();
        if (CollectionUtil.isEmpty(depts)) return userInfo;
        ApiDept dept = depts.get(0);
        userInfo.setDeptId(dept.getDeptId().toString());
        userInfo.setDeptName(dept.getDeptName());
        return userInfo;
    }
}
