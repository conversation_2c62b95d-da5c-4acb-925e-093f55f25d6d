package com.wbyy.pms.common.constant;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
public interface PMSConst {

    /**
     * 工作日工时 单位小时
     */
    BigDecimal WORKING_HOURS_OF_DAY = BigDecimal.valueOf(7.5);

    /**
     * 一天24小时
     */
    BigDecimal HOURS_OF_DAY = new BigDecimal("24");

    /**
     * 一个小时60分钟
     */
    BigDecimal MINUTES_OF_HOUR = new BigDecimal("60");

    /**
     * 午休90分钟
     */
    long NOON_REST_MINUTES = 90L;

    /**
     * 暂存操作
     */
    int OPERATION_STORE = 1;

    /**
     * 提交操作
     */
    int OPERATION_SUBMIT = 2;
}
