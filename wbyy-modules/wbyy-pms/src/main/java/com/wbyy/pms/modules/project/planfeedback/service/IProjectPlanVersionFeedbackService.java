package com.wbyy.pms.modules.project.planfeedback.service;

import java.util.List;
import com.baomidou.mybatisplus.extension.service.IService;
import com.wbyy.pms.modules.project.planfeedback.domain.ProjectPlanVersionFeedback;

/**
 * 项目任务反馈-版本Service接口
 *
 * <AUTHOR>
 * @date 2025-05-13
 */
public interface IProjectPlanVersionFeedbackService  extends IService<ProjectPlanVersionFeedback> {
    /**
     * 查询项目任务反馈-版本
     *
     * @param id 项目任务反馈-版本主键
     * @return 项目任务反馈-版本
     */
    ProjectPlanVersionFeedback selectById(Long id);

    /**
     * 查询项目任务反馈-版本列表
     *
     * @param projectPlanVersionFeedback 项目任务反馈-版本
     * @return 项目任务反馈-版本集合
     */
    List<ProjectPlanVersionFeedback> selectList(ProjectPlanVersionFeedback projectPlanVersionFeedback);

    /**
     * 新增项目任务反馈-版本
     *
     * @param projectPlanVersionFeedback 项目任务反馈-版本
     * @return 结果
     */
    boolean insert(ProjectPlanVersionFeedback projectPlanVersionFeedback);

    /**
     * 修改项目任务反馈-版本
     *
     * @param projectPlanVersionFeedback 项目任务反馈-版本
     * @return 结果
     */
    boolean update(ProjectPlanVersionFeedback projectPlanVersionFeedback);

    /**
     * 批量删除项目任务反馈-版本
     *
     * @param ids 需要删除的项目任务反馈-版本主键集合
     * @return 结果
     */
    boolean deleteByIds(Long[] ids);

}
