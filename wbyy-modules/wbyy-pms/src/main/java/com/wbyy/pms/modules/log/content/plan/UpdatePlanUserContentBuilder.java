package com.wbyy.pms.modules.log.content.plan;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.wbyy.log.builder.IContentBuilder;
import com.wbyy.log.model.LogRecord;
import com.wbyy.pms.modules.project.planuser.domain.ProjectPlanSnapshotUser;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * @author: 王金都
 * @date: 2025/7/7 17:07
 */
@Slf4j
public class UpdatePlanUserContentBuilder implements IContentBuilder<List<ProjectPlanSnapshotUser>> {
    @Override
    public String buildContent(LogRecord logRecord, List<ProjectPlanSnapshotUser> originalData, List<ProjectPlanSnapshotUser> newData) {
        if (CollectionUtil.isEmpty(newData)&&CollectionUtil.isEmpty(originalData)){
            log.warn("任务分配生成日志时，数据为空");
            return "";
        }
        // 日志对象是任务
        if (CollectionUtil.isNotEmpty(originalData)){
            ProjectPlanSnapshotUser data = originalData.get(0);
            logRecord.setDataModule(data.getProjectPlanName());
            logRecord.setDataModuleId(data.getProjectPlanId().toString());
        }else if (CollectionUtil.isNotEmpty(newData)){
            ProjectPlanSnapshotUser data = newData.get(0);
            logRecord.setDataModule(data.getProjectPlanName());
            logRecord.setDataModuleId(data.getProjectPlanId().toString());
        }
        String userName = "【" + logRecord.getUserName() + "(" + logRecord.getWorkNumber() + ")】";
        StringBuilder content = new StringBuilder();
        if (CollectionUtil.isEmpty(originalData)){
            content.append("将任务责任人");
        }
        for (ProjectPlanSnapshotUser data : originalData) {
            if (StrUtil.isNotBlank(content)){
                content.append("、");
            }else {
                content.append("将");
            }
            content.append("责任人【")
                    .append(data.getRealName())
                    .append("(")
                    .append(data.getWorkNumber())
                    .append(")")
                    .append("】计划工时(天)【")
                    .append(data.getWorkHour().toPlainString())
                    .append("】");
        }
        if (CollectionUtil.isEmpty(newData)){
            content.append("清空");
        }else {
            content.append("调整为");
        }
        StringBuilder newDataContent = new StringBuilder();
        for (ProjectPlanSnapshotUser data : newData) {
            if (StrUtil.isNotBlank(newDataContent)){
                newDataContent.append("、");
            }
            newDataContent.append("责任人【")
                    .append(data.getRealName())
                    .append("(")
                    .append(data.getWorkNumber())
                    .append(")")
                    .append("】计划工时(天)【")
                    .append(data.getWorkHour().toPlainString())
                    .append("】");
        }
        return userName+content+newDataContent;
    }
}
