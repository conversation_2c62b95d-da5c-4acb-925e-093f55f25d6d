package com.wbyy.pms.modules.configuration.basicparameter.roledept.service.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Arrays;

import cn.hutool.core.collection.CollectionUtil;
import com.wbyy.common.core.utils.SpringUtils;
import com.wbyy.biz.common.drag.service.IBaseVxeService;
import com.wbyy.biz.common.drag.service.impl.BaseVxeServiceImpl;
import com.wbyy.pms.modules.configuration.basicparameter.role.service.IProjectRoleService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import com.wbyy.pms.modules.configuration.basicparameter.roledept.mapper.ProjectRoleDeptMapper;
import com.wbyy.pms.modules.configuration.basicparameter.roledept.domain.ProjectRoleDept;
import com.wbyy.pms.modules.configuration.basicparameter.roledept.service.IProjectRoleDeptService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.transaction.annotation.Transactional;

/**
 * 项目角色与部门关联Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-04-22
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ProjectRoleDeptServiceImpl extends BaseVxeServiceImpl<ProjectRoleDeptMapper, ProjectRoleDept> implements IProjectRoleDeptService
{
    private final ProjectRoleDeptMapper projectRoleDeptMapper;
    private final IProjectRoleService projectRoleService;

    @Override
    public IBaseVxeService<ProjectRoleDept> getProxy() {
        return SpringUtils.getBean(IProjectRoleDeptService.class);
    }

    @Override
    public LambdaQueryWrapper<ProjectRoleDept> getWrapper(ProjectRoleDept entity) {
        return new LambdaQueryWrapper<ProjectRoleDept>().eq(ProjectRoleDept::getDeptId,entity.getDeptId());
    }

    /**
     * 查询项目角色与部门关联
     * 
     * @param id 项目角色与部门关联主键
     * @return 项目角色与部门关联
     */
    @Override
    public ProjectRoleDept selectProjectRoleDeptById(Long id)
    {
        return this.getById(id);
    }

    /**
     * 查询项目角色与部门关联列表
     * 
     * @param projectRoleDept 项目角色与部门关联
     * @return 项目角色与部门关联
     */
    @Override
    public List<ProjectRoleDept> selectProjectRoleDeptList(ProjectRoleDept projectRoleDept)
    {
        return this.baseMapper.list(projectRoleDept.getDeptId(), projectRoleDept.getProjectRoleDisable());
    }

    /**
     * 新增项目角色与部门关联
     * 
     * @param projectRoleDept 项目角色与部门关联
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean insertProjectRoleDept(ProjectRoleDept projectRoleDept)
    {
        Long deptId = projectRoleDept.getDeptId();
        this.delectByDeptId(deptId);
        List<ProjectRoleDept> saveList = new ArrayList<>();
        int sort = 1;
        for (Long projectRoleId : projectRoleDept.getProjectRoleIds()) {
            ProjectRoleDept saveItem = new ProjectRoleDept();
            saveItem.setDeptId(deptId);
            saveItem.setProjectRoleId(projectRoleId);
            saveItem.setSort(sort++);
            saveList.add(saveItem);
        }
        if (CollectionUtil.isNotEmpty(saveList)){
            return this.saveBatch(saveList);
        }else {
            return false;
        }

    }

    private void delectByDeptId(Long deptId){
        LambdaQueryWrapper<ProjectRoleDept> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ProjectRoleDept::getDeptId,deptId);
        this.remove(queryWrapper);
    }

    /**
     * 修改项目角色与部门关联
     * 
     * @param projectRoleDept 项目角色与部门关联
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateProjectRoleDept(ProjectRoleDept projectRoleDept)
    {
        return this.updateById(projectRoleDept);
    }

    /**
     * 批量删除项目角色与部门关联
     * 
     * @param ids 需要删除的项目角色与部门关联主键
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteProjectRoleDeptByIds(Long[] ids)
    {
        if (null!=ids&&ids.length>0){
            ProjectRoleDept byId = this.getById(ids[0]);
            if (null==byId) return true;
            Long deptId = byId.getDeptId();
            this.removeBatchByIds(Arrays.asList(ids));
            ProjectRoleDept dto = new ProjectRoleDept();
            dto.setDeptId(deptId);
            List<ProjectRoleDept> exists = this.selectProjectRoleDeptList(dto);
            if (CollectionUtil.isNotEmpty(exists)){
                int sort = 1;
                for (ProjectRoleDept exist : exists) {
                    exist.setSort(sort++);
                }
                this.updateBatchById(exists);
            }

        }

        return true;
    }

    @Override
    public ProjectRoleDept getProjectPmRole(Long deptId) {
        return this.baseMapper.getProjectPmRole(deptId);
    }

}
