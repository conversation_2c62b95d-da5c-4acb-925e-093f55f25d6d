package com.wbyy.pms.modules.project.info.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.wbyy.log.annotation.LogField;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.wbyy.common.core.annotation.Excel;
import com.wbyy.orm.core.domain.BaseAuditEntity;

import java.io.Serial;

/**
 * 项目信息-仿制药BE临床研究中心对象 project_info_crcgdbe
 * 
 * <AUTHOR>
 * @date 2025-05-06
 */
@Data
@TableName("project_info_crcgdbe")
@Schema(description = "项目信息-仿制药BE临床研究中心实体类")
@EqualsAndHashCode(callSuper = true)
public class ProjectInfoCrcgdbe extends BaseAuditEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /** 主键，与项目基础信息表id关联 */
    @Schema(description = "主键，与项目基础信息表id关联（新增：不传；修改：必传）")
    private Long id;

    /** 申办方 */
    @Excel(name = "申办方")
    @Schema(description = "申办方")
    @LogField(name = "申办方")
    private String sponsor;

    /** 申办方联系人和联系方式 */
    @Excel(name = "申办方联系人和联系方式")
    @Schema(description = "申办方联系人和联系方式")
    @LogField(name = "申办方联系人和联系方式")
    private String sponsorContact;

    /** 剂型（口服溶液，胶囊，片剂，缓释片，控释片，贴剂，凝胶贴膏，肠溶片，肠溶胶囊，吸入制剂，乳膏剂、软膏剂，缓释胶囊，颗粒） */
    @Excel(name = "剂型", readConverterExp = "口=服溶液，胶囊，片剂，缓释片，控释片，贴剂，凝胶贴膏，肠溶片，肠溶胶囊，吸入制剂，乳膏剂、软膏剂，缓释胶囊，颗粒")
    @Schema(description = "剂型（口服溶液，胶囊，片剂，缓释片，控释片，贴剂，凝胶贴膏，肠溶片，肠溶胶囊，吸入制剂，乳膏剂、软膏剂，缓释胶囊，颗粒）")
    @LogField(name = "剂型")
    private String dosageForm;

    /** 临床机构 */
    @Excel(name = "临床机构")
    @Schema(description = "临床机构")
    @LogField(name = "临床机构")
    private String clinicalInstitution;

    /** 试验设计（空腹两周期餐后两周期，空腹两周期餐后三周期，空腹两周期餐后四周期，空腹三周期餐后两周期，空腹三周期餐后三周期，空腹三周期餐后四周期，空腹四周期餐后两周期，空腹四周期餐后三周期，空腹四周期餐后四周期，空腹平行餐后平行，空腹两周期，空腹三周期，空腹四周期，餐后两周期，餐后三周期，餐后四周期） */
    @Excel(name = "试验设计", readConverterExp = "空=腹两周期餐后两周期，空腹两周期餐后三周期，空腹两周期餐后四周期，空腹三周期餐后两周期，空腹三周期餐后三周期，空腹三周期餐后四周期，空腹四周期餐后两周期，空腹四周期餐后三周期，空腹四周期餐后四周期，空腹平行餐后平行，空腹两周期，空腹三周期，空腹四周期，餐后两周期，餐后三周期，餐后四周期")
    @Schema(description = "试验设计（空腹两周期餐后两周期，空腹两周期餐后三周期，空腹两周期餐后四周期，空腹三周期餐后两周期，空腹三周期餐后三周期，空腹三周期餐后四周期，空腹四周期餐后两周期，空腹四周期餐后三周期，空腹四周期餐后四周期，空腹平行餐后平行，空腹两周期，空腹三周期，空腹四周期，餐后两周期，餐后三周期，餐后四周期）")
    @LogField(name = "试验设计")
    private String experimentDesign;

    /** 受试者例数 */
    @Excel(name = "受试者例数")
    @Schema(description = "受试者例数")
    @LogField(name = "受试者例数")
    private String subjectCount;

    /** 检测单位，默认安徽万邦医药科技股份有限公司 */
    @Excel(name = "检测单位，默认安徽万邦医药科技股份有限公司")
    @Schema(description = "检测单位，默认安徽万邦医药科技股份有限公司")
    @LogField(name = "检测单位")
    private String testUnit;

    /** 数据管理单位，默认安徽本奥医学科技有限公司 */
    @Excel(name = "数据管理单位，默认安徽本奥医学科技有限公司")
    @Schema(description = "数据管理单位，默认安徽本奥医学科技有限公司")
    @LogField(name = "数据管理单位")
    private String dataManageUnit;

    /** 统计分析单位，默认安徽本奥医学科技有限公司 */
    @Excel(name = "统计分析单位，默认安徽本奥医学科技有限公司")
    @Schema(description = "统计分析单位，默认安徽本奥医学科技有限公司")
    @LogField(name = "统计分析单位")
    private String statisticalAnalysisUnit;

    /** 项目概况 */
    @Excel(name = "项目概况")
    @Schema(description = "项目概况")
    @LogField(name = "项目概况")
    private String remark;

    /** 删除标志（0代表存在 1代表删除） */
    @Schema(description = "删除标志（0代表存在 1代表删除）")
    @TableLogic
    private Integer delFlag;


    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("sponsor", getSponsor())
            .append("sponsorContact", getSponsorContact())
            .append("dosageForm", getDosageForm())
            .append("clinicalInstitution", getClinicalInstitution())
            .append("experimentDesign", getExperimentDesign())
            .append("subjectCount", getSubjectCount())
            .append("testUnit", getTestUnit())
            .append("dataManageUnit", getDataManageUnit())
            .append("statisticalAnalysisUnit", getStatisticalAnalysisUnit())
            .append("remark", getRemark())
            .append("delFlag", getDelFlag())
            .append("createBy", getCreateBy())
            .append("createNameBy", getCreateNameBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateNameBy", getUpdateNameBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
