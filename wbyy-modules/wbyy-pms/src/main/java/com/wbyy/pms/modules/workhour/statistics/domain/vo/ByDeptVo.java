package com.wbyy.pms.modules.workhour.statistics.domain.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * @author: 王金都
 * @date: 2025/6/5 15:50
 */
@Data
@Schema(description = "根据部门统计")
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ByDeptVo {
    @Schema(description = "部门名称")
    private String deptName;

    @Schema(description = "部门id")
    private Long deptId;

    @Schema(description = "父级部门id")
    private Long deptParentId;

    @Schema(description = "填报人数")
    private Long fillCount;

    @Schema(description = "标准工时")
    private BigDecimal standardHour;

    @Schema(description = "填报总工时")
    private BigDecimal fillHour;

    @Schema(description = "填报项目总工时")
    private BigDecimal fillProjectHour;

    @Schema(description = "填报公共条目总工时")
    private BigDecimal fillCommonHour;

    @Schema(description = "项目总工时占比")
    private BigDecimal projectHourPercent;

    @Schema(description = "子部门数据")
    private List<ByDeptVo> children;
}
