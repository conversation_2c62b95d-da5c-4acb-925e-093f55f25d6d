package com.wbyy.pms.modules.listener;

import com.rabbitmq.client.Channel;
import com.wbyy.pms.modules.contract.nodeplan.service.IContractNodePlanService;
import com.wbyy.pms.modules.project.planrelate.service.IProjectPlanSnapshotRelateService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;

import java.util.List;

import static com.wbyy.pms.common.constant.RabbitmqConst.PLAN_ACTIVE_SYNC_CONTRACT_QUEUE;


/**
 * 计划生效后，修改合同关联任务的删除标志
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class PlanEffectiveChangeContractConsumer {

    private final IProjectPlanSnapshotRelateService projectPlanSnapshotRelateService;
    private final IContractNodePlanService contractNodePlanService;


    @RabbitListener(queues = PLAN_ACTIVE_SYNC_CONTRACT_QUEUE)
    public void consume(Message message, String msg, Channel channel) {
        log.info("队列：{}, 消息: {} -- {}", PLAN_ACTIVE_SYNC_CONTRACT_QUEUE, message.toString(), msg);

        List<Long> deletedPlanIds = projectPlanSnapshotRelateService.getDeletedPlanIds(msg);
        contractNodePlanService.updateStatus(deletedPlanIds);
    }
}
