package com.wbyy.pms.modules.contract.base.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wbyy.common.core.utils.PageUtils;
import com.wbyy.common.datascope.annotation.BizDataScope;
import com.wbyy.common.security.utils.SecurityUtils;
import com.wbyy.pms.modules.contract.base.domain.ContractBase;
import com.wbyy.pms.modules.contract.base.domain.dto.ContractBaseDTO;
import com.wbyy.pms.modules.contract.base.domain.vo.BaseTotalVO;
import com.wbyy.pms.modules.contract.base.mapper.ContractBaseMapper;
import com.wbyy.pms.modules.contract.base.service.IContractBaseService;
import com.wbyy.pms.modules.contract.contractnode.domain.ContractNode;
import com.wbyy.pms.modules.contract.contractnode.service.IContractNodeService;
import com.wbyy.pms.modules.project.base.service.IProjectBaseService;
import com.wbyy.pms.modules.project.teamperson.service.IProjectTeamPersonService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 合同基本信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-25
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ContractBaseServiceImpl extends ServiceImpl<ContractBaseMapper, ContractBase> implements IContractBaseService
{
    private final ContractBaseMapper contractBaseMapper;
    private final IProjectTeamPersonService projectTeamPersonService;
    private final IProjectBaseService projectBaseService;
    private final IContractNodeService contractNodeService;

    private IContractBaseService contractBaseService;

    @Autowired
    @Lazy
    public void setContractBaseService(IContractBaseService contractBaseService) {
        this.contractBaseService = contractBaseService;
    }

    /**
     * 查询合同基本信息
     * 
     * @param id 合同基本信息主键
     * @return 合同基本信息
     */
    @Override
    public ContractBase selectById(Long id)
    {
        ContractBase byId = this.getById(id);
        List<Long> pmByNumbers = projectTeamPersonService.findPmByNumbers(byId.getGroupContractNumber(), byId.getCompanyContractNumber());
        byId.setProjectManagerIdStr(CollUtil.join(pmByNumbers, ","));
        return byId;
    }

    /**
     * 查询合同基本信息列表
     * 
     * @param dto 合同基本信息
     * @return 合同基本信息
     */
    @Override
    public List<ContractBase> selectList(ContractBaseDTO dto) {
        Long userId = SecurityUtils.getUserId();
        List<String> companyContractNumbers = this.getCompanyContractNumbers(userId);
        List<ContractBase> contractBases;
        PageUtils.startPage();
        if (CollUtil.isNotEmpty(companyContractNumbers)) {
            contractBases = baseMapper.selectByStatusAndType(dto, companyContractNumbers);
        }else {
            contractBases = contractBaseService.findByPermissions(dto);
        }
        if (CollUtil.isEmpty(contractBases)) return contractBases;
        Map<Long, BigDecimal> paidAmountMap = contractNodeService.findByContractIds(contractBases.stream().map(ContractBase::getId)
                .collect(Collectors.toList())).stream().collect(Collectors.groupingBy(ContractNode::getContractId,
                Collectors.reducing(BigDecimal.ZERO, ContractNode::getPaidAmount, BigDecimal::add)));
        contractBases.forEach(base -> {
            if (paidAmountMap.containsKey(base.getId())) {
                BigDecimal paidAmount = paidAmountMap.get(base.getId());
                base.setPaidAmount(paidAmount);
                base.setPaidProgress(paidAmount.divide(base.getContractAmount(),2, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100)));
            }else {
                base.setPaidProgress(BigDecimal.ZERO);
            }
        });
        return contractBases;
    }

    /**
     * 根据权限过滤
     *
     * @param dto 入参
     * @return 合同
     */
    @Override
    @BizDataScope(deptAlias = "temp", userAlias = "temp")
    public List<ContractBase> findByPermissions(ContractBaseDTO dto) {
        return baseMapper.selectByStatusAndType(dto, null);
    }

    /**
     * 合同列表的总计
     * @param dto 合同基本信息
     * @return 总计
     */
    @Override
    public BaseTotalVO baseTotal(ContractBaseDTO dto) {
        Long userId = SecurityUtils.getUserId();
        List<String> companyContractNumbers = this.getCompanyContractNumbers(userId);
        if (CollUtil.isNotEmpty(companyContractNumbers)) {
            return baseMapper.baseTotal(dto, companyContractNumbers);
        }else {
            return contractBaseService.totalByPermissions(dto);
        }
    }

    private List<String> getCompanyContractNumbers(Long userId) {
        List<Long> projectIds = projectTeamPersonService.findPmProjectIdsByUserId(userId);
        return CollUtil.isNotEmpty(projectIds) ? projectBaseService.findCompanyContractNumbers(projectIds): List.of();
    }

    @Override
    @BizDataScope(deptAlias = "temp", userAlias = "temp")
    public BaseTotalVO totalByPermissions(ContractBaseDTO dto) {
        return baseMapper.baseTotal(dto, null);
    }

    @Override
    public ContractBase getByNumber(String groupContractNumber, String companyContractNumber) {
        LambdaQueryWrapper<ContractBase> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ContractBase::getGroupContractNumber, groupContractNumber);
        queryWrapper.eq(ContractBase::getCompanyContractNumber, companyContractNumber);
        return this.getOne(queryWrapper, false);
    }

    /**
     * 新增合同基本信息
     * 
     * @param contractBase 合同基本信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean insert(ContractBase contractBase)
    {
        return this.save(contractBase);
    }

    /**
     * 修改合同基本信息
     * 
     * @param contractBase 合同基本信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean update(ContractBase contractBase)
    {
        return this.updateById(contractBase);
    }

    /**
     * 批量删除合同基本信息
     * 
     * @param ids 需要删除的合同基本信息主键
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByIds(Long[] ids)
    {
        return this.removeBatchByIds(Arrays.asList(ids));
    }

}
