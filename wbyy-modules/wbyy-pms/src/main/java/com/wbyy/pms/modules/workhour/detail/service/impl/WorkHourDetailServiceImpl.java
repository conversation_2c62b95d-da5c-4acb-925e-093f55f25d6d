package com.wbyy.pms.modules.workhour.detail.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wbyy.common.core.exception.ServiceException;
import com.wbyy.common.core.utils.DateUtils;
import com.wbyy.common.rabbitmq.service.RabbitService;
import com.wbyy.common.redis.helper.SimpleLockHelper;
import com.wbyy.common.redis.service.RedisService;
import com.wbyy.common.security.utils.SecurityUtils;
import com.wbyy.log.annotation.OperationLog;
import com.wbyy.log.enums.BizIdFieldIndexEnum;
import com.wbyy.pms.common.constant.PMSConst;
import com.wbyy.pms.common.constant.lockkey.LockKeyOfWorkHourConst;
import com.wbyy.pms.common.enums.WorkHourStatusEnum;
import com.wbyy.pms.modules.log.content.workHour.BatchWorkHourContentBuilder;
import com.wbyy.pms.modules.log.content.workHour.RemoveWorkHourDetailContentBuilder;
import com.wbyy.pms.modules.log.content.workHour.SubmitWorkHourContentBuilder;
import com.wbyy.pms.modules.log.content.workHour.UpdateWorkHourContentBuilder;
import com.wbyy.pms.modules.project.planuser.domain.ProjectPlanReleaseUser;
import com.wbyy.pms.modules.project.planuser.service.IProjectPlanReleaseUserService;
import com.wbyy.pms.modules.configuration.weekofyear.service.IWeekOfYearService;
import com.wbyy.pms.modules.workhour.approval.domain.WorkHourApproval;
import com.wbyy.pms.modules.workhour.approval.service.IWorkHourApprovalService;
import com.wbyy.pms.modules.workhour.detail.domain.WorkHourDetail;
import com.wbyy.pms.modules.workhour.detail.domain.dto.OperationBatchDto;
import com.wbyy.pms.modules.workhour.detail.domain.dto.UserSubmitDto;
import com.wbyy.pms.modules.workhour.detail.domain.vo.FillPlanHourVo;
import com.wbyy.pms.modules.workhour.detail.domain.vo.RemainWorkHourVO;
import com.wbyy.pms.modules.workhour.detail.mapper.WorkHourDetailMapper;
import com.wbyy.pms.modules.workhour.detail.service.IWorkHourDetailService;
import com.wbyy.pms.modules.workhour.entry.domain.WorkHourEntry;
import com.wbyy.pms.modules.workhour.entry.service.IWorkHourEntryService;
import com.wbyy.system.api.UserApi;
import com.wbyy.system.api.model.LoginUser;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.wbyy.common.core.constant.Constants.NO_DELETE;
import static com.wbyy.pms.common.constant.PMSConst.WORKING_HOURS_OF_DAY;
import static com.wbyy.pms.common.constant.RabbitmqConst.WORK_HOUR_EXCHANGE;
import static com.wbyy.pms.common.constant.RabbitmqConst.WORK_HOUR_FILLED_KEY;

/**
 * 工时详细内容Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-05
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WorkHourDetailServiceImpl extends ServiceImpl<WorkHourDetailMapper, WorkHourDetail> implements IWorkHourDetailService {
    private final WorkHourDetailMapper workHourDetailMapper;

    private final IProjectPlanReleaseUserService projectPlanReleaseUserService;
    private final IWeekOfYearService weekOfYearService;

    private IWorkHourEntryService workHourEntryService;
    private IWorkHourApprovalService workHourApprovalService;
    private final RabbitService rabbitService;

    private final UserApi userApi;
    private final SimpleLockHelper simpleLockHelper;
    private final TransactionTemplate transactionTemplate;

    private final RedisService redisService;

    // 工时状态过滤集合
    private final static Set<Integer> STATUS_SET = Set.of(WorkHourStatusEnum.WAIT_APPROVAL.getCode(), WorkHourStatusEnum.PASS.getCode(), WorkHourStatusEnum.ARCHIVE.getCode());

    @Lazy
    @Autowired
    public void setWorkHourApprovalService(IWorkHourApprovalService workHourApprovalService) {
        this.workHourApprovalService = workHourApprovalService;
    }

    @Lazy
    @Autowired
    public void setWorkHourEntryService(IWorkHourEntryService workHourEntryService) {
        this.workHourEntryService = workHourEntryService;
    }

    /**
     * 查询工时详细内容
     *
     * @param id 工时详细内容主键
     * @return 工时详细内容
     */
    @Override
    public WorkHourDetail selectById(Long id) {
        return this.getById(id);
    }

    /**
     * 查询工时详细内容列表
     *
     * @param workHourDetail 工时详细内容
     * @return 工时详细内容
     */
    @Override
    public List<WorkHourDetail> selectList(WorkHourDetail workHourDetail) {
        LambdaQueryWrapper<WorkHourDetail> wrapper = Wrappers.lambdaQuery();
        wrapper.orderByDesc(WorkHourDetail::getCreateTime);
        return this.list(wrapper);
    }

    /**
     * 新增工时详细内容
     *
     * @param workHourDetail 工时详细内容
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean insert(WorkHourDetail workHourDetail) {
        return this.save(workHourDetail);
    }

    /**
     * 修改工时详细内容
     *
     * @param workHourDetail 工时详细内容
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean update(WorkHourDetail workHourDetail) {
        return this.updateById(workHourDetail);
    }

    /**
     * 批量删除工时详细内容
     *
     * @param ids 需要删除的工时详细内容主键
     * @return 结果
     */
    @Override
    @OperationLog(name = "删除工时",
    type = "工时",
    withBiz = true,
    bizIdField = "projectId",
    originalDataService = IWorkHourDetailService.class,
    originalDataMethod = "listPlanEntryDetailRelationByIds",
    objectIdField = "planId",
    objectNameField = "entryName",
    objectClazz = WorkHourDetail.class,
    contentBuilder = RemoveWorkHourDetailContentBuilder.class)
    public List<WorkHourDetail> deleteByIds(List<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return List.of();
        }
        List<WorkHourDetail> data = this.listByIds(ids);
        if (CollUtil.isEmpty(data)) {
            return List.of();
        }
        WorkHourDetail workHourDetail = data.get(0);
        String lockKey = String.format(LockKeyOfWorkHourConst.WORK_HOUR_OPER, workHourDetail.getYearNumber(),
                workHourDetail.getWeekNumber(), SecurityUtils.getUserId());
        return simpleLockHelper.execute(lockKey, 10L, 30L, TimeUnit.SECONDS, () -> {
            transactionTemplate.execute(status -> {
                List<WorkHourDetail> list = deleteDetailByIds(ids);
                if (CollUtil.isEmpty(list)) {
                    return true;
                }
                //判断是否需要删除条目，该条目只使用一次即能删除
                List<Long> entryIds = baseMapper.selectObjs(
                        Wrappers.<WorkHourDetail>lambdaQuery().select(WorkHourDetail::getEntryId)
                                .in(WorkHourDetail::getEntryId, list.stream().map(WorkHourDetail::getEntryId).collect(Collectors.toSet()))
                                .groupBy(WorkHourDetail::getEntryId)
                                .having("count(1) = 1"));
                // 删除条目
                if (CollUtil.isNotEmpty(entryIds)) {
                    workHourEntryService.removeByIds(entryIds);
                }
                return List.of();
            });
            return List.of();
        });
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<WorkHourDetail> deleteDetailByIds(List<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return List.of();
        }
        List<WorkHourDetail> list = this.list(Wrappers.<WorkHourDetail>lambdaQuery().in(WorkHourDetail::getId, ids)
                .in(WorkHourDetail::getStatus, Arrays.asList(WorkHourStatusEnum.STAGED.getCode(),
                        WorkHourStatusEnum.REJECTED.getCode(), WorkHourStatusEnum.WAIT_APPROVAL.getCode()))
                .eq(WorkHourDetail::getDelFlag, NO_DELETE));

        if (ids.size() != list.size()) {
            throw new ServiceException("不能删除审核通过的数据");
        }
        //删除待审核记录
        workHourApprovalService.deleteByWorkHourDetailIds(list.stream().map(WorkHourDetail::getId).collect(Collectors.toList()));
        //删除工时内容
        this.removeBatchByIds(ids);
        return list;
    }

    /**
     * 根据条目id，用户id查询待审核和审核通过工时
     *
     * @param planIds    计划id
     * @param userIds    用户id
     * @param statusList 状态列表
     * @return 已填报工时
     */
    @Override
    public List<WorkHourDetail> findByPlanIdsAndUserIds(Collection<Long> planIds,
                                                        Collection<Long> userIds,
                                                        Collection<Integer> statusList) {
        if (CollUtil.isEmpty(userIds) && CollUtil.isEmpty(planIds) && CollUtil.isEmpty(statusList)) {
            return List.of();
        }
        return baseMapper.selectByPlanIdsAndUserIdsAndStatus(planIds, userIds, statusList);
    }

    @Override
    public List<WorkHourDetail> findByYearAndWeekAndUserId(String yearNumber, Integer weekNumber, Long userId) {
        if (StrUtil.isEmpty(yearNumber) || weekNumber == null || userId == null) {
            return List.of();
        }

        LambdaQueryWrapper<WorkHourDetail> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(WorkHourDetail::getYearNumber, yearNumber)
                .eq(WorkHourDetail::getWeekNumber, weekNumber)
                .eq(WorkHourDetail::getCreateBy, userId);

        List<WorkHourDetail> list = this.list(wrapper);
        // 翻译条目名称
        if (CollUtil.isNotEmpty(list)) {
            List<WorkHourEntry> entries = workHourEntryService.listByIds(list.stream().map(WorkHourDetail::getEntryId).collect(Collectors.toList()));
            Map<Long, String> entryNameMap = entries.stream().collect(Collectors.toMap(WorkHourEntry::getId, WorkHourEntry::getEntryName));
            list.forEach(item -> item.setEntryName(entryNameMap.get(item.getEntryId())));
        }
        return list;
    }

    @Override
    public List<WorkHourDetail> findByEntryIds(Collection<Long> entryIds) {
        if (CollUtil.isEmpty(entryIds)) {
            return List.of();
        }
        LambdaQueryWrapper<WorkHourDetail> wrapper = Wrappers.lambdaQuery();
        wrapper.in(WorkHourDetail::getEntryId, entryIds);
        List<WorkHourDetail> list = this.list(wrapper);
        // 翻译条目名称
        if (CollUtil.isNotEmpty(list)) {
            List<WorkHourEntry> entries = workHourEntryService.listByIds(entryIds);
            Map<Long, String> entryNameMap = entries.stream().collect(Collectors.toMap(WorkHourEntry::getId, WorkHourEntry::getEntryName));
            list.forEach(item -> item.setEntryName(entryNameMap.get(item.getEntryId())));
        }
        return list;
    }

    @Override
    public BigDecimal getRemainWorkHours(Long planId) {
        Long userId = SecurityUtils.getUserId();
        // 查询计划工时（小时）
        BigDecimal planWorkHour = getPlanWorkHour(userId, planId);
        // 查询已填报的工时 （小时）
        BigDecimal fillWorkHour = getFillWorkHours(planId, userId,
                List.of(WorkHourStatusEnum.PASS.getCode(), WorkHourStatusEnum.WAIT_APPROVAL.getCode(), WorkHourStatusEnum.STAGED.getCode(), WorkHourStatusEnum.ARCHIVE.getCode()));
        return planWorkHour.subtract(fillWorkHour).setScale(1, RoundingMode.HALF_UP);
    }

    /**
     * 查询计划工时（小时）
     *
     * @return
     */
    private BigDecimal getPlanWorkHour(Long userId, Long planId) {
        List<ProjectPlanReleaseUser> dutyUserInfo = projectPlanReleaseUserService.findByProjectPlanIdsAndUserIds(List.of(planId), List.of(userId));
        if (CollUtil.isEmpty(dutyUserInfo)) {
            throw new ServiceException("暂无任务计划信息，请确认当前任务是否已被删除");
        }
        // 计划工时
        BigDecimal planWorkHour = dutyUserInfo.stream()
                .map(ProjectPlanReleaseUser::getWorkHour).reduce(BigDecimal::add)
                .orElse(BigDecimal.ZERO);
        if (planWorkHour.compareTo(BigDecimal.ZERO) == 0) {
            throw new ServiceException("暂无任务计划信息，请确认当前任务是否已被删除");
        }
        // 转为小时
        planWorkHour = planWorkHour.multiply(WORKING_HOURS_OF_DAY);
        return planWorkHour;
    }

    @Override
    public RemainWorkHourVO getRemainWorkHours(Long planId, Date theDay) {
        Long userId = SecurityUtils.getUserId();
        // 查询计划工时（小时）
        BigDecimal planWorkHour = getPlanWorkHour(userId, planId);

        // 累计填报数据
        List<WorkHourDetail> cumulativeReportingList = findByPlanIdsAndUserIds(List.of(planId), List.of(userId),
                List.of(WorkHourStatusEnum.PASS.getCode(),
                        WorkHourStatusEnum.STAGED.getCode(),
                        WorkHourStatusEnum.WAIT_APPROVAL.getCode(),
                        WorkHourStatusEnum.ARCHIVE.getCode()
                ));
        // 累计填报工时
        BigDecimal cumulativeReportingWorkHour = cumulativeReportingList.stream().map(item ->
                        Optional.ofNullable(item.getWorkHour()).orElse(BigDecimal.ZERO))
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        // 当前计划当前日期，已填写工时
        BigDecimal thisPlanWorkHour = cumulativeReportingList.stream().filter(item -> item.getFillDate().compareTo(theDay) == 0).map(item -> item.getWorkHour()).findAny().orElse(
                BigDecimal.ZERO
        );

        // 剩余工时
        BigDecimal remainderWorkHour = planWorkHour.subtract(cumulativeReportingWorkHour);

        return RemainWorkHourVO.builder()
                .planWorkHour(planWorkHour)
                .cumulativeReportingWorkHour(cumulativeReportingWorkHour)
                .remainderWorkHour(remainderWorkHour)
                .thisPlanWorkHour(thisPlanWorkHour)
                .build();
    }

    /**
     * 查询已填报的工时 （小时）
     *
     * @param planId     计划id
     * @param userId     用户id
     * @param statusList 状态列表
     * @return 已填报的工时（小时）
     */
    private BigDecimal getFillWorkHours(Long planId, Long userId, List<Integer> statusList) {
        LambdaQueryWrapper<WorkHourDetail> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(WorkHourDetail::getPlanId, planId)
                .eq(WorkHourDetail::getCreateBy, userId)
                .in(WorkHourDetail::getStatus, statusList);
        List<WorkHourDetail> workHourDetails = this.list(wrapper);
        return workHourDetails.stream().map(item -> Optional.of(item.getWorkHour()).orElse(BigDecimal.ZERO))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }


    @Override
    public boolean checkFill(String yearNumber, Integer weekNumber) {
        Long userId = SecurityUtils.getUserId();
        return this.baseMapper.countByYearNumberByWeekNumberByUserId(yearNumber, weekNumber, userId) > 0;
    }

    /**
     * @param userId
     * @param fillDate
     * @Description: 填报时间校验
     * @return: void
     * @author: 王金都
     * @Date: 2025/6/16 9:45
     */
    private void fillDateCheck(Long userId, Date fillDate) {
        // 检查时间是不是大于今天
        if (DateUtil.compare(new Date(), fillDate) < 0) {
            throw new ServiceException("不能填报今天之后的工时");
        }
        // 入职时间
        Date hireDate = redisService.getHireDate(userId);
        if (null != hireDate && hireDate.after(fillDate)) {
            throw new ServiceException("不可填报入职时间之前的工时");
        }
        // 离职时间
        Date departureDate = redisService.getDepartureDate(userId);
        if (null != departureDate && departureDate.before(fillDate)) {
            throw new ServiceException("不可填报离职时间之后的工时");
        }
    }

    @Override
    @OperationLog(name = "编辑工时",
    type = "工时",
    withBiz = true,
    bizIdField = "projectId",
    bizIdFieldIndex = BizIdFieldIndexEnum.NEW_DATA,
    objectClazz = WorkHourDetail.class,
    objectIdField = "planId",
    objectNameField = "entryName",
    contentBuilder = UpdateWorkHourContentBuilder.class
    )
    public WorkHourDetail tempStore(WorkHourDetail dto) {
        Date fillDate = dto.getFillDate();
        Long userId = SecurityUtils.getUserId();
        // 入离职时间校验
        this.fillDateCheck(userId, fillDate);
        Long entryId = dto.getEntryId();
        // 校验条目
        WorkHourEntry entry = workHourEntryService.getById(entryId);
        if (null == entry) {
            throw new ServiceException("填报的工时条目不存在");
        }
        dto.setEntryName(entry.getEntryName());
        dto.setProjectId(entry.getProjectId());
        Long planId = entry.getPlanId();
        // 存一下计划id
        dto.setPlanId(planId);
        // 工时不能小于0
        if (BigDecimal.ZERO.compareTo(dto.getWorkHour()) > 0) {
            throw new ServiceException("填报工时不能小于0");
        }
        String yearNumber = dto.getYearNumber();
        Integer weekNumber = dto.getWeekNumber();
        String lockKey = String.format(LockKeyOfWorkHourConst.WORK_HOUR_OPER, yearNumber, weekNumber, userId);
        return simpleLockHelper.execute(lockKey, 10L, 30L, TimeUnit.SECONDS, () -> {
            // 这个用户这周的条目
            List<Long> entryIds = workHourEntryService.idsByUserIdByYearAndWeek(userId, yearNumber, weekNumber);
            if (CollectionUtil.isEmpty(entryIds)) {
                throw new ServiceException("用户本周未添加条目");
            }
            // 查库中是否有数据,这一年这一周这一天这个用户填的工时
            List<WorkHourDetail> dayDetails = this.getByUserIdByYearNumberByWeekNumberByFillDate(userId, yearNumber, weekNumber, fillDate, entryIds);
            // 是否有这一条目的数据
            WorkHourDetail exist = dayDetails.stream().filter(item -> entryId.equals(item.getEntryId()))
                    .findFirst().orElse(null);
            if (null!=exist){
                dto.setOriginalWorkHour(exist.getWorkHour());
            }
            // 工时输入0时
            if (BigDecimal.ZERO.compareTo(dto.getWorkHour()) == 0) {
                if (null == exist) {
                    // 输入工时为0，且库中没有填写过的数据，直接返回不处理
                    return dto;
                }
                // 库中有数据，删除
                this.removeById(exist.getId());
                return dto;
            }
            // 校验这一天工时是否大于24小时
            checkFullHour(dto, exist, dayDetails);
            // 校验是否超期
            dto.setOverTime(checkOverTime(dto, planId, exist));
            if (exist != null) {
                // 检验库里的状态
                if (WorkHourStatusEnum.PASS.getCode().equals(exist.getStatus())) {
                    throw new ServiceException("该天该条目工时已提交，不可修改");
                }
                if (WorkHourStatusEnum.WAIT_APPROVAL.getCode().equals(exist.getStatus())) {
                    throw new ServiceException("该天该条目工时审核中，不可修改");
                }
                dto.setCreateBy(userId);
                // 注意，这里是编码式数据库操作
                this.baseMapper.tempStore(dto);
            } else {
                // 登录用户数据填充
                Optional.ofNullable(SecurityUtils.getLoginUser())
                        .ifPresent(user -> {
                            dto.setRealName(user.getRealName());
                            dto.setWorkNumber(user.getWorkNumber());
                            dto.setDeptId(SecurityUtils.getDeptId(user));
                        });
                dto.setStatus(WorkHourStatusEnum.STAGED.getCode());
                this.save(dto);
            }
            return dto;
        });
    }

    /**
     * @param dto
     * @param planId
     * @param exist
     * @Description: 校验是否超期
     * @return: boolean
     * @author: 王金都
     * @Date: 2025/6/11 15:24
     */
    private boolean checkOverTime(WorkHourDetail dto, Long planId, WorkHourDetail exist) {
        // planId不为空标识任务条目工时 需要检查是否超过计划工时，超过的话工时描述必填
        if (null == planId) return false;
        // 还能填的工时
        BigDecimal remainWorkHour = this.getRemainWorkHours(planId);
        // 前面填过数据了，把老工时减掉
        if (null != exist) {
            BigDecimal oldWorkHour = exist.getWorkHour();
            remainWorkHour = remainWorkHour.add(oldWorkHour);
        }
        if (remainWorkHour.compareTo(dto.getWorkHour()) < 0) {
            if (StrUtil.isBlank(dto.getDescription())) {
                throw new ServiceException("累计填报工时超出计划工时，请填写本次工时描述及超出原因！");
            }
            return true;
        } else {
            return false;
        }
    }

    /**
     * @param dto
     * @param exist
     * @param dayDetails
     * @Description: 校验一天是否填满
     * @return: void
     * @author: 王金都
     * @Date: 2025/6/11 15:23
     */
    private void checkFullHour(WorkHourDetail dto, WorkHourDetail exist, List<WorkHourDetail> dayDetails) {
        BigDecimal dayTotal = dayDetails.stream().map(WorkHourDetail::getWorkHour)
                .reduce(BigDecimal.ZERO, BigDecimal::add)
                .add(dto.getWorkHour());
        // 库中有数据了，把老数据剪掉
        if (null != exist) {
            dayTotal = dayTotal.subtract(exist.getWorkHour());
        }
        if (dayTotal.compareTo(PMSConst.HOURS_OF_DAY) > 0) {
            throw new ServiceException("一天填报工时不得大于24小时");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @OperationLog(name = "提交工时",
    type = "工时",
    withBiz = true,
    objectClazz = WorkHourEntry.class,
    contentBuilder = SubmitWorkHourContentBuilder.class)
    public List<WorkHourEntry> submit(UserSubmitDto dto) {
        // 查询工时详情
        Long userId = SecurityUtils.getUserId();
        List<WorkHourDetail> workHourDetailList = findByYearAndWeekAndUserId(dto.getYearNumber(), dto.getWeekNumber(), userId);

        // 取出暂存的数据
        List<WorkHourDetail> tempStoreList = workHourDetailList.stream()
                .filter(item -> WorkHourStatusEnum.STAGED.getCode().equals(item.getStatus())).toList();

        // 提交前，数据校验
        checkSubmitData(userId, dto, workHourDetailList, tempStoreList);


        if (CollUtil.isEmpty(tempStoreList)) {
            return null;
        }
        // 超期的数据，需要保存审核流程记录
        List<WorkHourDetail> overTimeList = tempStoreList.stream()
                .filter(WorkHourDetail::getOverTime).toList();
        if (CollUtil.isNotEmpty(overTimeList)) {
            // 查询条超期的工时数据对应的条目数据
            List<WorkHourEntry> workHourEntryList =
                    workHourEntryService.selectListByIds(overTimeList.stream()
                            .map(WorkHourDetail::getEntryId).collect(Collectors.toSet()));
            Map<Long, WorkHourEntry> idAndEntryMap = workHourEntryList.stream()
                    .collect(Collectors.toMap(WorkHourEntry::getId, Function.identity(), (o1, o2) -> o1));

            // 保存审核流程记录
            List<WorkHourApproval> workHourApprovalList = new ArrayList<>();
            overTimeList.forEach(workHourDetail -> {
                workHourDetail.setStatus(WorkHourStatusEnum.WAIT_APPROVAL.getCode());
                WorkHourEntry workHourEntry = idAndEntryMap.get(workHourDetail.getEntryId());
                workHourApprovalList.add(
                        WorkHourApproval.builder()
                                .workHourDetailId(workHourDetail.getId())
                                .projectId(workHourEntry.getProjectId())
                                .projectName(workHourEntry.getProjectName())
                                .projectNumber(workHourEntry.getProjectNumber())
                                .planId(workHourEntry.getPlanId())
                                .entryName(workHourEntry.getEntryName())
                                .status(WorkHourStatusEnum.WAIT_APPROVAL.getCode())
                                .workHour(workHourDetail.getWorkHour())
                                .fillDate(workHourDetail.getFillDate())
                                .description(workHourDetail.getDescription())
                                .realName(workHourDetail.getRealName())
                                .workNumber(workHourDetail.getWorkNumber())
                                .build()
                );
            });
            // 修改工时填报状态
            this.updateBatchById(overTimeList);

            // 保存审核记录
            workHourApprovalService.saveBatch(workHourApprovalList);
        }
        // 未超期的
        List<WorkHourDetail> unOverTimeList = tempStoreList.stream()
                .filter(item -> !item.getOverTime()).toList();
        // 查条目
        List<Long> entryIds = tempStoreList.stream().map(WorkHourDetail::getEntryId).toList();
        List<WorkHourEntry> list = CollectionUtil.isNotEmpty(entryIds) ? workHourEntryService.listByIds(entryIds) : List.of();
        if (CollUtil.isEmpty(unOverTimeList)) {
            return list;
        }
        // 批量修改
        unOverTimeList.forEach(item ->
                item.setStatus(WorkHourStatusEnum.PASS.getCode()));
        this.updateBatchById(unOverTimeList);
        // 默认通过发送消息
        rabbitService.sendMessage(WORK_HOUR_EXCHANGE, WORK_HOUR_FILLED_KEY, JSON.toJSONString(unOverTimeList.stream().map(WorkHourDetail::getId).collect(Collectors.toSet())));
        return list;
    }

    /**
     * 提交前，数据校验
     *
     * @param userId             用户ID
     * @param dto                提交参数
     * @param workHourDetailList 工时详情
     * @param tempStoreList      暂存数据
     */
    private void checkSubmitData(Long userId, UserSubmitDto dto,
                                 List<WorkHourDetail> workHourDetailList,
                                 List<WorkHourDetail> tempStoreList) {
        if (CollUtil.isEmpty(workHourDetailList)) {
            throw new ServiceException("暂无提交数据");
        }
        // 超期的是否填写描述
        List<WorkHourDetail> overTimeList = workHourDetailList.stream()
                .filter(WorkHourDetail::getOverTime).toList();
        if (CollUtil.isNotEmpty(overTimeList)) {
            List<String> errorMsg = new ArrayList<>();
            overTimeList.forEach(workHourDetail -> {
                if (StrUtil.isEmpty(workHourDetail.getDescription())) {
                    errorMsg.add(workHourDetail.getEntryName() + "(" + DateUtils.parseToStr(workHourDetail.getFillDate())+ ")") ;
                }
            });
            // 构造错误描述
            makeErrorMsg(errorMsg, "1");
        }

        // 驳回的数据
        List<WorkHourDetail> rejectList = workHourDetailList.stream()
                .filter(item -> WorkHourStatusEnum.REJECTED.getCode().equals(item.getStatus())).toList();

        // 检查工时详情，是否包含驳回数据，如果有，报错提示
        if (CollUtil.isNotEmpty(rejectList)) {
            List<String> errorMsg = new ArrayList<>();
            rejectList.forEach(workHourDetail -> {
                if (Objects.equals(workHourDetail.getStatus(), WorkHourStatusEnum.PASS.getCode())) {
                    errorMsg.add(workHourDetail.getEntryName() + "(" + DateUtils.parseToStr(workHourDetail.getFillDate()) + ")");
                }
            });
            // 构造错误描述
            makeErrorMsg(errorMsg, "2");
        }

        // 判断上周是否提交，如果上周没有提交，则不允许提交

        // 上周对应的年度
        int yearNumber = Integer.parseInt(dto.getYearNumber());
        // 上周
        Integer weekNumber = dto.getWeekNumber();

        if (weekNumber == 1) {
            yearNumber = yearNumber - 1;
            // 查询上一年的最后一周
            weekNumber = weekOfYearService.getEndWeekNumberByYear(yearNumber);
        } else {
            weekNumber = weekNumber - 1;
        }
        if (weekNumber == null) {
            return;
        }
        List<WorkHourDetail> lastWorkHourDetailList =
                findByYearAndWeekAndUserId(yearNumber + "", weekNumber, userId);
        if (CollUtil.isEmpty(lastWorkHourDetailList)) {
            return;
        }
        List<WorkHourDetail> unSubmitList = lastWorkHourDetailList.stream()
                .filter(item ->
                        Objects.equals(item.getStatus(), WorkHourStatusEnum.REJECTED.getCode())
                                || Objects.equals(item.getStatus(), WorkHourStatusEnum.STAGED.getCode())).toList();
        if (CollUtil.isNotEmpty(unSubmitList))
            throw new ServiceException(
                    String.format("请先提交上周(第%s周)工时后，再提交本周工时！", weekNumber));

        // 判断本周暂时的数据，加上历史已填报的工期，是否超出计划工期
        if (CollUtil.isNotEmpty(tempStoreList)) {
            Set<Long> planIds = tempStoreList.stream().filter(item -> item.getPlanId() != null)
                    .map(WorkHourDetail::getPlanId).collect(Collectors.toSet());

            if(CollUtil.isEmpty(planIds))
                return;
            // 查询本次提交的项目工时，对应的项目任务的计划工时
            List<ProjectPlanReleaseUser> projectPlanReleaseUserList =
                    projectPlanReleaseUserService.findByProjectPlanIdsAndUserIds(planIds, List.of(userId));
            // 查询本次提交的项目工时，对应项目任务已填报的总工时(提交、审核中、归档)
            List<WorkHourDetail> oldFillList = findByPlanIdsAndUserIds(planIds, List.of(userId), List.of(
                    WorkHourStatusEnum.PASS.getCode(),
                    WorkHourStatusEnum.WAIT_APPROVAL.getCode(),
                    WorkHourStatusEnum.ARCHIVE.getCode()
            ));

            // 计划任务和计划任务工时的Map
            Map<Long, BigDecimal> plaIdAndPlanWorkHourMap = projectPlanReleaseUserList.stream().collect(Collectors.toMap(
                    ProjectPlanReleaseUser::getProjectPlanId, item ->
                            item.getWorkHour() == null ? BigDecimal.ZERO : item.getWorkHour().multiply(WORKING_HOURS_OF_DAY)
            ));
            // 历史填报的计划任务 和 历史填报的工时  Map
            Map<Long, BigDecimal> fillPlaIdAndWorkHourMap = oldFillList.stream().collect(Collectors.groupingBy(WorkHourDetail::getPlanId,
                    Collectors.reducing(BigDecimal.ZERO, WorkHourDetail::getWorkHour, BigDecimal::add)
            ));
            List<String> errorMsg = new ArrayList<>();
            Map<Long, BigDecimal> cumulativeWorkHourMap = new HashMap<>();
            List<WorkHourDetail> list = tempStoreList.stream()
                    .filter(item -> item.getPlanId() != null).toList();

            if (CollUtil.isNotEmpty(list)) {
                list.stream()
                        .filter(item -> item.getPlanId() != null)
                        .sorted(Comparator.comparingLong(WorkHourDetail::getEntryId))
                        .forEach(item -> {
                            BigDecimal planWorkHour = plaIdAndPlanWorkHourMap.get(item.getPlanId());
                            BigDecimal fillWorkHour = fillPlaIdAndWorkHourMap.getOrDefault(item.getPlanId(),BigDecimal.ZERO);
                            if (cumulativeWorkHourMap.get(item.getPlanId()) != null) {
                                fillWorkHour = fillWorkHour.add(cumulativeWorkHourMap.get(item.getPlanId()));
                            }
                            cumulativeWorkHourMap.put(item.getPlanId(), fillWorkHour.add(item.getWorkHour()));

                            if (planWorkHour != null
                                    && cumulativeWorkHourMap.get(item.getPlanId()).compareTo(planWorkHour) >= 0
                                    && StrUtil.isEmpty(item.getDescription())) {
                                errorMsg.add(item.getEntryName() + "(" + DateUtils.parseToStr(item.getFillDate()) + ")");
                                cumulativeWorkHourMap.put(item.getPlanId(), cumulativeWorkHourMap.get(item.getPlanId()).add(item.getWorkHour()));
                            }
                        });
                if (CollUtil.isNotEmpty(errorMsg)) {
                    makeErrorMsg(errorMsg, "1");
                }
            }
        }
    }

    /**
     * 构造错误描述
     *
     * @param errorMsg 错误信息
     * @param type     提示类型，1：超期未填写描述，2：驳回，未重新填写
     */
    private static void makeErrorMsg(List<String> errorMsg, String type) {
        if (CollUtil.isEmpty(errorMsg)) {
            return;
        }
        String commonMsg;
        if ("1".equals(type)) {
            commonMsg = "已超出计划工时";
        } else {
            commonMsg = "已驳回";
        }
        String msg = StrUtil.join("</br>", errorMsg);
        if (errorMsg.size() == 1) {
            throw new ServiceException(StrUtil.format("你填报的工时条目“{}”，" + commonMsg + "，请重新填写或补充工时描述，再进行提交", msg));
        } else {
            throw new ServiceException(StrUtil.format("你填报的工时条目" + commonMsg + "：</br>{}</br>请重新填写或补充工时描述，再进行提交", msg));
        }
    }

    /**
     * @param userId
     * @param yearNumber
     * @param weekNumber
     * @param fillDate
     * @Description: 获取去用户这一年这一周这一天的工时数据
     * @return: WorkHourDetail
     * @author: 王金都
     * @Date: 2025/6/10 14:50
     */
    private List<WorkHourDetail> getByUserIdByYearNumberByWeekNumberByFillDate(Long userId,
                                                                               String yearNumber,
                                                                               Integer weekNumber,
                                                                               Date fillDate,
                                                                               List<Long> entryIds) {
        LambdaQueryWrapper<WorkHourDetail> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(WorkHourDetail::getFillDate, fillDate);
        wrapper.eq(WorkHourDetail::getYearNumber, yearNumber);
        wrapper.eq(WorkHourDetail::getWeekNumber, weekNumber);
        wrapper.eq(WorkHourDetail::getCreateBy, userId);
        wrapper.in(WorkHourDetail::getEntryId, entryIds);
        return this.list(wrapper);
    }

    /**
     * 查询指定日期的填报工时信息
     *
     * @param theDay 日期，不传默认今天
     * @return 工时信息
     */
    @Override
    public List<WorkHourDetail> getDayWorkHours(Date theDay) {
        if (theDay == null) {
            theDay = new Date();
        }
        return baseMapper.selectListByFillDateAndUserId(theDay, SecurityUtils.getUserId());
    }

    @Override
    @OperationLog(name = "编辑工时",
    type = "工时",
    withBiz = true,
    bizIdField = "projectId",
    bizIdFieldIndex = BizIdFieldIndexEnum.NEW_DATA,
    objectClazz = WorkHourDetail.class,
    objectIdField = "planId",
    objectNameField = "entryName",
    contentBuilder = BatchWorkHourContentBuilder.class
    )
    public List<WorkHourDetail> operationBatch(OperationBatchDto dto) {
        // 操作类型校验
        Integer operationType = dto.getOperationType();
        boolean storeFlag = operationType.equals(PMSConst.OPERATION_STORE);
        boolean submitFlag = operationType.equals(PMSConst.OPERATION_SUBMIT);
        LoginUser loginUser = SecurityUtils.getLoginUser();
        Long userId = SecurityUtils.getUserId();
        String yearNumber = dto.getYearNumber();
        Integer weekNumber = dto.getWeekNumber();
        Date fillDate = dto.getFillDate();
        // 入离职时间校验
        this.fillDateCheck(userId, fillDate);
        List<WorkHourDetail> toOperations = getAndCheckRepeat(dto);
        Long deptId = SecurityUtils.getDeptId();
        // 基础数据填充
        toOperations.forEach(item -> {
            item.setYearNumber(yearNumber);
            item.setWeekNumber(weekNumber);
            item.setFillDate(fillDate);
            item.setDeptId(deptId);
            item.setWorkNumber(loginUser.getWorkNumber());
            item.setRealName(loginUser.getRealName());
        });
        // 本次填报的工时中任务条目的任务id
        List<Long> toOperationPlanIds = toOperations.stream().map(WorkHourDetail::getPlanId)
                .filter(Objects::nonNull)
                .toList();
        // 用户任务的计划工时
        Map<Long, BigDecimal> planHourMap = this.planHourMap(toOperationPlanIds, userId);
        String lockKey = String.format(LockKeyOfWorkHourConst.WORK_HOUR_OPER, yearNumber, weekNumber, userId);
        return simpleLockHelper.execute(lockKey, 10L, 30L, TimeUnit.SECONDS, () -> {
            // 库中已有的这次填报日期的数据
            List<WorkHourDetail> thisDayFilleds = this.baseMapper.selectFilled(yearNumber, weekNumber, fillDate, userId);
            // 转map project_id-plan_id-entry_name
            Map<String, WorkHourDetail> thisDayfilledMap = thisDayFilleds.stream()
                    .collect(Collectors.toMap(this::getKey, Function.identity(), (o1, o2) -> o1));
            // 这天的工时
            // 用户任务历史已填报的工时
            Map<Long, BigDecimal> fillPlanHourMap = this.fillPlanHourMap(toOperationPlanIds, userId);
            // 库中条目数据,防止有的条目没有填过工时，关联查询查不到
            List<WorkHourEntry> entries = workHourEntryService.listUserWeekEntry(yearNumber, weekNumber, userId);
            Map<String, WorkHourEntry> entryMap = entries.stream()
                    .collect(Collectors.toMap(item -> item.getProjectId() + StrUtil.DASHED + item.getPlanId() + StrUtil.DASHED + item.getEntryName(),
                            Function.identity(), (o1, o2) -> o1));
            BigDecimal dayHour = BigDecimal.ZERO;
            // 库中已存在的且处理过的工时
            Set<String> existHandledKeys = new HashSet<>();
            // 超时的工时
            Set<String> overTimeKeys = new HashSet<>();
            // 驳回数据忽略不操作
            Set<String> ingoreKeys = new HashSet<>();
            for (WorkHourDetail toOperation : toOperations) {
                String key = this.getKey(toOperation);
                // 获取已在库中存在的这个条目的工时
                WorkHourDetail thisDayFilled = thisDayfilledMap.get(key);
                // 这次填报库中的工时
                BigDecimal thisDayFilledHour = BigDecimal.ZERO;
                // 累加填报日的工时
                dayHour = dayHour.add(toOperation.getWorkHour());
                // 库中有这个条目的数据，校验状态是否正确
                if (null != thisDayFilled) {
                    // 基本数据填充
                    toOperation.setId(thisDayFilled.getId());
                    toOperation.setEntryId(thisDayFilled.getEntryId());
                    toOperation.setOriginalWorkHour(thisDayFilled.getWorkHour());
                    existHandledKeys.add(key);
                    thisDayFilledHour = thisDayFilled.getWorkHour();
                    Integer status = thisDayFilled.getStatus();
                    // 校验状态
                    this.checkStatus(storeFlag, submitFlag, status, toOperation.getEntryName());
                }
                // planId不为空认为是任务工时，校验是否超期
                Long planId = toOperation.getPlanId();
                if (null != planId) {
                    // 健壮性校验
                    if (null == toOperation.getProjectId() ||
                            StrUtil.isBlank(toOperation.getProjectName()) ||
                            StrUtil.isBlank(toOperation.getProjectNumber())) {
                        throw new ServiceException("填报任务工时时，项目信息必传");
                    }
                    // 已经填报的工时
                    BigDecimal filledHour = fillPlanHourMap.getOrDefault(planId, BigDecimal.ZERO)
                            // ,减掉这次填报的库中已存在的工时
                            .subtract(thisDayFilledHour)
                            // 加上这次提交的
                            .add(toOperation.getWorkHour());
                    // 计划填报工时
                    BigDecimal planHour = planHourMap.get(planId);
                    if (null == planHour)
                        throw new ServiceException("任务条目【" + toOperation.getEntryName() + "】未找到计划工时");
                    if (BigDecimal.ZERO.compareTo(planHour.subtract(filledHour)) > 0) {
                        if (StrUtil.isBlank(toOperation.getDescription())) {
                            throw new ServiceException("条目【" + toOperation.getEntryName() + "】累计填报工时超出计划工时，请填写本次工时描述及超出原因！");
                        }
                        toOperation.setOverTime(true);
                        // 暂存超时数据
                        overTimeKeys.add(key);
                    }else {
                        toOperation.setOverTime(false);
                    }
                }
            }
            // 工时不能超过24小时
            // 未处理的工时加上处理过的工时
            dayHour = thisDayFilleds.stream().filter(item -> !existHandledKeys.contains(this.getKey(item)))
                    .map(WorkHourDetail::getWorkHour).reduce(BigDecimal.ZERO, BigDecimal::add).add(dayHour);
            if (dayHour.compareTo(PMSConst.HOURS_OF_DAY) > 0) {
                throw new ServiceException("每天填报工时不得超过24小时");
            }
            // 去掉忽略数据
            List<WorkHourDetail> filteredList = toOperations.stream().filter(item -> !ingoreKeys.contains(this.getKey(item)))
                    .toList();
            // 需要新增的条目
            List<WorkHourEntry> saveEntries = new ArrayList<>();
            filteredList.stream()
                    .filter(item -> !existHandledKeys.contains(this.getKey(item)))
                    .toList().forEach(item -> {
                        String key = this.getKey(item);
                        WorkHourEntry entry = entryMap.get(key);
                        if (null == entry) {
                            long entryId = IdUtil.getSnowflakeNextId();
                            entry = this.buildEntry(entryId, item, loginUser);
                            saveEntries.add(entry);
                        }
                        item.setEntryId(entry.getId());
                    });

            return transactionTemplate.execute(status -> {
                if (CollectionUtil.isNotEmpty(saveEntries)) {
                    workHourEntryService.saveBatch(saveEntries);
                }
                // 查询条目
                List<Long> entryIds = filteredList.stream().map(WorkHourDetail::getEntryId)
                        .toList();
                List<WorkHourEntry> searchEntries = CollectionUtil.isNotEmpty(entryIds) ?
                        workHourEntryService.listByIds(entryIds) : List.of();
                Map<Long, WorkHourEntry> entryMapById = searchEntries.stream()
                        .collect(Collectors.toMap(WorkHourEntry::getId, Function.identity(),
                                (o1, o2) -> o1));
                // 暂存
                if (storeFlag) {
                    // 处理数据状态
                    filteredList.forEach(item -> {
                        item.setStatus(WorkHourStatusEnum.STAGED.getCode());
                    });
                    if (CollectionUtil.isNotEmpty(filteredList)) {
                        this.saveOrUpdateBatch(filteredList);
                    }
                }
                // 提交
                if (submitFlag) {
                    // 处理数据状态
                    for (WorkHourDetail toOperation : filteredList) {
                        String key = this.getKey(toOperation);
                        if (overTimeKeys.contains(key)) {
                            toOperation.setStatus(WorkHourStatusEnum.WAIT_APPROVAL.getCode());
                        } else {
                            toOperation.setStatus(WorkHourStatusEnum.PASS.getCode());
                        }
                    }
                    if (CollectionUtil.isNotEmpty(filteredList)) {
                        this.saveOrUpdateBatch(filteredList);
                    }
                    // 要审核的数据
                    List<WorkHourApproval> approvals = new ArrayList<>();
                    for (WorkHourDetail detail : filteredList) {
                        String key = this.getKey(detail);
                        if (!overTimeKeys.contains(key)) continue;
                        approvals.add(this.buildApproval(detail, loginUser));
                    }
                    if (CollectionUtil.isNotEmpty(approvals)) {
                        workHourApprovalService.saveBatch(approvals);
                        return filteredList.stream().peek(filterItem->{
                            filterItem.setBatchResult(false);
                            filterItem.setOperationType(operationType);
                            WorkHourEntry entry = entryMapById.get(filterItem.getEntryId());
                            if (null!=entry){
                                filterItem.setEntryName(entry.getEntryName());
                                filterItem.setProjectId(entry.getProjectId());
                                filterItem.setProjectNumber(entry.getProjectNumber());
                                filterItem.setProjectName(entry.getProjectName());
                            }
                        }).toList();
                    }
                }
                return filteredList.stream().peek(filterItem->{
                    filterItem.setBatchResult(true);
                    filterItem.setOperationType(operationType);
                    WorkHourEntry entry = entryMapById.get(filterItem.getEntryId());
                    if (null!=entry){
                        filterItem.setEntryName(entry.getEntryName());
                        filterItem.setProjectId(entry.getProjectId());
                        filterItem.setProjectNumber(entry.getProjectNumber());
                        filterItem.setProjectName(entry.getProjectName());
                    }
                }).toList();
            });
        });
    }

    /**
     * @Description: 获取待处理数据，并校验重复
       * @param dto
     * @return: List<WorkHourDetail>
     * @author: 王金都
     * @Date: 2025/6/20 17:17
     */
    private List<WorkHourDetail> getAndCheckRepeat(OperationBatchDto dto) {
        List<WorkHourDetail> toOperations = dto.getDetails();
        // 重复条目检查
        Set<String> scaned = new HashSet<>();
        for (WorkHourDetail toOperation : toOperations) {
            if (null!=toOperation.getPlanId()){
                String planIdStr = toOperation.getPlanId().toString();
                if (scaned.contains(planIdStr)){
                    throw new ServiceException("不可提交重复数据");
                }
                scaned.add(planIdStr);
            }else {
                String entryName = toOperation.getEntryName();
                if (scaned.contains(entryName)){
                    throw new ServiceException("不可提交重复数据");
                }
                scaned.add(entryName);
            }
        }
        return toOperations;
    }

    /**
     * @Description: 校验状态
     * @param storeFlag
     * @param submitFlag
     * @param status
     * @param entryName
     * @return: void
     * @author: 王金都
     * @Date: 2025/6/17 17:12
     */
    private void checkStatus(boolean storeFlag, boolean submitFlag, Integer status, String entryName) {
        // 只有暂存和被驳回数据能再次暂存
        if (storeFlag&&
                (!WorkHourStatusEnum.STAGED.getCode().equals(status)&&
                        !WorkHourStatusEnum.REJECTED.getCode().equals(status))){
            throw new ServiceException("条目【"+entryName
                    +"】状态为【"+WorkHourStatusEnum.getDescriptionByCode(status)+"】不可暂存");
        }
        // 只有暂存和被驳回数据能能提交
        if (submitFlag&&(!WorkHourStatusEnum.STAGED.getCode().equals(status)&&
                !WorkHourStatusEnum.REJECTED.getCode().equals(status))){
            throw new ServiceException("条目【"+entryName
                    +"】状态为【"+WorkHourStatusEnum.getDescriptionByCode(status)+"】不可提交");
        }
    }

    /**
     * @Description: 构建工时条目数据
     * @param entryId
     * @param item
     * @param loginUser
     * @return: WorkHourEntry
     * @author: 王金都
     * @Date: 2025/6/17 17:09
     */
    private WorkHourEntry buildEntry(Long entryId, WorkHourDetail item, LoginUser loginUser) {
        WorkHourEntry entry = new WorkHourEntry();
        entry.setId(entryId);
        entry.setProjectId(item.getProjectId());
        entry.setProjectName(item.getProjectName());
        entry.setProjectNumber(item.getProjectNumber());
        entry.setPlanId(item.getPlanId());
        entry.setEntryName(item.getEntryName());
        entry.setYearNumber(item.getYearNumber());
        entry.setWeekNumber(item.getWeekNumber());
        entry.setRealName(loginUser.getRealName());
        entry.setWorkNumber(loginUser.getWorkNumber());
        return entry;
    }

    /**
     * @Description: 初始化审批数据
     * @param detail
     * @param loginUser
     * @return: WorkHourApproval
     * @author: 王金都
     * @Date: 2025/6/17 17:04
     */
    private WorkHourApproval buildApproval(WorkHourDetail detail, LoginUser loginUser) {
        WorkHourApproval approval = new WorkHourApproval();
        approval.setWorkHourDetailId(detail.getId());
        approval.setProjectId(detail.getProjectId());
        approval.setProjectName(detail.getProjectName());
        approval.setProjectNumber(detail.getProjectNumber());
        approval.setPlanId(detail.getPlanId());
        approval.setEntryName(detail.getEntryName());
        approval.setStatus(WorkHourStatusEnum.WAIT_APPROVAL.getCode());
        approval.setWorkHour(detail.getWorkHour());
        approval.setFillDate(detail.getFillDate());
        approval.setDescription(detail.getDescription());
        approval.setRealName(loginUser.getRealName());
        approval.setWorkNumber(loginUser.getWorkNumber());
        return approval;
    }

    /**
     * @Description: 获取用户任务已填报的工时
     * @param planIds
     * @param userId
     * @return: Map<Long,BigDecimal>
     * @author: 王金都
     * @Date: 2025/6/17 15:15
     */
    private Map<Long, BigDecimal> fillPlanHourMap(List<Long> planIds, Long userId) {
        Map<Long, BigDecimal> result = new HashMap<>();
        if (CollectionUtil.isEmpty(planIds)) return result;
        List<FillPlanHourVo> hourVos = this.baseMapper.selectFillPlanHour(planIds, userId);
        for (FillPlanHourVo hourVo : hourVos) {
            result.put(hourVo.getPlanId(), hourVo.getWorkHour());
        }
        return result;
    }

    /**
     * @Description: 获取用户任务计划工时map
     * @param planIds
     * @param userId
     * @return: Map<Long,BigDecimal>
     * @author: 王金都
     * @Date: 2025/6/17 15:05
     */
    private Map<Long, BigDecimal> planHourMap(List<Long> planIds, Long userId) {
        Map<Long, BigDecimal> result = new HashMap<>();
        if (CollectionUtil.isEmpty(planIds)) return result;
        List<ProjectPlanReleaseUser> userPlans = projectPlanReleaseUserService.findByProjectPlanIdsAndUserIds(planIds, List.of(userId));
        for (ProjectPlanReleaseUser userPlan : userPlans) {
            result.put(userPlan.getProjectPlanId(), userPlan.getWorkHour().multiply(WORKING_HOURS_OF_DAY));
        }
        return result;
    }

    /**
     * @Description: 获取转mapkey
     * @param item
     * @return: String
     * @author: 王金都
     * @Date: 2025/6/17 14:17
     */
    private String getKey(WorkHourDetail item) {
        return item.getProjectId() + StrUtil.DASHED + item.getPlanId() + StrUtil.DASHED + item.getEntryName();
    }

    /**
     * 查询最近一天填报工时信息
     *
     * @param theDay 该日期之前，不传默认今天
     * @return 工时信息
     */
    @Override
    public List<WorkHourDetail> getLatestDayWorkHours(Date theDay) {
        if (theDay == null) {
            theDay = new Date();
        }
        Long userId = SecurityUtils.getUserId();
        List<WorkHourDetail> workHourDetails = baseMapper.selectLatestDayWorkHours(theDay, userId);
        if (CollUtil.isEmpty(workHourDetails)) return workHourDetails;
        // 待审核和审核通过的不覆盖
        Set<String> concatSet = baseMapper.selectListByFillDateAndUserId(theDay, userId)
                .stream().filter(item ->
                        STATUS_SET.contains(item.getStatus()))
                .map(item -> item.getEntryName() + item.getEntryId()).collect(Collectors.toSet());
        workHourDetails = workHourDetails.stream().filter(item -> !concatSet.contains(item.getEntryName()
                + item.getEntryId())).collect(Collectors.toList());
        // 填充超时信息
        List<Long> planIdList = workHourDetails.stream().map(WorkHourDetail::getPlanId).filter(Objects::nonNull).toList();
        // 用户任务的计划工时
        Map<Long, BigDecimal> planHourMap = this.planHourMap(planIdList, userId);
        // 已填报工时
        Map<Long, BigDecimal> fillHourMap = this.fillPlanHourMap(planIdList, userId);
        workHourDetails.stream().filter(item -> null != item.getPlanId()).forEach(item -> {
            if (planHourMap.containsKey(item.getPlanId())) {
                BigDecimal fillHour = fillHourMap.containsKey(item.getPlanId()) ? fillHourMap.get(item.getPlanId()).add(item.getWorkHour()) : item.getWorkHour();
                item.setOverTime(fillHour.compareTo(planHourMap.get(item.getPlanId())) > 0);
            }
        });
        return workHourDetails;
    }

    /**
     * 根据时间范围和用户id 返回范围内每天每人的填报情况
     *
     * @param userIds   用户id
     * @param startDate 时间范围
     * @param endDate   时间范围
     * @return 范围内每天每人的填报情况
     */
    @Override
    public List<WorkHourDetail> findByUserIdsAndTime(Collection<Long> userIds, Date startDate, Date endDate, Long projectId, Integer projectType) {
        if (CollectionUtil.isEmpty(userIds) || null == startDate || null == endDate) return List.of();
        return baseMapper.findByUserIdsAndTime(userIds, startDate, endDate, projectId, projectType);
    }

    @Override
    public List<WorkHourDetail> listPlanEntryDetailRelationByIds(List<Long> ids) {
        if(CollectionUtil.isEmpty(ids)){
            return List.of();
        }
        LambdaQueryWrapper<WorkHourDetail> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(WorkHourDetail::getId,ids);
        queryWrapper.isNotNull(WorkHourDetail::getPlanId);
        List<WorkHourDetail> list = this.list(queryWrapper);
        List<Long> entryIds = list.stream().map(WorkHourDetail::getEntryId).toList();
        if (CollectionUtil.isNotEmpty(entryIds)){
            List<WorkHourEntry> entries = workHourEntryService.listByIds(entryIds);
            list.forEach(item->{
                entries.stream().filter(entry->entry.getId().equals(item.getEntryId()))
                        .findFirst().ifPresent(entry->{
                            item.setEntryName(entry.getEntryName());
                            item.setProjectId(entry.getProjectId());
                            item.setProjectName(entry.getProjectName());
                            item.setProjectNumber(entry.getProjectNumber());
                        });
            });
        }
        return list;
    }
}
