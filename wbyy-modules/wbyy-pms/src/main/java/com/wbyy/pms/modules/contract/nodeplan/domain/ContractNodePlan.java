package com.wbyy.pms.modules.contract.nodeplan.domain;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.wbyy.pms.modules.project.plan.domain.vo.StatusVO;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.wbyy.common.core.annotation.Excel;
import com.wbyy.orm.core.domain.BaseAuditEntity;

import java.io.Serial;
import java.util.List;

/**
 * 合同节点与任务关联对象 contract_node_plan
 * 
 * <AUTHOR>
 * @date 2025-06-25
 */
@Data
@SuperBuilder
@NoArgsConstructor
@Accessors(chain = true)
@TableName(value = "contract_node_plan", autoResultMap = true)
@Schema(description = "合同节点与任务关联实体类")
@EqualsAndHashCode(callSuper = true)
public class ContractNodePlan extends BaseAuditEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /** 主键 */
    @Schema(description = "主键（新增：不传；修改：必传）")
    private Long id;

    @Schema(description = "合同主键id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "合同主键id不能为空")
    private Long contractId;

    @TableField(exist = false)
    private String contractName;
    /**
     * 父id
     */
    @Schema(description = "父id，渲染树用")
    @TableField(exist = false)
    private Long parentId;

    /**
     * 子数据
     */
    @Schema(description = "子数据，渲染树用")
    @TableField(exist = false)
    private List<ContractNodePlan> children;

    /** 合同节点id */
    @Excel(name = "合同节点id")
    @Schema(description = "合同节点id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "合同节点id不能为空")
    private Long contractNodeId;

    @TableField(exist = false)
    private String contractNodeName;

    /** 项目id */
    @Excel(name = "项目id")
    @Schema(description = "项目id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "项目id不能为空")
    private Long projectId;

    /** 项目名称 */
    @Excel(name = "项目名称")
    @Schema(description = "项目名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "项目名称不能为空")
    @Size(max = 100, message = "项目名称不能超过 100 个字符")
    private String projectName;

    /** 项目编码 */
    @Excel(name = "项目编码")
    @Schema(description = "项目编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "项目编码不能为空")
    @Size(max = 100, message = "项目编码不能超过 100 个字符")
    private String projectNumber;

    /** 任务id */
    @Excel(name = "任务id")
    @Schema(description = "任务id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "任务id不能为空")
    private Long planId;

    /** 任务名称 */
    @Excel(name = "任务名称")
    @Schema(description = "任务名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "任务名称不能为空")
    @Size(max = 256, message = "任务名称不能超过 256 个字符")
    private String planName;

    /** 任务责任人名称,多个逗号隔开 */
    @Excel(name = "任务责任人名称,多个逗号隔开")
    @Schema(description = "任务责任人名称,多个逗号隔开")
    @Size(max = 100, message = "任务责任人名称,多个逗号隔开不能超过 100 个字符")
    private String planUserRealNames;

    /** 计划完成日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "计划完成日期", width = 30, dateFormat = "yyyy-MM-dd")
    @Schema(description = "计划完成日期")
    private Date planEndDate;

    /** 实际完成日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "实际完成日期", width = 30, dateFormat = "yyyy-MM-dd")
    @Schema(description = "实际完成日期")
    @TableField(exist = false)
    private Date planRealityEndDate;

    @Schema(description = "进度")
    @TableField(exist = false)
    private Integer planRealityProgress;

    @Schema(description = "状态")
    @TableField(exist = false)
    private StatusVO status;

    /** 任务删除标识 0未删除 1删除 */
    @Excel(name = "任务删除标识 0未删除 1删除")
    @Schema(description = "任务删除标识 0未删除 1删除", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "任务删除标识 0未删除 1删除不能为空")
    private Integer planDelete;

    /** 删除标志（0代表存在 1代表删除） */
    @Schema(description = "删除标志（0代表存在 1代表删除）", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "任务删除标识 0未删除 1删除不能为空")
    @TableLogic
    private Integer delFlag;


    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("contractNodeId", getContractNodeId())
            .append("projectId", getProjectId())
            .append("projectName", getProjectName())
            .append("projectNumber", getProjectNumber())
            .append("planId", getPlanId())
            .append("planName", getPlanName())
            .append("planUserRealNames", getPlanUserRealNames())
            .append("planEndDate", getPlanEndDate())
            .append("planRealityEndDate", getPlanRealityEndDate())
            .append("planDelete", getPlanDelete())
            .append("delFlag", getDelFlag())
            .append("createBy", getCreateBy())
            .append("createNameBy", getCreateNameBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateNameBy", getUpdateNameBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
