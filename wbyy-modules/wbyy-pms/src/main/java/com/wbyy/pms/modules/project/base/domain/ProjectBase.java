package com.wbyy.pms.modules.project.base.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.wbyy.log.annotation.LogField;
import com.wbyy.pms.common.config.validation.ConditionalValidation;
import com.wbyy.pms.modules.project.info.domain.ProjectInfoAct;
import com.wbyy.pms.modules.project.info.domain.ProjectInfoBenao;
import com.wbyy.pms.modules.project.info.domain.ProjectInfoCrcgdbe;
import com.wbyy.pms.modules.project.info.domain.ProjectInfoDrdc;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.wbyy.common.core.annotation.Excel;
import com.wbyy.orm.core.domain.BaseAuditEntity;

import java.io.Serial;
import java.util.Date;

/**
 * 项目基础信息对象 project_base
 * 
 * <AUTHOR>
 * @date 2025-05-06
 */
@Data
@TableName("project_base")
@Schema(description = "项目基础信息实体类")
@EqualsAndHashCode(callSuper = true)
@ConditionalValidation()
public class ProjectBase extends BaseAuditEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /** 主键id */
    @Schema(description = "主键id（新增：不传；修改：必传）")
    private Long id;

    /** 项目编号 */
    @Excel(name = "项目编号")
    @Schema(description = "项目编号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "项目编号不能为空")
    @LogField(name = "项目编码")
    private String number;

    public void setNumber(String number){
        if (null!=number) number = number.trim();
        this.number = number;
    }

    /** 项目名称 */
    @Excel(name = "项目名称")
    @Schema(description = "项目名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "项目名称不能为空")
    @LogField(name = "项目名称")
    private String name;

    /** 项目类型id */
    @Excel(name = "项目类型id")
    @Schema(description = "项目类型id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "项目类型id不能为空")
    private Long typeId;

    @Schema(description = "项目类型")
    @LogField(name = "项目类型")
    private String typeName;

    /** 隶属项目中心id */
    @Excel(name = "隶属项目中心id")
    @Schema(description = "隶属项目中心id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "隶属项目中心id不能为空")
    private Long centerDeptId;

    /** 隶属项目中心编码 */
    @Excel(name = "隶属项目中心编码")
    @Schema(description = "隶属项目中心编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "隶属项目中心编码不能为空")
    private String centerDeptCode;

    /** 隶属部门id */
    @Excel(name = "隶属部门id")
    @Schema(description = "隶属部门id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "隶属部门id不能为空")
    private Long deptId;

    /** 项目状态（0:待启动、1:进行中、2:已终止、3:已暂停、4:已完结） */
    @Excel(name = "项目状态", readConverterExp = "0=:待启动、1:进行中、2:已终止、3:已暂停、4:已完结")
    @Schema(description = "项目状态（0:待启动、1:进行中、2:已终止、3:已暂停、4:已完结）", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "项目状态不能为空")
    @LogField(name = "项目状态",dictExp = "0=待启动,1=进行中,2=已终止,3=已暂停,4=已完结")
    private Integer status;

    @Schema(description = "项目来源")
    @NotNull(message = "项目来源不能为空")
    private String source;

    @Schema(description = "工作流ID")
    @NotNull(message = "工作流ID不能为空")
    private Long  oaWorkflowId;

    @Schema(description = "OA表单数据-id")
    private Long oaFormDataId;

    /** 集团合同编号 */
    @Excel(name = "集团合同编号")
    @Schema(description = "集团合同编号")
    @LogField(name = "集团合同编号")
    private String groupContractNumber;

    /** 公司合同编号 */
    @Excel(name = "公司合同编号")
    @Schema(description = "公司合同编号")
    @LogField(name = "公司合同编号")
    private String companyContractNumber;

    /** 合同编号 */
    @Excel(name = "合同编号")
    @Schema(description = "合同编号")
    @LogField(name = "合同编号")
    private String contractNumber;

    @Schema(description = "项目计划起始排期时间")
    private Date planStartDate;

    /** 删除标志（0代表存在 1代表删除） */
    @Schema(description = "删除标志（0代表存在 1代表删除）")
    @TableLogic
    private Integer delFlag;

    @Schema(description = "删除原因")
    private String delReason;

    @Schema(description = "红绿灯（0:绿色;1:红色;2:灰色）")
    @TableField(exist = false)
    private Integer light;

    @Schema(description = "项目实际进度")
    @TableField(exist = false)
    private Integer realityProgress;

    @Schema(description = "项目计划进度")
    @TableField(exist = false)
    private Integer planProgress;

    @Schema(description = "项目经理Id")
    @TableField(exist = false)
    private Long projectManagerId;

    @Schema(description = "项目经理Id")
    @TableField(exist = false)
    private String projectManagerIdStr;

    @Schema(description = "项目经理")
    @TableField(exist = false)
    private String projectManager;

    @TableField(exist = false)
    @Schema(description = "项目信息[atc]表单-分析测试中心")
    private ProjectInfoAct projectInfoAct;

    @TableField(exist = false)
    @Schema(description = "项目信息[benao]表单-本奥医学")
    private ProjectInfoBenao projectInfoBenao;

    @TableField(exist = false)
    @Schema(description = "项目信息[crcgdbe]表单-仿制药BE临床研究中心")
    private ProjectInfoCrcgdbe projectInfoCrcgdbe;

    @TableField(exist = false)
    @Schema(description = "项目信息[drdc]表单-药物研发中心")
    private ProjectInfoDrdc projectInfoDrdc;

    /** 项目模版id */
    @Excel(name = "项目模版id")
    @Schema(description = "项目模版id")
    private Long templateId;

    @Schema(description = "项目模版名称")
    @TableField(exist = false)
    private String templateName;

    @Schema(description = "项目计划结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @TableField(exist = false)
    private Date planEndDate;

    @Schema(description = "合同id")
    @TableField(exist = false)
    private Long contractId;

    @TableField(exist = false)
    private Object model;


    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("number", getNumber())
            .append("name", getName())
            .append("typeId", getTypeId())
            .append("centerDeptId", getCenterDeptId())
            .append("centerDeptCode", getCenterDeptCode())
            .append("deptId", getDeptId())
            .append("status", getStatus())
            .append("delFlag", getDelFlag())
            .append("createBy", getCreateBy())
            .append("createNameBy", getCreateNameBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateNameBy", getUpdateNameBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
