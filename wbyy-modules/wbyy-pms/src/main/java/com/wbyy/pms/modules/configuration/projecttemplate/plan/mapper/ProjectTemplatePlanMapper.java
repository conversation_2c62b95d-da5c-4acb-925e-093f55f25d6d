package com.wbyy.pms.modules.configuration.projecttemplate.plan.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wbyy.pms.modules.configuration.projecttemplate.plan.domain.ProjectTemplatePlan;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 项目模板计划Mapper接口
 *
 * <AUTHOR>
 * @date 2025-04-23
 */
public interface ProjectTemplatePlanMapper extends BaseMapper<ProjectTemplatePlan> {


    /**
     * 查询 所有父级
     *
     * @param id 主键
     * @param projectTemplateId 项目模板Id
     * @return
     */
    List<ProjectTemplatePlan> selectAllParentNodeById(@Param("projectTemplateId") Long projectTemplateId,
                                                @Param("id") Long id);
    /**
     * 查询 所有子节点
     *
     * @param id 主键
     * @param projectTemplateId 项目模板Id
     * @return
     */
    List<ProjectTemplatePlan> selectAllChildrenNodeById(@Param("projectTemplateId") Long projectTemplateId,
                                                @Param("id") Long id);

}
