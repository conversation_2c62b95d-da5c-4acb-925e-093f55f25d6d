package com.wbyy.pms.modules.configuration.basicparameter.type.dao;

import java.util.Collection;
import java.util.List;
import com.wbyy.pms.modules.configuration.basicparameter.type.domain.ProjectTypeObjectPO;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 项目类型(业务类型)与对象关系Dao接口
 *
 * <AUTHOR>
 * @date 2025-07-15
 */
public interface IProjectTypeObjectDao extends IService<ProjectTypeObjectPO> {
    /**
     * 查询项目类型(业务类型)与对象关系
     *
     * @param id 项目类型(业务类型)与对象关系主键
     * @return 项目类型(业务类型)与对象关系
     */
    ProjectTypeObjectPO selectById(Long id);

    /**
     * 查询项目类型(业务类型)与对象关系列表
     *
     * @param po 项目类型(业务类型)与对象关系
     * @return 项目类型(业务类型)与对象关系集合
     */
    List<ProjectTypeObjectPO> selectList(ProjectTypeObjectPO po);

    /**
     * 根据 po 中不为null的字段 查询项目类型(业务类型)与对象关系列表
     *
     * @param po 项目类型(业务类型)与对象关系
     * @return 项目类型(业务类型)与对象关系列表
     */
    List<ProjectTypeObjectPO> findByPO(ProjectTypeObjectPO po);

    /**
     * 新增项目类型(业务类型)与对象关系
     *
     * @param po 项目类型(业务类型)与对象关系
     * @return 结果
     */
    boolean insert(ProjectTypeObjectPO po);

    /**
     * 修改项目类型(业务类型)与对象关系
     *
     * @param po 项目类型(业务类型)与对象关系
     * @return 结果
     */
    boolean update(ProjectTypeObjectPO po);

    /**
     * 批量删除项目类型(业务类型)与对象关系
     *
     * @param ids 需要删除的项目类型(业务类型)与对象关系主键集合
     * @return 结果
     */
    boolean deleteByIds(Long[] ids);

    /**
     * 根据项目业务类型id获取数据
     *
     * @param projectTypeId
     * @return ProjectTypeObjectPO
     * <AUTHOR>
     * @date 2025/7/15 11:15
     */
    ProjectTypeObjectPO getByProjectTypeId(Long projectTypeId);

    /**
     * 根据项目业务类型id集合获取数据集合
     *
     * @param projectTypeIds
     * @return List<ProjectTypeObjectPO>
     * <AUTHOR>
     * @date 2025/7/16 9:08
     */
    List<ProjectTypeObjectPO> listByProjectTypeIds(Collection<Long> projectTypeIds);

}
