package com.wbyy.pms.modules.project.planuser.domain;

import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.wbyy.common.core.annotation.Excel;
import com.wbyy.orm.core.domain.BaseAuditEntity;

import java.io.Serial;

/**
 * 项目计划责任人-编制对象 project_plan_snapshot_user
 * 
 * <AUTHOR>
 * @date 2025-05-13
 */
@Data
@TableName("project_plan_snapshot_user")
@Schema(description = "项目计划责任人-编制实体类")
@EqualsAndHashCode(callSuper = true)
@SuperBuilder
@Accessors(chain = true)
@NoArgsConstructor
public class ProjectPlanSnapshotUser extends BaseAuditEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /** 主键id */
    @Schema(description = "主键id（新增：不传；修改：必传）")
    private Long id;

    /** 关联项目id */
    @Excel(name = "关联项目id")
    @Schema(description = "关联项目id", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long projectId;

    /** 关联计划id */
    @Excel(name = "关联计划id")
    @Schema(description = "关联计划id", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long projectPlanId;

    @TableField(exist = false)
    private String projectPlanName;

    /** 责任人id */
    @Excel(name = "责任人id")
    @Schema(description = "责任人id", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "责任人id不能为空")
    private Long userId;

    @Schema(description = "用户工号")
    private String workNumber;

    @Schema(description = "真实姓名（责任人姓名）")
    private String realName;


    /** 工时，默认取工期内的自然日天数 */
    @Excel(name = "工时，默认取工期内的自然日天数")
    @Schema(description = "工时，默认取工期内的自然日天数", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "工时不能为空")
    private BigDecimal workHour;

    @TableField(exist = false)
    @Schema(description = "项目角色（已翻译）")
    private String projectRoleNames;

    /** 删除标志（0代表存在 1代表删除） */
    @Schema(description = "删除标志（0代表存在 1代表删除）")
    @TableLogic
    private Integer delFlag;


    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("projectId", getProjectId())
            .append("projectPlanId", getProjectPlanId())
            .append("userId", getUserId())
            .append("workHour", getWorkHour())
            .append("delFlag", getDelFlag())
            .append("createBy", getCreateBy())
            .append("createNameBy", getCreateNameBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateNameBy", getUpdateNameBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
