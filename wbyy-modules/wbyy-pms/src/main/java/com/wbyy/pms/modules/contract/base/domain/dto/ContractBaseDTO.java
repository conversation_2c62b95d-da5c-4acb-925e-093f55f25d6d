package com.wbyy.pms.modules.contract.base.domain.dto;

import com.wbyy.system.api.model.ApiBaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/25  11:15
 * @description 查询合同列表入参
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ContractBaseDTO extends ApiBaseEntity {

    /**
     * 业务类型id
     */
    @Schema(description = "业务类型id")
    private List<String> businessTypeIds;

    /**
     * 合同状态，进行中=1、已暂停=2、已终止=3、已结束=0
     */
    @Schema(description = "合同状态，进行中=1、已暂停=2、已终止=3、已结束=0")
    private List<Integer> statusList;

    @Schema(description = "合同名称、集团合同编号、公司合同编号模糊搜索")
    private String searchValue;
}
