package com.wbyy.pms.modules.team.taskstatistics.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.wbyy.pms.modules.project.plan.domain.vo.StatusVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/7/2  9:18
 * @description 任务统计返回对象
 */
@Data
public class TaskStatisticsVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "任务名称")
    private String name;

    @Schema(description = "任务id")
    private Long id;

    @Schema(description = "责任人姓名(已翻译、直接使用)")
    private String projectPlanUserNames;

    @Schema(description = "状态", implementation = StatusVO.class)
    private StatusVO status;

    /**
     * 计划开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Schema(description = "计划开始时间")
    private Date planStartDate;

    /**
     * 计划结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Schema(description = "计划结束时间")
    private Date planEndDate;

    @Schema(description = "里程碑(1:是;0:否)")
    private Integer milestoneFlag;

    @Schema(description = "实际进度")
    private Integer realityProgress;


    @Schema(description = "项目名称")
    private String projectName;

    @Schema(description = "项目id")
    private Long projectId;

    @Schema(description = "项目编号")
    private String number;

    @Schema(description = "项目类型id")
    private Long typeId;

    @Schema(description = "项目类型")
    private String typeName;

    @Schema(description = "隶属部门id")
    private Long deptId;

    @Schema(description = "隶属部门")
    private String deptName;

}
