package com.wbyy.pms.modules.configuration.dict.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wbyy.common.core.constant.CacheConstants;
import com.wbyy.common.core.exception.ServiceException;
import com.wbyy.common.core.utils.TreeUtil;
import com.wbyy.common.redis.service.CommonCacheService;
import com.wbyy.log.annotation.OperationLog;
import com.wbyy.log.constant.DefaultOperationType;
import com.wbyy.pms.modules.configuration.dict.domain.DictData;
import com.wbyy.pms.modules.configuration.dict.mapper.DictDataMapper;
import com.wbyy.pms.modules.configuration.dict.service.IDictDataService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 字典数据Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-04-17
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DictDataServiceImpl extends ServiceImpl<DictDataMapper, DictData> implements IDictDataService {
    private final DictDataMapper dictDataMapper;
    private final CommonCacheService<DictData> commonCacheService;

    /**
     * 查询字典数据
     *
     * @param dictCode 字典数据主键
     * @return 字典数据
     */
    @Override
    public DictData selectSysDictDataByDictCode(Long dictCode) {
        return this.getById(dictCode);
    }

    /**
     * 查询字典数据列表
     *
     * @param dictData 字典数据
     * @return 字典数据
     */
    @Override
    public List<DictData> selectSysDictDataList(DictData dictData) {
        LambdaQueryWrapper<DictData> wrapper = Wrappers.lambdaQuery();
        wrapper.orderByDesc(DictData::getCreateTime);
        return this.list(wrapper);
    }

    /**
     * 新增字典数据
     *
     * @param dictData 字典数据
     * @return 结果
     */
    @Override
    @OperationLog(name = DefaultOperationType.ADD,
            originalDataMethod = "selectSysDictDataByDictCode",
            objectIdField = "dictCode",
            objectClazz = DictData.class,
            type = "字典值")
    @Transactional(rollbackFor = Exception.class)
    public boolean insertSysDictData(DictData dictData) {
        // 数据唯一校验
        this.checkDictDataByDictType(dictData);
        boolean isOk = this.save(dictData);
        // 删除缓存
        commonCacheService.removeDictCache(CacheConstants.PMS_DICT_KEY, dictData.getDictType());
        return isOk;
    }

    /**
     * 检查字典是否重复
     * @param dictData
     * @return 条数
     */
    private void checkDictDataByDictType(DictData dictData) {
        LambdaQueryWrapper<DictData> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(DictData::getDictType, dictData.getDictType())
                .eq(DictData::getDictValue, dictData.getDictValue());
        if (dictData.getDictCode() != null) {
            wrapper.ne(DictData::getDictCode, dictData.getDictCode());
        }
        long count = this.count(wrapper);
        if (count > 0) {
            throw new ServiceException("数据字典值不能重复");
        }
    }

    /**
     * 修改字典数据
     *
     * @param dictData 字典数据
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @OperationLog(name = DefaultOperationType.UPDATE,
            originalDataMethod = "selectSysDictDataByDictCode",
            objectIdField = "dictCode",
            objectClazz = DictData.class,
            type = "字典值")
    public boolean updateSysDictData(DictData dictData) {
        // 唯一性检查
        this.checkDictDataByDictType(dictData);

        boolean isOk = this.updateById(dictData);
        // 删除缓存
        commonCacheService.removeDictCache(CacheConstants.PMS_DICT_KEY, dictData.getDictType());
        return isOk;
    }

    /**
     * 批量删除字典数据
     *
     * @param dictCodes 需要删除的字典数据主键
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @OperationLog(name = DefaultOperationType.DELETE,
            type = "字典值",objectClazz = DictData.class)
    public boolean deleteSysDictDataByDictCodes(Long[] dictCodes) {
        List<Long> ids = Arrays.asList(dictCodes);
        List<DictData> dictData = this.listByIds(ids);
        if (CollUtil.isNotEmpty(dictData)) {
            Set<String> dictTypeSet = dictData.stream().map(DictData::getDictType).collect(Collectors.toSet());
            dictTypeSet.forEach(dictType ->
                    commonCacheService.removeDictCache(CacheConstants.PMS_DICT_KEY, dictType));
        }
        return this.removeBatchByIds(ids);
    }

    @Override
    public List<DictData> selectDictDataByType(String dictType) {
        LambdaQueryWrapper<DictData> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(DictData::getDictType, dictType)
                .orderByAsc(DictData::getParentDictCode, DictData::getDictSort);
        List<DictData> treeData = this.list(wrapper);
        JSONArray jsonArray = JSONArray.parseArray(JSON.toJSONString(treeData));
        JSONArray objects = TreeUtil.buildTree(jsonArray, "dictCode", "parentDictCode", "children");
        return JSONArray.parseArray(objects.toJSONString(), DictData.class);
    }

    @Override
    public long countDictDataByType(String dictType) {
        LambdaQueryWrapper<DictData> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(DictData::getDictType, dictType);
        return this.count(wrapper);
    }

}
