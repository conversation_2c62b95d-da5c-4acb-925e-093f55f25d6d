-- 按钮父菜单ID
SELECT @parentId := UNIX_TIMESTAMP() + round(10000*rand(), 0);

-- 菜单 SQL
insert into sys_menu (menu_id, menu_name, parent_id, menu_sort, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, application_code)
values(@parentId, '项目模板计划', '1912025074121904130', '1', 'projectTemplatePlan', 'project/projectTemplatePlan/index', 1, 0, 'C', '0', '0', 'project:projectTemplatePlan:list', '#', 1, sysdate(), null, null, '项目模板计划菜单', 'pms-system');


-- 按钮 SQL
insert into sys_menu (menu_id, menu_name, parent_id, menu_sort, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, application_code)
values(UNIX_TIMESTAMP() + round(10000*rand(), 0), '项目模板计划查询', @parentId, '1',  '#', '', 1, 0, 'F', '0', '0', 'project:projectTemplatePlan:list',        '#', 1, sysdate(), null, null, '', 'pms-system');

insert into sys_menu (menu_id, menu_name, parent_id, menu_sort, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, application_code)
values(UNIX_TIMESTAMP() + round(10000*rand(), 0), '项目模板计划新增', @parentId, '2',  '#', '', 1, 0, 'F', '0', '0', 'project:projectTemplatePlan:add',          '#', 1, sysdate(), null, null, '', 'pms-system');

insert into sys_menu (menu_id, menu_name, parent_id, menu_sort, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, application_code)
values(UNIX_TIMESTAMP() + round(10000*rand(), 0), '项目模板计划修改', @parentId, '3',  '#', '', 1, 0, 'F', '0', '0', 'project:projectTemplatePlan:edit',         '#', 1, sysdate(), null, null, '', 'pms-system');

insert into sys_menu (menu_id, menu_name, parent_id, menu_sort, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, application_code)
values(UNIX_TIMESTAMP() + round(10000*rand(), 0), '项目模板计划删除', @parentId, '4',  '#', '', 1, 0, 'F', '0', '0', 'project:projectTemplatePlan:remove',       '#', 1, sysdate(), null, null, '', 'pms-system');

insert into sys_menu (menu_id, menu_name, parent_id, menu_sort, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, application_code)
values(UNIX_TIMESTAMP() + round(10000*rand(), 0), '项目模板计划导出', @parentId, '5',  '#', '', 1, 0, 'F', '0', '0', 'project:projectTemplatePlan:export',       '#', 1, sysdate(), null, null, '', 'pms-system');