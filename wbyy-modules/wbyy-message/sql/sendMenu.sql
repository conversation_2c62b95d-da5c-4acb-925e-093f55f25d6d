-- 按钮父菜单ID
SELECT @parentId := UNIX_TIMESTAMP() + round(10000*rand(), 0);

-- 菜单 SQL
insert into sys_menu (menu_id, menu_name, parent_id, menu_sort, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, application_code)
values(@parentId, '消息发送记录', '1925779955281735681', '1', 'send', 'message/send/index', 1, 0, 'C', '0', '0', 'message:send:list', '#', 1, sysdate(), null, null, '消息发送记录菜单', 'sys');


-- 按钮 SQL
insert into sys_menu (menu_id, menu_name, parent_id, menu_sort, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, application_code)
values(UNIX_TIMESTAMP() + round(10000*rand(), 0), '消息发送记录查询', @parentId, '1',  '#', '', 1, 0, 'F', '0', '0', 'message:send:list',        '#', 1, sysdate(), null, null, '', 'sys');

insert into sys_menu (menu_id, menu_name, parent_id, menu_sort, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, application_code)
values(UNIX_TIMESTAMP() + round(10000*rand(), 0), '消息发送记录新增', @parentId, '2',  '#', '', 1, 0, 'F', '0', '0', 'message:send:add',          '#', 1, sysdate(), null, null, '', 'sys');

insert into sys_menu (menu_id, menu_name, parent_id, menu_sort, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, application_code)
values(UNIX_TIMESTAMP() + round(10000*rand(), 0), '消息发送记录修改', @parentId, '3',  '#', '', 1, 0, 'F', '0', '0', 'message:send:edit',         '#', 1, sysdate(), null, null, '', 'sys');

insert into sys_menu (menu_id, menu_name, parent_id, menu_sort, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, application_code)
values(UNIX_TIMESTAMP() + round(10000*rand(), 0), '消息发送记录删除', @parentId, '4',  '#', '', 1, 0, 'F', '0', '0', 'message:send:remove',       '#', 1, sysdate(), null, null, '', 'sys');

insert into sys_menu (menu_id, menu_name, parent_id, menu_sort, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark, application_code)
values(UNIX_TIMESTAMP() + round(10000*rand(), 0), '消息发送记录导出', @parentId, '5',  '#', '', 1, 0, 'F', '0', '0', 'message:send:export',       '#', 1, sysdate(), null, null, '', 'sys');