package com.wbyy.message.common.config.properties;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Component
@ConfigurationProperties(prefix = "message")
public class MessageProperties {

    /**
     * 授权的应用 code
     */
    private List<String> authApp;

    /**
     * 消息默认发送人，为空时，按照业务指定的用户发送消息
     * 不为空时，任何消息将发给  defaultUserId
     */
    private List<String> defaultUserId;

    private Map<String, MsgSourceItem> msgSource;


    @Data
    public static class MsgSourceItem {
        // 来源图标
        private String iconUrl;
        // 来源描述
        private String desc;
        // 来源描述颜色
        private Integer descColor;
        // 重复消息检测间隔
        private Integer duplicateCheckInterval;
    }
}
