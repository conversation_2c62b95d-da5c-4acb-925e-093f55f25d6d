package com.wbyy.message.modules.callback;

import com.wbyy.common.core.constant.AppCodeConst;
import com.wbyy.common.core.domain.R;
import com.wbyy.common.qyweixin.config.WxCpConfiguration;
import com.wbyy.common.qyweixin.config.properties.AppConfig;
import com.wbyy.common.qyweixin.config.properties.WxCpProperties;
import com.wbyy.message.utils.QwUtils;
import com.wbyy.message.utils.aes.WXBizMsgCrypt;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.util.crypto.WxCryptUtil;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

@RestController
@RequiredArgsConstructor
@Tag(name = "企微回调")
@RequestMapping("/callback/qw")
@Slf4j
public class QwCallbackController {

    private final WxCpProperties wxCpProperties;

    /**
     * @param msg_signature 企业微信加密签名
     * @param timestamp     时间戳
     * @param nonce         时间戳
     * @param echostr       加密的字符串
     * @return
     */
    @GetMapping
    public String getCallbackCheck(String msg_signature, String timestamp, String nonce, String echostr) throws Exception {
        log.info("【企微回调】-应用消息回调，参数：msg_signature：{}，timestamp：{}，nonce：{}，echostr：{}",
                msg_signature, timestamp, nonce, echostr);
        /*
         对收到的请求做Urldecode处理
         通过参数 msg_signature 对请求进行校验，确认调用者的合法性。
         解密 echostr 参数得到消息内容(即msg字段)
         在1秒内响应GET请求，响应内容为上一步得到的明文消息内容(不能加引号，不能带bom头，不能带换行符)
        */
        AppConfig appConfig = WxCpConfiguration.getAppConfig(AppCodeConst.MESSAGE);

        WXBizMsgCrypt wxcpt = new WXBizMsgCrypt(appConfig.getToken(), appConfig.getEncodingAesKey(), wxCpProperties.getCorpId());
        //需要返回的明文
        String verified = wxcpt.VerifyURL(msg_signature, timestamp, nonce, echostr);












//
//
//
//
////        WxCryptUtil wxCryptUtil = new WxCryptUtil(appConfig.getToken(), appConfig.getEncodingAesKey(), wxCpProperties.getCorpId());
//        WXBizMsgCrypt wxBizMsgCrypt = new WXBizMsgCrypt(appConfig.getToken(), appConfig.getEncodingAesKey(), wxCpProperties.getCorpId());
//        String verified = wxBizMsgCrypt.DecryptMsg(msg_signature, timestamp, nonce, echostr);
        log.debug("verified {}", verified);

        return verified;
    }

    @PostMapping
    public R<Boolean> postCallbackTodo(HttpServletRequest request) {
        Map<String, String> paramMap = QwUtils.amendCodeMap(request.getParameterMap());
        String msg_signature = paramMap.get("msg_signature");
        String timestamp = paramMap.get("timestamp");
        String nonce = paramMap.get("nonce");
        String echostr = paramMap.get("echostr");
        log.info("【企微回调】-应用消息回调，参数：{}", paramMap);
        /*
         对收到的请求做Urldecode处理
         通过参数 msg_signature 对请求进行校验，确认调用者的合法性。
         解密 echostr 参数得到消息内容(即msg字段)
         在1秒内响应GET请求，响应内容为上一步得到的明文消息内容(不能加引号，不能带bom头，不能带换行符)
        */
        AppConfig appConfig = WxCpConfiguration.getAppConfig(AppCodeConst.MESSAGE);
        WxCryptUtil wxCryptUtil = new WxCryptUtil(appConfig.getToken(), appConfig.getEncodingAesKey(), wxCpProperties.getCorpId());
        String decryptContent = wxCryptUtil.decryptContent(msg_signature, timestamp, nonce, echostr);
        log.debug("【企微回调】-应用消息回调，解密后参数：{}", decryptContent);
        return R.ok();
    }
}
