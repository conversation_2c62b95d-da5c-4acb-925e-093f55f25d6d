package com.wbyy.message.modules.messagetemplate.controller;

import com.wbyy.common.core.domain.R;
import com.wbyy.common.core.utils.poi.ExcelUtil;
import com.wbyy.common.core.web.controller.BaseController;
import com.wbyy.common.core.web.page.TableDataInfo;
import com.wbyy.common.log.annotation.Log;
import com.wbyy.common.log.enums.BusinessType;
import com.wbyy.common.security.annotation.RequiresPermissions;
import com.wbyy.message.modules.messagetemplate.domain.MessageTemplate;
import com.wbyy.message.modules.messagetemplate.service.IMessageTemplateService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 消息模板Controller
 *
 * <AUTHOR>
 * @date 2025-05-23
 */
@RestController
@RequiredArgsConstructor
@Tag(name = "消息模板")
@RequestMapping("/message/template")
public class MessageTemplateController extends BaseController {

    private final IMessageTemplateService messageTemplateService;

    @Operation(summary = "分页列表")
    @RequiresPermissions("message:template:list")
    @GetMapping("/page")
    public TableDataInfo<MessageTemplate> page(@ParameterObject MessageTemplate messageTemplate) {
        startPage();
        List<MessageTemplate> list = messageTemplateService.selectList(messageTemplate);
        return getDataTable(list);
    }
    @Operation(summary = "分页列表")
    @RequiresPermissions("message:template:list")
    @GetMapping("/list")
    public R<List<MessageTemplate>> list() {
        List<MessageTemplate> list = messageTemplateService.findAll();
        return R.ok(list);
    }

    @Operation(summary = "导出列表")
    @RequiresPermissions("message:template:export")
    @Log(title = "消息模板", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, MessageTemplate messageTemplate) {
        List<MessageTemplate> list = messageTemplateService.selectList(messageTemplate);
        ExcelUtil<MessageTemplate> util = new ExcelUtil<MessageTemplate>(MessageTemplate.class);
        util.exportExcel(response, list, "消息模板数据");
    }

    @Operation(summary = "获取详细信息")
    @RequiresPermissions("message:template:query")
    @GetMapping(value = "/{id}")
    public R<MessageTemplate> getInfo(@PathVariable("id") Long id) {
        return R.ok(messageTemplateService.selectById(id));
    }

    @Operation(summary = "新增")
    @RequiresPermissions("message:template:add")
    @Log(title = "消息模板", businessType = BusinessType.INSERT)
    @PostMapping
    public R<Boolean> add(@RequestBody @Validated MessageTemplate messageTemplate) {
        return R.ok(messageTemplateService.insert(messageTemplate));
    }

    @Operation(summary = "修改")
    @RequiresPermissions("message:template:edit")
    @Log(title = "消息模板", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<Boolean> edit(@RequestBody @Validated MessageTemplate messageTemplate) {
        return R.ok(messageTemplateService.update(messageTemplate));
    }

    @Operation(summary = "删除")
    @RequiresPermissions("message:template:remove")
    @Log(title = "消息模板", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Boolean> remove(@PathVariable Long[] ids) {
        return R.ok(messageTemplateService.deleteByIds(ids));
    }
}
