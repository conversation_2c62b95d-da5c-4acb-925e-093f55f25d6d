package com.wbyy.message.utils;
import com.wbyy.common.core.exception.ServiceException;

import javax.crypto.Cipher;
import javax.crypto.NoSuchPaddingException;
import javax.crypto.spec.SecretKeySpec;
import javax.crypto.spec.IvParameterSpec;
import java.security.InvalidAlgorithmParameterException;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.Base64;

public class WXDecryptor {
    private static final String ALGORITHM = "AES/CBC/PKCS5Padding";

    public static String decrypt(String encryptedData, String encodingAesKey) {
        // Base64解码
        byte[] decodedData = Base64.getDecoder().decode(encryptedData);
        byte[] keyBytes = Base64.getDecoder().decode(encodingAesKey + "=");

        // 初始化AES秘钥
        SecretKeySpec keySpec = new SecretKeySpec(keyBytes, "AES");
        IvParameterSpec ivSpec = new IvParameterSpec(keyBytes, 0, 16);
        Cipher cipher = null;

        try {
            // 创建Cipher对象
            cipher = Cipher.getInstance(ALGORITHM);
            cipher.init(Cipher.DECRYPT_MODE, keySpec, ivSpec);
            // 解密返回明文
            return new String(cipher.doFinal(decodedData), "UTF-8");
        } catch (Exception e) {
            throw new ServiceException(e);
        }


    }
}