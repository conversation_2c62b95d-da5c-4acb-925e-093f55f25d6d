package com.wbyy.crm.modules.customers.customer.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.FieldStrategy;

import java.math.BigDecimal;
import java.util.Date;

import com.wbyy.crm.common.annotation.CrmMapping;
import com.wbyy.crm.common.base.BaseDomain;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 客户基本信息(customer)实体类
 *
 * <AUTHOR>
 * @since 2025-03-25 10:11:41
 * @description 由 Mybatisplus Code Generator 创建
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@Accessors(chain = true)
@TableName("customer")
public class Customer extends BaseDomain {

    /**
     * 公司ID
     */


    /**
     * 公司名称
     */
    @CrmMapping(value = "custom_name")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String name;

    /**
     * 公司类型
     */
    @CrmMapping(value = "customer_select_19")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String industry;

    /**
     * 客户类别
     */
    @CrmMapping(value = "customer_select_0")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String status;

    /**
     * 公司性质
     */
    @CrmMapping(value = "customer_select_23")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String companyType;

    /**
     * 客户来源
     */
    @CrmMapping(value = "customer_select_10")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String source;

    /**
     * 所属区域
     */
    @CrmMapping(value = "custom_location")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String region;

    /**
     * 客户状态
     */
    @CrmMapping(value = "customer_select_21")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String activityStatus;

    /**
     * 所属部门
     */
    @CrmMapping(value = "dimension_name")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String department;

    /**
     * 客户级别
     */
    @CrmMapping(value = "custom_level")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String level;

    /**
     * 注册资本
     */
    @CrmMapping(value = "customer_input_16")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private BigDecimal registeredCapital;

    /**
     * 法定代表人
     */
    @CrmMapping(value = "customer_input_14")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String legalRepresentative;

    /**
     * 统一信用代码
     */
    @CrmMapping(value = "unc_id")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String creditCode;

    /**
     * 企业成立日期
     */
    @CrmMapping(value = "customer_date_0")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private Date establishmentDate;

    /**
     * 客户来源说明
     */
    @CrmMapping(value = "customer_input_19")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String sourceInfo;

    /**
     * custom_nick
     */
    @CrmMapping(value = "custom_nick")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String customNick;

    /**
     * 负责人
     */
    @CrmMapping("charger_name")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String responsiblePerson;

    /**
     * 是否新客，1是，0否
     */
    @TableField(updateStrategy = FieldStrategy.NOT_NULL,insertStrategy = FieldStrategy.NOT_NULL)
    private Integer newCustomer;
}