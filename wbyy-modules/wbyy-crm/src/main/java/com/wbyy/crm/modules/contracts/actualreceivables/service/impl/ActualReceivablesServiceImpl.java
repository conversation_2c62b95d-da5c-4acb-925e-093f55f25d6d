package com.wbyy.crm.modules.contracts.actualreceivables.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wbyy.crm.common.base.BaseService;
import com.wbyy.crm.modules.contracts.actualreceivables.domain.ActualReceivables;
import com.wbyy.crm.modules.contracts.actualreceivables.mapper.ActualReceivablesMapper;
import com.wbyy.crm.modules.contracts.actualreceivables.service.ActualReceivablesService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 实际回款服务接口实现
 *
 * <AUTHOR>
 * @since 2025-03-26 14:03:27
 * @description 
 */
@Slf4j
@RequiredArgsConstructor
@Service("actualReceivablesService")
public class ActualReceivablesServiceImpl extends ServiceImpl<ActualReceivablesMapper, ActualReceivables> implements ActualReceivablesService, BaseService {
    private final ActualReceivablesMapper actualReceivablesMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void loadData(List<Object> list) {
        List<ActualReceivables> remoteDataList = list.stream().map(ActualReceivables.class::cast).toList();
        Set<Long> remoteIdSet = remoteDataList.stream().map(ActualReceivables::getId).collect(Collectors.toSet());
        List<ActualReceivables> orignalList = this.list();
        Set<Long> toRemoveIdSet = orignalList.stream().map(ActualReceivables::getId).filter(item -> !remoteIdSet.contains(item)).collect(Collectors.toSet());
        if (CollectionUtil.isNotEmpty(toRemoveIdSet)){
            this.removeBatchByIds(toRemoveIdSet);
        }
        log.info("【ActualReceivables】删除数据数：{}",toRemoveIdSet.size());
        if (CollectionUtil.isNotEmpty(remoteDataList)){
            this.saveOrUpdateBatch(remoteDataList);
        }
        log.info("【ActualReceivables】落库数据数：{}",remoteDataList.size());
    }

    @Override
    public List<ActualReceivables> listByCreatedAtRange(Date startTime, Date endTime) {
        LambdaQueryWrapper<ActualReceivables> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.ge(ActualReceivables::getCreatedAt,startTime);
        queryWrapper.le(ActualReceivables::getCreatedAt,endTime);
        return list(queryWrapper);
    }
}