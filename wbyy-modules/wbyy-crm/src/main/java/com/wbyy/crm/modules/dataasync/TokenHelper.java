package com.wbyy.crm.modules.dataasync;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.Data;
import okhttp3.*;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.time.Instant;
import java.util.concurrent.locks.ReadWriteLock;
import java.util.concurrent.locks.ReentrantReadWriteLock;

@Component
public class TokenHelper {
    @Value("${crm.corp-id}")
    private String corpId;
    @Value("${crm.app-id}")
    private String appId;
    @Value("${crm.app-secret}")
    private String appSecret;
    @Value("${crm.get-token}")
    private String getToken;

    // JSON解析工具
    private static final ObjectMapper mapper = new ObjectMapper();
    // HTTP客户端
    private static final OkHttpClient httpClient = new OkHttpClient();

    // 读写锁（公平锁）
    private final static ReadWriteLock lock = new ReentrantReadWriteLock(true);
    // Token及过期时间
    private volatile String token;
    private volatile long expiresAt;

    public String getToken() throws Exception {
        // 尝试获取读锁
        lock.readLock().lock();
        try {
            if (isTokenValid()) {
                return token;
            }
        } finally {
            lock.readLock().unlock();
        }

        // Token无效，获取写锁
        lock.writeLock().lock();
        try {
            // 双重检查
            if (!isTokenValid()) {
                refreshToken();
            }
            return token;
        } finally {
            lock.writeLock().unlock();
        }
    }

    private boolean isTokenValid() {
        return token != null && Instant.now().getEpochSecond() < (expiresAt - 3);
    }

    private void refreshToken() throws Exception {
        MediaType mediaType=MediaType.Companion.parse("application/json;charset=utf-8");
        RequestBody stringBody=RequestBody.Companion
                .create(String.format("{\"corpId\":\"%s\",\"appId\":\"%s\",\"appSecret\":\"%s\"}", corpId, appId, appSecret),
                        mediaType);
        Request request = new Request.Builder()
                .url(getToken)
                .post(stringBody)
                .build();

        try (Response response = httpClient.newCall(request).execute()) {
            if (!response.isSuccessful()) throw new IOException("Unexpected code " + response);

            ResponseData responseData = null;
            if (response.body() != null) {
                responseData = mapper.readValue(response.body().bytes(), ResponseData.class);
            }

            if (responseData != null && responseData.success) {
                this.token = responseData.data.corpAccessToken;
                this.expiresAt = Instant.now().getEpochSecond() + responseData.data.expiresTime;
            }
        }
    }

    // 响应数据结构
    @Data
    private static class ResponseData {
        boolean success;
        Data data;
        String message;
        String apiName;
        String ip;
        Integer result;
        String traceId;

        @lombok.Data
        static class Data {
            @JsonProperty("corpAccessToken")
            String corpAccessToken;
            @JsonProperty("expiresTime")
            long expiresTime;
        }
    }

}
