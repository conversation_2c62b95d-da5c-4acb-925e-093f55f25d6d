package com.wbyy.crm.modules.api.constant;

import java.math.BigDecimal;

public class TargetCompleteConst {
    public static final String CONTRACT_AMOUNT_TEXT = "合同额/新客(万元)";
    public static final String RECEVIABLES_AMOUNT_TEXT = "回款(万元)";
    public static final String VISIT_CHECKIN_TEXT = "拜访签到";
    public static final String SALES_OPPORTUNITY_TEXT = "新增销售机会";
    public static final String CUSTOMER_TEXT = "新建客户";
    public static final String FOLLOW_UP_TEXT = "跟进次数";
    public static final String WIN_AMOUNT_TEXT = "赢单金额(万元)";
    public static final String WIN_COUNT_TEXT = "赢单数";

    public static final String COMPLETE_TEXT = "迄今完成";
    public static final String COMPLETE_TARGET_TEXT = "迄今目标";
    public static final String COMPLETE_PERCENT_TEXT = "迄今完成率";
    public static final String MONTH_COMPLETE_TARGET_TEXT = "月目标";
    public static final String MONTH_COMPLETE_TEXT = "月完成";

    public static final String QUARTER_TEXT = "季度完成率";

    public static final String QUARTER_4_TEXT = "4季度完成率";
    public static final String QUARTER_3_TEXT = "3季度完成率";
    public static final String QUARTER_2_TEXT = "2季度完成率";
    public static final String QUARTER_1_TEXT = "1季度完成率";

    public static final String GOAL_TYPE_CONTRACT_AMOUNT_TEXT = "合同订单金额";
    public static final String GOAL_TYPE_RECEVIABLES_AMOUNT_TEXT = "回款金额";
    public static final String GOAL_TYPE_VISIT_CHECKIN_TEXT = "新增拜访签到";
    public static final String GOAL_TYPE_SALES_OPPORTUNITY_TEXT = "新增销售机会";
    public static final String GOAL_TYPE_CUSTOMER_TEXT = "新建客户";
    public static final String GOAL_TYPE_FOLLOW_UP_TEXT = "新增跟进";
    public static final String GOAL_TYPE_WIN_AMOUNT_TEXT = "赢单金额";
    public static final String GOAL_TYPE_WIN_COUNT_TEXT = "赢单数";

    public static final BigDecimal MAX_VALUE = new BigDecimal("100");

}
