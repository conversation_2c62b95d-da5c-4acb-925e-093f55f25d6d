package com.wbyy.crm.modules.business.salesopportunity.domain;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.math.BigDecimal;
import java.util.Date;

import com.wbyy.crm.common.annotation.CrmMapping;
import com.wbyy.crm.common.base.BaseDomain;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 商机基本信息表(sales_opportunity)实体类
 *
 * <AUTHOR>
 * @since 2025-03-27 09:05:53
 * @description 
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@Accessors(chain = true)
@TableName("sales_opportunity")
public class SalesOpportunity extends BaseDomain {
    /**
     * 主键id
     */

    /**
     * 商机编码
     */
    @CrmMapping("opportunity_name")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String name;

    /**
     * 商机来源（如线上咨询、客户介绍、广告推广等）
     */
    @CrmMapping("opportunity_input_23")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String source;

    /**
     * 销售机会阶段ID
     */
    @CrmMapping("opportunity_select_11_value")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String statusId;

    /**
     * 销售机会阶段
     */
    @CrmMapping("opportunity_stage")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String statusName;

    /**
     * 项目总价值
     */
    @CrmMapping("opportunity_amount")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private BigDecimal amount;

    /**
     * 成功率
     */
    @CrmMapping("opportunity_select_16")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String successRate;

    /**
     * 商机类型
     */
    @CrmMapping("opportunity_calculation_txt_0")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String opportunityType;

    /**
     * 业务类型
     */
    @CrmMapping("opportunity_select_0")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String businessTypeName;

    /**
     * 业务类型id
     */
    @CrmMapping("opportunity_select_0_value")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String businessTypeId;

    /**
     * 是否招标（0-否，1-是）
     */
    @CrmMapping("opportunity_select_5")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String isBidding;

    /**
     * 是否首次签订（0-否，1-是）
     */
    @CrmMapping("opportunity_select_6")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String isFirstContract;

    /**
     * 销售流程
     */
    @CrmMapping("stage_flow_code")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String salesProcess;

    /**
     * 药品类型
     */
    @CrmMapping("opportunity_select_1")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String drugType;

    /**
     * 注册类型
     */
    @CrmMapping("opportunity_select_2")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String registrationType;

    /**
     * 计划书需求时间
     */
    @CrmMapping("expect_deal_time")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private Date proposalDueDate;

    /**
     * 赢率
     */
    @CrmMapping("opportunity_win_rate")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private BigDecimal winRate;

    /**
     * 项目联系人
     */
    @CrmMapping("opportunity_input_13")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String projectContactName;

    /**
     * 项目联系人id
     */
    private String projectContactId;

    /**
     * 项目联系人职位
     */
    @CrmMapping("opportunity_input_14")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String projectContactPosition;

    /**
     * 拓展对接人
     */
    @CrmMapping("opportunity_refer_object_multi_1")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String expansionContactName;

    /**
     * 拓展对接人id
     */
    @CrmMapping("opportunity_refer_object_multi_id_1")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String expansionContactId;

    /**
     * 商机识别人
     */
    @CrmMapping("opportunity_input_3")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String opportunityIdentifierName;

    /**
     * 技术负责人
     */
    @CrmMapping("opportunity_refer_object_multi_2")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String techLeadName;

    /**
     * 技术负责人id
     */
    @CrmMapping("opportunity_refer_object_multi_id_2")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String techLeadId;

    /**
     * 其他人员
     */
    @CrmMapping("opportunity_input_17")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String otherStaff;

    /**
     * 协作人
     */
    @CrmMapping("opportunity_input_30")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String collaboratorsName1;

    /**
     * 协作人id
     */
    private String collaboratorsId1;

    /**
     * 协作人
     */
    @CrmMapping("opportunity_input_31")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String collaboratorsName2;

    /**
     * 协作人id
     */
    private String collaboratorsId2;

    /**
     * 协作人
     */
    @CrmMapping("opportunity_input_32")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String collaboratorsName3;

    /**
     * 协作人id
     */
    private String collaboratorsId3;

    /**
     * 协作人
     */
    @CrmMapping("opportunity_input_33")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String collaboratorsName4;

    /**
     * 协作人id
     */
    private String collaboratorsId4;

    /**
     * 负责人名称
     */
    @CrmMapping("charger_name")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String ownerName;

    /**
     * 负责人id
     */
    @CrmMapping("charger_id")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String ownerId;

    /**
     * 客户ID（关联客户表）
     */
    @CrmMapping("customer_id")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String customerId;

    /**
     * 客户名称
     */
    @CrmMapping("custom_name")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String customerName;



    /**
     * 商机状态
     */
    @CrmMapping("opportunity_stage")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String opportunityStatus;

    /**
     * 输单原因
     */
    @CrmMapping("lose_single_reason")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String lossReason;

    /**
     * 输单类型
     */
    @CrmMapping("opportunity_select_19")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String lossType;

    /**
     * 输单描述
     */
    @CrmMapping("lose_single_remark")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String lossDescription;

    @CrmMapping("lose_single_stage")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String inputStage;

    @CrmMapping("stage_change_time")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private Date stageChangeTime;

    @CrmMapping("opportunity_auto_number_0")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String opportunityNumber;

    @CrmMapping("opportunity_stage")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String customerStage;


}