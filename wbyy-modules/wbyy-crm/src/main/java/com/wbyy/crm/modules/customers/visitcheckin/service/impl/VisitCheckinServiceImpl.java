package com.wbyy.crm.modules.customers.visitcheckin.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wbyy.crm.common.base.BaseService;
import com.wbyy.crm.modules.contracts.actualreceivables.domain.ActualReceivables;
import com.wbyy.crm.modules.customers.visitcheckin.domain.VisitCheckin;
import com.wbyy.crm.modules.customers.visitcheckin.mapper.VisitCheckinMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import com.wbyy.crm.modules.customers.visitcheckin.service.VisitCheckinService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 拜访签到服务接口实现
 *
 * <AUTHOR>
 * @since 2025-03-26 10:30:38
 * @description 
 */
@Slf4j
@RequiredArgsConstructor
@Service("visitCheckinService")
public class VisitCheckinServiceImpl extends ServiceImpl<VisitCheckinMapper, VisitCheckin> implements VisitCheckinService, BaseService {
    private final VisitCheckinMapper visitCheckinMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void loadData(List<Object> list) {
        List<VisitCheckin> remoteDataList = list.stream().map(VisitCheckin.class::cast).toList();
        Set<Long> remoteIdSet = remoteDataList.stream().map(VisitCheckin::getId).collect(Collectors.toSet());
        List<VisitCheckin> orignalList = this.list();
        Set<Long> toRemoveIdSet = orignalList.stream().map(VisitCheckin::getId).filter(item -> !remoteIdSet.contains(item)).collect(Collectors.toSet());
        if (CollectionUtil.isNotEmpty(toRemoveIdSet)){
            this.removeBatchByIds(toRemoveIdSet);
        }
        log.info("【VisitCheckin】删除数据数：{}",toRemoveIdSet.size());
        if (CollectionUtil.isNotEmpty(remoteDataList)){
            this.saveOrUpdateBatch(remoteDataList);
        }
        log.info("【VisitCheckin】落库数据数：{}",remoteDataList.size());
    }

    @Override
    public List<VisitCheckin> listByCheckinTimeRange(Date startTime, Date endTime) {
        LambdaQueryWrapper<VisitCheckin> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.ge(VisitCheckin::getCheckinTime,startTime);
        queryWrapper.le(VisitCheckin::getCheckinTime,endTime);
        return list(queryWrapper);
    }
}