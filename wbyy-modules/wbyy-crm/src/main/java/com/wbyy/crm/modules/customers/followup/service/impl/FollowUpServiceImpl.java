package com.wbyy.crm.modules.customers.followup.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wbyy.crm.common.base.BaseService;
import com.wbyy.crm.modules.customers.followup.domain.FollowUp;
import com.wbyy.crm.modules.customers.followup.mapper.FollowUpMapper;
import com.wbyy.crm.modules.customers.followup.service.FollowUpService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 跟进记录服务接口实现
 *
 * <AUTHOR>
 * @since 2025-03-26 09:47:49
 * @description
 */
@Slf4j
@RequiredArgsConstructor
@Service("followUpService")
public class FollowUpServiceImpl extends ServiceImpl<FollowUpMapper, FollowUp> implements FollowUpService, BaseService {
    private final FollowUpMapper followUpMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void loadData(List<Object> list) {
        List<FollowUp> remoteDataList = list.stream().map(FollowUp.class::cast).toList();
        Set<Long> remoteIdSet = remoteDataList.stream().map(FollowUp::getId).collect(Collectors.toSet());
        List<FollowUp> orignalList = this.list();
        Set<Long> toRemoveIdSet = orignalList.stream().map(FollowUp::getId).filter(item -> !remoteIdSet.contains(item)).collect(Collectors.toSet());
        if (CollectionUtil.isNotEmpty(toRemoveIdSet)){
            this.removeBatchByIds(toRemoveIdSet);
        }
        log.info("【FollowUp】删除数据数：{}",toRemoveIdSet.size());
        if (CollectionUtil.isNotEmpty(remoteDataList)){
            this.saveOrUpdateBatch(remoteDataList);
        }
        log.info("【FollowUp】落库数据数：{}",remoteDataList.size());
    }

    @Override
    public List<FollowUp> listByCreatedRange(Date startTime, Date endTime) {
        LambdaQueryWrapper<FollowUp> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.ge(FollowUp::getCreated,startTime);
        queryWrapper.le(FollowUp::getCreated,endTime);
        return list(queryWrapper);
    }
}