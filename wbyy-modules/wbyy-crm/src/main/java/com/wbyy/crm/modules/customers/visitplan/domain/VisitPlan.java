package com.wbyy.crm.modules.customers.visitplan.domain;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;

import com.wbyy.crm.common.annotation.CrmMapping;
import com.wbyy.crm.common.base.BaseDomain;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 拜访计划(visit_plan)实体类
 *
 * <AUTHOR>
 * @since 2025-04-08 10:13:47
 * @description 
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@Accessors(chain = true)
@TableName("visit_plan")
public class VisitPlan extends BaseDomain {
    private static final long serialVersionUID = 1L;

    /**
     * 计划内容
     * CRM对应字段: content
     */
    @CrmMapping("content")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String planContent;

    /**
     * 客户名称
     * CRM对应字段: custom_name
     */
    @CrmMapping("custom_name")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String customerName;

    /**
     * 客户 ID
     * CRM对应字段: custom_id
     */
    @CrmMapping("custom_id")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private Long customerId;

    /**
     * 计划拜访时间
     * CRM对应字段: begin_time
     */
    @CrmMapping("begin_time")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private Date startTime;

    /**
     * 计划结束时间
     * CRM对应字段: end_time
     */
    @CrmMapping("end_time")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private Date endTime;

    /**
     * 负责人名称
     * CRM对应字段: charger_name
     */
    @CrmMapping("charger_name")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String ownerName;

    /**
     * 负责人 ID
     * CRM对应字段: charger_id
     */
    @CrmMapping("charger_id")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String ownerId;

    /**
     * 协作人
     * CRM对应字段: collaborator_name
     */
    @CrmMapping("collaborator_name")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String collaboratorsName;

    /**
     * 协作人 ID
     * CRM对应字段: collaborator_id
     */
    @CrmMapping("collaborator_id")
    @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private String collaboratorsId;
    

}