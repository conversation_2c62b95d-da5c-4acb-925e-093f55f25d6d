package com.wbyy.crm.modules.salescenter.annualgoalscompany.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wbyy.crm.common.base.BaseService;
import com.wbyy.crm.modules.salescenter.annualgoalscompany.domain.AnnualGoalsCompany;
import com.wbyy.crm.modules.salescenter.annualgoalscompany.mapper.AnnualGoalsCompanyMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import com.wbyy.crm.modules.salescenter.annualgoalscompany.service.AnnualGoalsCompanyService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 公司年度目标表服务接口实现
 *
 * <AUTHOR>
 * @since 2025-03-26 14:58:03
 * @description 
 */
@Slf4j
@RequiredArgsConstructor
@Service("annualGoalsCompanyService")
public class AnnualGoalsCompanyServiceImpl extends ServiceImpl<AnnualGoalsCompanyMapper, AnnualGoalsCompany> implements AnnualGoalsCompanyService, BaseService {
    private final AnnualGoalsCompanyMapper annualGoalsCompanyMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void loadData(List<Object> list) {
        List<AnnualGoalsCompany> remoteDataList = list.stream().map(AnnualGoalsCompany.class::cast).toList();
        Set<Long> remoteIdSet = remoteDataList.stream().map(AnnualGoalsCompany::getId).collect(Collectors.toSet());
        List<AnnualGoalsCompany> orignalList = this.list();
        Set<Long> toRemoveIdSet = orignalList.stream().map(AnnualGoalsCompany::getId).filter(item -> !remoteIdSet.contains(item)).collect(Collectors.toSet());
        if (CollectionUtil.isNotEmpty(toRemoveIdSet)){
            this.removeBatchByIds(toRemoveIdSet);
        }
        log.info("【AnnualGoalsCompany】删除数据数：{}",toRemoveIdSet.size());
        if (CollectionUtil.isNotEmpty(remoteDataList)){
            this.saveOrUpdateBatch(remoteDataList);
        }
        log.info("【AnnualGoalsCompany】落库数据数：{}",remoteDataList.size());
    }
}