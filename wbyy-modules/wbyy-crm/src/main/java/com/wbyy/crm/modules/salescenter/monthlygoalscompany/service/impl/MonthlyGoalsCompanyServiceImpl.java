package com.wbyy.crm.modules.salescenter.monthlygoalscompany.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wbyy.crm.common.base.BaseService;
import com.wbyy.crm.modules.salescenter.monthlygoalscompany.domain.MonthlyGoalsCompany;
import com.wbyy.crm.modules.salescenter.monthlygoalscompany.mapper.MonthlyGoalsCompanyMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import com.wbyy.crm.modules.salescenter.monthlygoalscompany.service.MonthlyGoalsCompanyService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 公司月度目标表服务接口实现
 *
 * <AUTHOR>
 * @since 2025-03-26 15:40:45
 * @description 
 */
@Slf4j
@RequiredArgsConstructor
@Service("monthlyGoalsCompanyService")
public class MonthlyGoalsCompanyServiceImpl extends ServiceImpl<MonthlyGoalsCompanyMapper, MonthlyGoalsCompany> implements MonthlyGoalsCompanyService, BaseService {
    private final MonthlyGoalsCompanyMapper monthlyGoalsCompanyMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void loadData(List<Object> list) {
        List<MonthlyGoalsCompany> remoteDataList = list.stream().map(MonthlyGoalsCompany.class::cast).toList();
        Set<Long> remoteIdSet = remoteDataList.stream().map(MonthlyGoalsCompany::getId).collect(Collectors.toSet());
        List<MonthlyGoalsCompany> orignalList = this.list();
        Set<Long> toRemoveIdSet = orignalList.stream().map(MonthlyGoalsCompany::getId).filter(item -> !remoteIdSet.contains(item)).collect(Collectors.toSet());
        if (CollectionUtil.isNotEmpty(toRemoveIdSet)){
            this.removeBatchByIds(toRemoveIdSet);
        }
        log.info("【MonthlyGoalsCompany】删除数据数：{}",toRemoveIdSet.size());
        if (CollectionUtil.isNotEmpty(remoteDataList)){
            this.saveOrUpdateBatch(remoteDataList);
        }
        log.info("【MonthlyGoalsCompany】落库数据数：{}",remoteDataList.size());
    }
}