package com.wbyy.crm.modules.contracts.contract.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.wbyy.crm.modules.contracts.contract.domain.Contract;

import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * 合同基本信息表服务接口
 *
 * <AUTHOR>
 * @since 2025-03-26 11:03:37
 * @description 
 */
public interface ContractService extends IService<Contract> {

    Set<String> userNames();

    List<Contract> listByContractDateRange(Date startTime, Date endTime);

}
