<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wbyy.crm.modules.contracts.actualreceivables.mapper.ActualReceivablesMapper">
    <sql id="tableName">
        actual_receivables
    </sql>

    <sql id="baseColumn">
        id,transaction_no,serial_number,department,department_id,contract_name,contract_id,contract_data_no,project_code,due_date,business_date,contract_order_no,contract_order_title,contract_order_id,planned_payment_period,expected_payment_amount,actual_payment_amount,actual_payment_no,invoice_issued,payment_milestone,is_payment_received,invoice_date,invoice_amount,currency_name,discount_amount,payment_type,payment_method,is_advance_payment,created_by_name,created_by_id,updated_by_name,updated_by_id,updated_at,created_at
    </sql>

</mapper>