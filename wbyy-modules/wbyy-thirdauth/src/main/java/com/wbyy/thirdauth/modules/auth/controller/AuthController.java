package com.wbyy.thirdauth.modules.auth.controller;

import com.wbyy.thirdauth.modules.auth.service.IKingdeeAuthService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * @author: 王金都
 * @date: 2025/5/28 14:22
 */
@RestController
@RequestMapping("/auth")
@RequiredArgsConstructor
public class AuthController {

    private final IKingdeeAuthService kingdeeAuthService;

    @GetMapping("/kingdee")
    public ResponseEntity<Void> kingdee(@RequestParam("eteams_token")String eteamsToken){
        String authUrl = kingdeeAuthService.getAuthUrl(eteamsToken);
        return ResponseEntity.status(302).header("Location", authUrl).build();
    }
}
