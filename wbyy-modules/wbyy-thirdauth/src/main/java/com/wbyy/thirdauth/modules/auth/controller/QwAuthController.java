package com.wbyy.thirdauth.modules.auth.controller;

import com.wbyy.common.core.constant.SecurityConstants;
import com.wbyy.common.core.domain.R;
import com.wbyy.common.core.web.controller.BaseController;
import com.wbyy.thirdauth.modules.auth.service.IQwAuthService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2025/6/19  10:23
 * @description 企微登录服务
 */
@RestController
@RequestMapping("/auth/qw/")
@RequiredArgsConstructor
public class QwAuthController extends BaseController {

    private final IQwAuthService qwAuthService;

    @GetMapping("/login")
    public R<?> qwLogin(@RequestParam("code") String code) {
        String applicationCode = getRequest().getHeader(SecurityConstants.APPLICATION_CODE);
        return R.ok(qwAuthService.qwLogin(code, applicationCode));
    }
}
