package com.wbyy.thirdauth.modules.auth.controller;

import cn.hutool.core.util.StrUtil;
import com.wbyy.common.core.constant.SecurityConstants;
import com.wbyy.common.core.domain.R;
import com.wbyy.common.core.exception.ServiceException;
import com.wbyy.common.core.utils.ServletUtils;
import com.wbyy.thirdauth.modules.auth.service.ISsoService;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2025/7/22 9:08
 */
@Slf4j
@RestController
@RequestMapping("/sso")
@RequiredArgsConstructor
public class SsoController {

    private final ISsoService ssoService;

    @GetMapping("/jump")
    public ResponseEntity<Void> jump(@RequestParam("eteams_token")String eteamsToken,
                                     @RequestParam(value = "appCode",required = false)String appCode){
        if (StrUtil.isBlank(appCode)){
            HttpServletRequest httpServletRequest = ServletUtils.getRequest();
            if (null!=httpServletRequest){
                appCode = httpServletRequest.getHeader(SecurityConstants.APPLICATION_CODE);
            }
        }
        if (StrUtil.isBlank(appCode)){
            throw new ServiceException("应用编码不得为空");
        }
        String toUrl = ssoService.getToUrl(eteamsToken,appCode);
        return ResponseEntity.status(302).header("Location", toUrl).build();
    }

    @GetMapping("/url")
    public R<String> url(@RequestParam(value = "appCode",required = false)String appCode){
        if (StrUtil.isBlank(appCode)){
            HttpServletRequest httpServletRequest = ServletUtils.getRequest();
            if (null!=httpServletRequest){
                appCode = httpServletRequest.getHeader(SecurityConstants.APPLICATION_CODE);
            }
        }
        if (StrUtil.isBlank(appCode)){
            throw new ServiceException("应用编码不得为空");
        }
        String ssoUrl = ssoService.getSsoUrl(appCode);
        return R.ok(ssoUrl);
    }
}
