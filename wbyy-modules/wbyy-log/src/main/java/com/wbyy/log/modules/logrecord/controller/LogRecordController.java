package com.wbyy.log.modules.logrecord.controller;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.wbyy.common.core.web.controller.BaseController;
import com.wbyy.common.core.web.page.TableDataInfo;
import com.wbyy.common.security.annotation.RequiresPermissions;
import com.wbyy.log.modules.logrecord.domain.LogRecord;
import com.wbyy.log.modules.logrecord.service.LogRecordService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("log-record")
@Tag(name = "日志记录")
public class LogRecordController extends BaseController {

    @Autowired
    private LogRecordService logRecordService;

    @RequiresPermissions("log-server:log-record:list")
    @GetMapping("list")
    @Operation(summary = "列表")
    public TableDataInfo list(@ParameterObject LogRecord dto){
        dto.buildSearchValue();
        startPage();
        return getDataTable(logRecordService
                .list(new LambdaQueryWrapper<LogRecord>()
                        .like(StrUtil.isNotBlank(dto.getUserName()),LogRecord::getUserName,dto.getUserName())
                        .like(StrUtil.isNotBlank(dto.getDeptName()),LogRecord::getDeptName,dto.getDeptName())
                        .like(StrUtil.isNotBlank(dto.getIp()),LogRecord::getIp,dto.getIp())
                        .ge(null!=dto.getOperationStartTime(),LogRecord::getOperationTime,dto.getOperationStartTime())
                        .le(null!=dto.getOperationEndTime(),LogRecord::getOperationTime,dto.getOperationEndTime())
                        .like(StrUtil.isNotBlank(dto.getIntegrateCode()),LogRecord::getIntegrateCode,dto.getIntegrateCode())
                        .like(StrUtil.isNotBlank(dto.getModule()),LogRecord::getModule,dto.getModule())
                        .like(StrUtil.isNotBlank(dto.getDataModule()),LogRecord::getDataModule,dto.getDataModule())
                        .like(StrUtil.isNotBlank(dto.getDataModuleId()),LogRecord::getDataModuleId,dto.getDataModuleId())
                        .like(StrUtil.isNotBlank(dto.getBizName()),LogRecord::getBizName,dto.getBizName())
                        .like(StrUtil.isNotBlank(dto.getBizId()),LogRecord::getBizId,dto.getBizId())
                        .like(StrUtil.isNotBlank(dto.getBizCode()),LogRecord::getBizCode,dto.getBizCode())
                        .like(StrUtil.isNotBlank(dto.getOperationType()),LogRecord::getOperationType,dto.getOperationType())
                        .like(StrUtil.isNotBlank(dto.getContent()),LogRecord::getContent,dto.getContent())
                        .orderByDesc(LogRecord::getOperationTime)
                ));
    }
}
