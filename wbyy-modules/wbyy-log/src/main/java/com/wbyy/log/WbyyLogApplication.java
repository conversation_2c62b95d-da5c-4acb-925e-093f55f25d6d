package com.wbyy.log;

import com.wbyy.common.security.annotation.EnableCustomConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import com.wbyy.common.security.annotation.EnableRyFeignClients;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;

/**
 * log
 * 
 * <AUTHOR>
 */
@Slf4j
@EnableCustomConfig
@EnableRyFeignClients
@SpringBootApplication
public class WbyyLogApplication
{
    public static void main(String[] args)
    {
        SpringApplication.run(WbyyLogApplication.class, args);
        System.out.println("(♥◠‿◠)ﾉﾞ  LOG模块启动成功   ლ(´ڡ`ლ)ﾞ  \n");
        log.info("(♥◠‿◠)ﾉﾞ  LOG模块启动成功   ლ(´ڡ`ლ)ﾞ  \n");
    }
}
