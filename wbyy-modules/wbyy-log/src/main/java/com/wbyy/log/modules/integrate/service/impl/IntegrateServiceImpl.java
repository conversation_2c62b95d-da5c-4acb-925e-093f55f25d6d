package com.wbyy.log.modules.integrate.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.wbyy.common.core.exception.ServiceException;
import com.wbyy.log.modules.integrate.domain.Integrate;
import com.wbyy.log.modules.integrate.mapper.IntegrateMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import com.wbyy.log.modules.integrate.service.IntegrateService;
import org.springframework.stereotype.Service;

/**
 * 应用集成表服务接口实现
 *
 * <AUTHOR>
 * @since 2025-04-15 09:46:33
 * @description 
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class IntegrateServiceImpl extends ServiceImpl<IntegrateMapper, Integrate> implements IntegrateService {
    private final IntegrateMapper integrateMapper;

    private static final Object CODE_LOCK = new Object();

    @Override
    public void addNew(Integrate dto) {
        synchronized (CODE_LOCK){
            Integrate byCode = getByCode(dto.getCode());
            if (null!=byCode) throw new ServiceException("应用标识已存在");
            this.save(dto);
        }
    }

    @Override
    public  void update(Integrate dto) {
        synchronized (CODE_LOCK){
            Integrate byCode = getByCode(dto.getCode());
            if (null!=byCode&&byCode.getId().equals(dto.getId())){
                throw new ServiceException("应用标识已存在");
            }
            this.updateById(dto);
        }
    }

    @Override
    public Integrate getByCodeByToken(String code, String token) {
        LambdaQueryWrapper<Integrate> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Integrate::getCode,code);
        queryWrapper.eq(Integrate::getToken,token);
        return getOne(queryWrapper,false);
    }

    private Integrate getByCode(String code){
        LambdaQueryWrapper<Integrate> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Integrate::getCode,code);
        return this.getOne(queryWrapper,false);
    }
}