package com.wbyy.weaver.integration.employee.controller;

import com.wbyy.common.core.domain.R;
import com.wbyy.common.core.exception.ServiceException;
import com.wbyy.common.security.annotation.InnerAuth;
import com.wbyy.weaver.common.config.ConfigProperties;
import com.wbyy.weaver.common.constant.ConfigConst;
import com.wbyy.weaver.common.helper.OAuth2TokenHelper;
import com.weaver.openapi.pojo.NormalResult;
import com.weaver.openapi.pojo.user.params.UserVo;
import com.weaver.openapi.pojo.user.res.DepartmentUserResultVo;
import com.weaver.openapi.pojo.user.res.QueryEmployeeResultVo;
import com.weaver.openapi.pojo.user.res.vo.QueryEmployeeData;
import com.weaver.openapi.pojo.user.res.vo.UserInfoResult;
import com.weaver.openapi.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @author: 王金都
 * @date: 2025/4/27 16:26
 */
@RestController
@RequestMapping("employee")
public class EmployeeController {
    @Autowired
    private OAuth2TokenHelper oAuth2TokenHelper;
    @Autowired
    private ConfigProperties configProperties;

    @PostMapping("query")
    @InnerAuth
    public R<QueryEmployeeData> query(@RequestBody UserVo dto){
        String accessToken = oAuth2TokenHelper.getAccessToken();
        dto.setAccessToken(accessToken);
        QueryEmployeeResultVo vo = UserService.queryEmployee(dto, configProperties.getBaseUrl(), null);
        NormalResult message = vo.getMessage();
        if (null!=message&& !ConfigConst.SUCCESS_CODE.equals(message.getErrcode())){
            throw new ServiceException("用户获取失败，"+message.getErrmsg());
        }
        return R.ok(vo.getData());
    }

    @PostMapping("department-users-v3")
    @InnerAuth
    public R<List<UserInfoResult>> departmentUsersV3(@RequestBody UserVo dto){
        String accessToken = oAuth2TokenHelper.getAccessToken();
        dto.setAccessToken(accessToken);
        DepartmentUserResultVo vo = UserService.departmentUsersV3(dto, configProperties.getBaseUrl(), null);
        NormalResult message = vo.getMessage();
        if (null!=message&&!ConfigConst.SUCCESS_CODE.equals(message.getErrcode())){
            throw new ServiceException("用户获取失败，"+message.getErrmsg());
        }
        return R.ok(vo.getUser());
    }
}
