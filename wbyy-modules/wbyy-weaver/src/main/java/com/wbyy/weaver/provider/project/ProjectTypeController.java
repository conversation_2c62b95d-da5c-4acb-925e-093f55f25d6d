package com.wbyy.weaver.provider.project;

import com.alibaba.fastjson2.JSONArray;
import com.wbyy.common.core.constant.SecurityConstants;
import com.wbyy.common.core.domain.R;
import com.wbyy.pms.api.ProjectTypeApi;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2025/7/28 17:47
 */
@RestController
@RequestMapping("/provider/project/type")
@RequiredArgsConstructor
public class ProjectTypeController {

    private final ProjectTypeApi projectTypeApi;

    @GetMapping("/list")
    public R<JSONArray> list(){
        return projectTypeApi.simpleTree(SecurityConstants.INNER);
    }
}
