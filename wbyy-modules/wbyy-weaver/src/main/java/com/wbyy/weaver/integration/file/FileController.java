package com.wbyy.weaver.integration.file;

import com.wbyy.common.core.domain.R;
import com.wbyy.common.security.annotation.InnerAuth;
import com.wbyy.weaver.common.config.ConfigProperties;
import com.wbyy.weaver.common.helper.OAuth2TokenHelper;
import com.weaver.openapi.pojo.file.params.FileVo;
import com.weaver.openapi.pojo.file.res.vo.FileData;
import com.weaver.openapi.service.FileService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;

/**
 * @author: 王金都
 * @date: 2025/6/3 10:29
 */
@RestController
@RequestMapping("/file")
@Slf4j
public class FileController {

    @Autowired
    private OAuth2TokenHelper oAuth2TokenHelper;
    @Autowired
    private ConfigProperties configProperties;

    @GetMapping("/download-file-v2")
    @InnerAuth
    public R<FileData> downloadFileV2(@RequestParam("fileId")String fileId,@RequestParam("userId")String userId) throws IOException {
        FileVo fileVo = new FileVo();
        fileVo.setAccessToken(oAuth2TokenHelper.getAccessToken());
        fileVo.setFileId(fileId);
        fileVo.setUserid(Long.valueOf(userId));
        FileData fileData = FileService.downloadFileDataV2(fileVo, configProperties.getBaseUrl(), null);
        return R.ok(fileData);
    }
}
