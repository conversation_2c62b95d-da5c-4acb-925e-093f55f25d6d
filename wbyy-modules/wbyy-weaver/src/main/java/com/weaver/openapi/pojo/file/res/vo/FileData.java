package com.weaver.openapi.pojo.file.res.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

/**
 * @author: 王金都
 * @date: 2025/6/3 11:19
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class FileData implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;
    private byte[] bytes;
    private String contentType;
    private String filename;
}
