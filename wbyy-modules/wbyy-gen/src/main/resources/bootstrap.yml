# Tomcat
server:
  port: 8800

# Spring
spring: 
  application:
    # 应用名称
    name: wbyy-gen
  profiles:
    # 环境配置
    active: dev
  cloud:
    nacos:
      discovery:
        # 服务注册地址
        server-addr: 121.41.167.10:8848
        # 命名空间
        namespace: 6f340a26-b61b-4bd9-adce-681f7ba8c9e9
      config:
        # 配置中心地址
        server-addr: 121.41.167.10:8848
        # 配置文件格式
        file-extension: yml
        # 共享配置
        shared-configs:
          - application-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
          - application-redis-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
          - application-datasource-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
          - application-mybatis-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
        # 命名空间
        namespace: 6f340a26-b61b-4bd9-adce-681f7ba8c9e9
      username: nacos
      password: Wbyy_nacos_e__fasa3w
