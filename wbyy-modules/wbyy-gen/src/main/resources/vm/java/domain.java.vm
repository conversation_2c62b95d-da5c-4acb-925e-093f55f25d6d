package ${packageName}.domain;

#foreach ($import in $importList)
import ${import};
#end
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;
import com.wbyy.common.core.annotation.Excel;
#if($table.crud || $table.sub)
import com.wbyy.orm.core.domain.BaseAuditEntity;
#elseif($table.tree)
import com.wbyy.common.core.domain.web.TreeEntity;
#end

import java.io.Serial;

/**
 * ${functionName}对象 ${tableName}
 *
 * <AUTHOR>
 * @date ${datetime}
 */
#if($table.crud || $table.sub)
#set($Entity="BaseAuditEntity")
#elseif($table.tree)
#set($Entity="TreeEntity")
#end
@Data
@SuperBuilder
@NoArgsConstructor
@Accessors(chain = true)
@TableName(value = "${tableName}", autoResultMap = true)
@Schema(description = "${functionName}实体类")
@EqualsAndHashCode(callSuper = true)
public class ${ClassName}PO extends ${Entity} {

    @Serial
    private static final long serialVersionUID = 1L;

#foreach ($column in $columns)
#if(!$table.isSuperColumn($column.javaField))
    /** $column.columnComment */
#if($column.list)
#set($parentheseIndex=$column.columnComment.indexOf("（"))
#if($parentheseIndex != -1)
#set($comment=$column.columnComment.substring(0, $parentheseIndex))
#else
#set($comment=$column.columnComment)
#end
#if($parentheseIndex != -1)
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
#elseif($column.javaType == 'Date')
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "${comment}", width = 30, dateFormat = "yyyy-MM-dd")
#else
    @Excel(name = "${comment}")
#end
#end
#if ($column.isRequired == '1')
    @Schema(description = "$column.columnComment", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "${comment}不能为空")
#elseif($column.isPk == '1')
    @Schema(description = "$column.columnComment（新增：不传；修改：必传）")
#else
    @Schema(description = "$column.columnComment")
#end
#if($column.javaType == 'String' && !$table.isNull($column.columnLength) && $column.columnLength > 0)
    @Size(max = $column.columnLength, message = "${comment}不能超过 $column.columnLength 个字符")
#end
#if($column.javaField == 'delFlag')
    @TableLogic
    private $column.javaType $column.javaField;

#else
    private $column.javaType $column.javaField;

#end
#end
#end


#if($table.sub)
    /** $table.subTable.functionName信息 */
    @Schema(description = "$table.subTable.functionName信息")
    private List<${subClassName}> ${subclassName}List;
#end

#if($table.sub)
    public List<${subClassName}> get${subClassName}List() {
        return ${subclassName}List;
    }

    public void set${subClassName}List(List<${subClassName}> ${subclassName}List) {
        this.${subclassName}List = ${subclassName}List;
    }

#end
}
