package ${packageName}.service.impl;

import java.util.List;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
#foreach ($column in $columns)
#if($column.javaField == 'createTime' || $column.javaField == 'updateTime')
#break
#end
#end
import org.springframework.stereotype.Service;
#if($table.sub)
import java.util.ArrayList;

import com.wbyy.common.core.utils.StringUtils;
import org.springframework.transaction.annotation.Transactional;
import ${packageName}.domain.${subClassName};
#end
import ${packageName}.dao.I${ClassName}Dao;
import ${packageName}.domain.${ClassName}PO;
import ${packageName}.service.I${ClassName}Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * ${functionName}Service业务层处理
 *
 * <AUTHOR>
 * @date ${datetime}
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ${ClassName}ServiceImpl implements I${ClassName}Service {
    private final I${ClassName}Dao ${className}Dao;

    @Override
    public ${ClassName}PO selectBy${pkColumn.capJavaField}(${pkColumn.javaType} ${pkColumn.javaField}) {

        return ${className}Dao.selectBy${pkColumn.capJavaField}(${pkColumn.javaField});
    }

    @Override
    public List<${ClassName}PO> selectList(${ClassName}PO po) {

        return ${className}Dao.selectList(po);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean insert(${ClassName}PO po) {

        return ${className}Dao.insert(po);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean update(${ClassName}PO po) {

        return ${className}Dao.update(po);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteBy${pkColumn.capJavaField}s(${pkColumn.javaType}[] ${pkColumn.javaField}s) {

        return ${className}Dao.deleteBy${pkColumn.capJavaField}s(${pkColumn.javaField}s);
    }
}
