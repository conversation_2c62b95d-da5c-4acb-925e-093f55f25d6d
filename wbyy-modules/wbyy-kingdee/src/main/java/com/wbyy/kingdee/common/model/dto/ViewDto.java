package com.wbyy.kingdee.common.model.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.wbyy.common.core.exception.ServiceException;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import static com.wbyy.kingdee.common.config.jackson.StaticMapper.OBJECT_MAPPER;

@EqualsAndHashCode(callSuper = true)
@Data
@Slf4j
@NoArgsConstructor
public class ViewDto extends AbstractBaseDto {
    @JsonProperty("CreateOrgId")
    private Integer createOrgId;
    @JsonProperty("Number")
    private String number;
    @JsonProperty("Id")
    private String id;
    @JsonProperty("IsSortBySeq")
    private Boolean isSortBySeq;

    public String toParamString(){
        try {
            return OBJECT_MAPPER.writeValueAsString(this);
        } catch (JsonProcessingException e) {
            log.error("【ViewDto】转参数失败",e);
            throw new ServiceException("【ViewDto】转参数失败");
        }
    }
}
