package com.wbyy.common.rabbitmq.domain;

import com.wbyy.orm.core.domain.BaseAuditEntity;
import lombok.*;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

import java.io.Serial;

@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class RabbitmqErrorMsg extends BaseAuditEntity {

    @Serial
    private static final long serialVersionUID = 7687649297055585319L;


    private Long id;
    /**
     * 数据类型
     */
    private String className;
    /**
     * 数据
     */
    private String data;
    /**
     * 异常信息
     */
    private String errorMsg;
    /**
     * 交换机
     */
    private String exchange;
    /**
     * 队列
     */
    private String queue;
    /**
     * 路由key
     */
    private String routingKey;

    /**
     * 状态 0-未操作 1-已操作
     */
    private Integer status;
    /**
     * 类型 0-发送 1-消费
     */
    private Integer type;
}
