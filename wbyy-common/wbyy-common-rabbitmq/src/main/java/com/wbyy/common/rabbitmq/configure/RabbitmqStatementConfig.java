package com.wbyy.common.rabbitmq.configure;

import org.springframework.amqp.core.*;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;
import java.util.Map;

@Configuration
public class RabbitmqStatementConfig {

    public static final String DELAY_EXCHANGE_NAME = "wbyy.delay.exchange";

    // 消息重投队列(延时)
    public static final String MESSAGE_RESEND_QUEUE = "message-resend-queue";
    public static final String MESSAGE_RESEND_ROUTING_KEY = "message.resend.routing.key";

    // ========================= 消息重投队列 =============================

    /**
     * 声明交换机
     */
    @Bean(DELAY_EXCHANGE_NAME)
    public CustomExchange exchange() {
        Map<String, Object> args = new HashMap<>();
        args.put("x-delayed-type", "direct");
        return new CustomExchange(DELAY_EXCHANGE_NAME, "x-delayed-message", true, false, args);
    }


    @Bean(MESSAGE_RESEND_QUEUE)
    public Queue messageResendQueue() {
        return new Queue(MESSAGE_RESEND_QUEUE);
    }

    @Bean
    public Binding bindingMessageResendNotify(@Qualifier(MESSAGE_RESEND_QUEUE) Queue queue,
                                              @Qualifier(DELAY_EXCHANGE_NAME) Exchange exchange) {
        return BindingBuilder.bind(queue).to(exchange).with(MESSAGE_RESEND_ROUTING_KEY).noargs();
    }
    // ========================= 消息重投队列 =============================
}
