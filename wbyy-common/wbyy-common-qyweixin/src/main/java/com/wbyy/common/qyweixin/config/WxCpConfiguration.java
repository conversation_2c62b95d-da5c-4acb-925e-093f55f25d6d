package com.wbyy.common.qyweixin.config;

import cn.hutool.core.collection.CollectionUtil;
import com.google.common.collect.Maps;
import com.wbyy.common.qyweixin.config.handler.IWxCpMessageHandler;
import com.wbyy.common.qyweixin.config.properties.AppConfig;
import com.wbyy.common.qyweixin.config.properties.WxCpProperties;
import jakarta.annotation.PostConstruct;
import lombok.Getter;
import lombok.val;
import me.chanjar.weixin.cp.api.WxCpService;
import me.chanjar.weixin.cp.api.impl.WxCpServiceImpl;
import me.chanjar.weixin.cp.config.impl.WxCpDefaultConfigImpl;
import me.chanjar.weixin.cp.message.WxCpMessageRouter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
public class WxCpConfiguration {
    @Autowired
    private WxCpProperties properties;
    @Autowired(required = false)
    private List<IWxCpMessageHandler> wxCpMessageHandlerList;

    @Getter
    private static Map<String, WxCpMessageRouter> routers = Maps.newHashMap();
    private static Map<String, WxCpService> cpServices = Maps.newHashMap();
    private static Map<String, AppConfig> appConfigs = Maps.newHashMap();

    public static AppConfig getAppConfig(String agentCode) {
        return appConfigs.get(agentCode);
    }

    public static WxCpService getCpService(String agentCode) {
        return cpServices.get(agentCode);
    }

    @PostConstruct
    public void initServices() {
        this.properties.getAppConfigs().forEach(a -> {
            val configStorage = new WxCpDefaultConfigImpl();
            configStorage.setCorpId(this.properties.getCorpId());
            configStorage.setAgentId(a.getAgentId());
            configStorage.setCorpSecret(a.getSecret());
            configStorage.setToken(a.getToken());
            configStorage.setAesKey(a.getAesKey());
            val service = new WxCpServiceImpl();
            service.setWxCpConfigStorage(configStorage);
            routers.put(a.getAgentCode(), this.newRouter(service));
            cpServices.put(a.getAgentCode(),service);
        });

        appConfigs = this.properties.getAppConfigs().stream().collect(Collectors.toMap(AppConfig::getAgentCode, a -> a));
    }

    private WxCpMessageRouter newRouter(WxCpService wxCpService) {
        val newRouter = new WxCpMessageRouter(wxCpService);
        if (CollectionUtil.isNotEmpty(wxCpMessageHandlerList)){
            for (IWxCpMessageHandler handler : wxCpMessageHandlerList) {
                newRouter.rule().async(false).msgType(handler.getMsgType())
                        .event(handler.getEvent()).handler(handler).end();
            }
        }
        return newRouter;
    }
}
