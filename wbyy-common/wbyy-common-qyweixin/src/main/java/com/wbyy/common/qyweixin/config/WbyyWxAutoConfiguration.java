package com.wbyy.common.qyweixin.config;

import com.wbyy.common.qyweixin.config.handler.IWxCpMessageHandler;
import com.wbyy.common.qyweixin.config.properties.WxCpProperties;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * @author: 王金都
 * @date: 2025/5/30 11:24
 */
@Configuration
@EnableConfigurationProperties(WxCpProperties.class)
public class WbyyWxAutoConfiguration {

    @Bean
    public WxCpConfiguration wxCpConfiguration(){
        return new WxCpConfiguration();
    }
}
