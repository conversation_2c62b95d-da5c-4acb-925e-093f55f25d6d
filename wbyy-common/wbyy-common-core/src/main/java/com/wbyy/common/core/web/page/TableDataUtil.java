package com.wbyy.common.core.web.page;

import com.github.pagehelper.PageInfo;
import com.wbyy.common.core.constant.HttpStatus;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/7/7  17:01
 * @description 分页数据构造工具类
 */
public class TableDataUtil {

    /**
     * 返回分页数据，无需继承BaseController
     *
     * @param list 数据
     * @param <T>  数据类型
     * @return 分页数据
     */
    public static <T> TableDataInfo<T> buildInfo(List<T> list) {
        return buildInfo(list, null);
    }

    /**
     * 返回分页数据，无需继承BaseController
     *
     * @param list  数据
     * @param total 分页total，不传则使用PageInfo获取
     * @param <T>   数据类型
     * @param <V>   total
     * @return 分页数据
     */
    public static <T, V extends Number> TableDataInfo<T> buildInfo(List<T> list, V total) {
        TableDataInfo<T> tableDataInfo = new TableDataInfo<>();
        tableDataInfo.setCode(HttpStatus.SUCCESS);
        tableDataInfo.setRows(list);
        tableDataInfo.setMsg("查询成功");
        tableDataInfo.setTotal(null != total ? total.intValue() : Math.toIntExact(new PageInfo(list).getTotal()));
        return tableDataInfo;
    }
}
