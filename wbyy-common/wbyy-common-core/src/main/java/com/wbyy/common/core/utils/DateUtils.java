package com.wbyy.common.core.utils;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.Week;
import com.wbyy.common.core.exception.ServiceException;
import org.apache.commons.lang3.time.DateFormatUtils;

import java.lang.management.ManagementFactory;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 时间工具类
 *
 * <AUTHOR>
 */
public class DateUtils extends org.apache.commons.lang3.time.DateUtils {

    public static final long MILLISECONDS_PER_DAY = 24 * 60 * 60 * 1000;

    public static String YYYY = "yyyy";

    public static String YYYY_MM = "yyyy-MM";

    public static String YYYY_MM_DD = "yyyy-MM-dd";
    public static String YYYYMMDD = "yyyyMMdd";

    public static String YYYYMMDDHHMMSS = "yyyyMMddHHmmss";

    public static String YYYY_MM_DD_HH_MM_SS = "yyyy-MM-dd HH:mm:ss";

    private static String[] parsePatterns = {
            "yyyy-MM-dd", "yyyy-MM-dd HH:mm:ss", "yyyy-MM-dd HH:mm", "yyyy-MM",
            "yyyy/MM/dd", "yyyy/MM/dd HH:mm:ss", "yyyy/MM/dd HH:mm", "yyyy/MM",
            "yyyy.MM.dd", "yyyy.MM.dd HH:mm:ss", "yyyy.MM.dd HH:mm", "yyyy.MM"};

    /**
     * 获取当前Date型日期
     *
     * @return Date() 当前日期
     */
    public static Date getNowDate() {
        return new Date();
    }
    public static Date getNowDateByDayStart() {
        try {
            return parseDate(dateTime(), YYYYMMDD);
        } catch (ParseException e) {
            throw new ServiceException(e);
        }
    }

    /**
     * 获取当前日期, 默认格式为yyyy-MM-dd
     *
     * @return String
     */
    public static String getDate() {
        return dateTimeNow(YYYY_MM_DD);
    }

    public static final String getTime() {
        return dateTimeNow(YYYY_MM_DD_HH_MM_SS);
    }

    public static final String dateTimeNow() {
        return dateTimeNow(YYYYMMDDHHMMSS);
    }

    public static final String dateTimeNow(final String format) {
        return parseDateToStr(format, new Date());
    }

    public static final String dateTime(final Date date) {
        return parseDateToStr(YYYY_MM_DD, date);
    }

    public static final String parseDateToStr(final String format, final Date date) {
        return new SimpleDateFormat(format).format(date);
    }
    public static final String parseToStr(final Date date) {
        if (date == null) {
            return null;
        }
        return new SimpleDateFormat(YYYY_MM_DD).format(date);
    }

    public static final Date dateTime(final String format, final String ts) {
        try {
            return new SimpleDateFormat(format).parse(ts);
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 日期路径 即年/月/日 如2018/08/08
     */
    public static final String datePath() {
        Date now = new Date();
        return DateFormatUtils.format(now, "yyyy/MM/dd");
    }

    /**
     * 日期路径 即年/月/日 如20180808
     */
    public static final String dateTime() {
        Date now = new Date();
        return DateFormatUtils.format(now, "yyyyMMdd");
    }

    /**
     * 日期型字符串转化为日期 格式
     */
    public static Date parseDate(Object str) {
        if (str == null) {
            return null;
        }
        try {
            return parseDate(str.toString(), parsePatterns);
        } catch (ParseException e) {
            return null;
        }
    }

    /**
     * 获取服务器启动时间
     */
    public static Date getServerStartDate() {
        long time = ManagementFactory.getRuntimeMXBean().getStartTime();
        return new Date(time);
    }

    /**
     * 计算时间差
     *
     * @param endDate   最后时间
     * @param startTime 开始时间
     * @return 时间差（天/小时/分钟）
     */
    public static String timeDistance(Date endDate, Date startTime) {
        long nd = MILLISECONDS_PER_DAY;
        long nh = 1000 * 60 * 60;
        long nm = 1000 * 60;
        // long ns = 1000;
        // 获得两个时间的毫秒时间差异
        long diff = endDate.getTime() - startTime.getTime();
        // 计算差多少天
        long day = diff / nd;
        // 计算差多少小时
        long hour = diff % nd / nh;
        // 计算差多少分钟
        long min = diff % nd % nh / nm;
        // 计算差多少秒//输出结果
        // long sec = diff % nd % nh % nm / ns;
        return day + "天" + hour + "小时" + min + "分钟";
    }

    /**
     * 计算两个日期查了多少天
     * @param startTime 开始
     * @param endDate 结束
     * @param up 向上取整（进一）
     * @return
     */
    public static long dayDistance(Date startTime, Date endDate, boolean up) {
        long nd = MILLISECONDS_PER_DAY;
        long time = endDate.getTime() - startTime.getTime();
        return up ? (time / nd) + 1 : time / nd;
    }

    /**
     * 计算两个日期查了多少天，默认:向上取整（进一）
     * @param startTime 开始
     * @param endDate 结束
     * @return
     */
    public static long dayDistance(Date startTime, Date endDate) {
        return dayDistance(startTime, endDate, true);
    }

    /**
     * 增加 LocalDateTime ==> Date
     */
    public static Date toDate(LocalDateTime temporalAccessor) {
        ZonedDateTime zdt = temporalAccessor.atZone(ZoneId.systemDefault());
        return Date.from(zdt.toInstant());
    }

    /**
     * 增加 LocalDate ==> Date
     */
    public static Date toDate(LocalDate temporalAccessor) {
        LocalDateTime localDateTime = LocalDateTime.of(temporalAccessor, LocalTime.of(0, 0, 0));
        ZonedDateTime zdt = localDateTime.atZone(ZoneId.systemDefault());
        return Date.from(zdt.toInstant());
    }

    /**
     * 根据日期区间，获取所有日期
     *
     * @param startTime 开始时间 yyyy-MM-dd
     * @param endTime   结束时间 yyyy-MM-dd
     * @return 日期的集合
     */
    public static List<String> getDaysByDateInterval(String startTime, String endTime) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDate startDate = LocalDate.parse(startTime, formatter);
        LocalDate endDate = LocalDate.parse(endTime, formatter);
        return getDaysByDateInterval(startDate, endDate);
    }

    /**
     * 根据日期区间，获取所有日期
     *
     * @param startTime 开始时间 yyyy-MM-dd
     * @param endTime   结束时间 yyyy-MM-dd
     * @return 日期的集合
     */
    public static List<String> getDaysByDateInterval(LocalDate startTime, LocalDate endTime) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        // 确保开始日期不晚于结束日期
        if (startTime.isAfter(endTime)) {
            LocalDate temp = startTime;
            startTime = endTime;
            endTime = temp;
        }

        List<String> dateList = new ArrayList<>();
        LocalDate currentDate = startTime;

        // 遍历日期区间，包含开始和结束日期
        while (!currentDate.isAfter(endTime)) {
            dateList.add(currentDate.format(formatter));
            currentDate = currentDate.plusDays(1);
        }

        return dateList;
    }

    /**
     * 根据日期区间，获取所有日期
     *
     * @param startTime 开始时间 yyyy-MM-dd
     * @param endTime   结束时间 yyyy-MM-dd
     * @return 日期的集合
     */
    public static List<String> getDaysByDateInterval(Date startTime, Date endTime) {
        String startStr = parseDateToStr(YYYY_MM_DD, startTime);
        String endStr = parseDateToStr(YYYY_MM_DD, endTime);
        LocalDate startLocalDate = LocalDate.parse(startStr, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        LocalDate endLocalDate = LocalDate.parse(endStr, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        return getDaysByDateInterval(startLocalDate, endLocalDate);
    }

    /**
     * @Description: 获取周几，习惯上的，从周一开始
     * @param date
     * @return: int
     * @author: 王金都
     * @Date: 2025/6/9 11:17
     */
    public static int getNormalWeek(Date date){
        Week weekEnum = DateUtil.dayOfWeekEnum(date);
        switch (weekEnum){
            case MONDAY -> {
                return 1;
            }
            case TUESDAY -> {
                return 2;
            }
            case WEDNESDAY -> {
                return 3;
            }
            case THURSDAY -> {
                return 4;
            }
            case FRIDAY -> {
                return 5;
            }
            case SATURDAY -> {
                return 6;
            }
            default -> {
                return 7;
            }
        }
    }

    /**
     * @Description: 根据天获取中文周，周一、周二
     * @param date
     * @return: String
     * @author: 王金都
     * @Date: 2025/6/9 11:20
     */
    public static String getNormalWeekCn(Date date){
        int normalWeek = getNormalWeek(date);
        switch (normalWeek){
            case 1 -> {
                return "周一";
            }
            case 2 -> {
                return "周二";
            }
            case 3 -> {
                return "周三";
            }
            case 4 -> {
                return "周四";
            }
            case 5 -> {
                return "周五";
            }
            case 6 -> {
                return "周六";
            }
            default -> {
                return "周日";
            }
        }
    }

    /**
     * @Description: 统计维度的月开始
     * @param year
     * @param month
     * @return: Date
     * @author: 王金都
     * @Date: 2025/6/16 15:59
     */
    public static Date monthStartDate(String year, Integer month){
        int preMonth = month == 1 ? 12 : month - 1;
        String preYear = month == 1 ? String.valueOf((((Integer.parseInt(year)) - 1))) : year;
        return DateUtil.parseDate(preYear + "-" + preMonth + "-26");
    }

    /**
     * @Description: 统计维度的月结束
     * @param year
     * @param month
     * @return: Date
     * @author: 王金都
     * @Date: 2025/6/16 15:59
     */
    public static Date monthEndDate(String year, Integer month){
        return DateUtil.parseDate(year + "-" + month + "-25");
    }

    public static final String MORNING_START = " 08:30:00";
    public static final String MORNING_END = " 12:00:00";
    public static final String AFTERNOON_START = " 13:30:00";
    public static final String AFTERNOON_END = " 17:30:00";

    /**
     * @Description: 上午上班时间
     * @param date
     * @return: Date
     * @author: 王金都
     * @Date: 2025/6/16 15:59
     */
    public static Date morningStart(String date){
        return DateUtil.parseDateTime(date+MORNING_START);
    }

    /**
     * @Description: 上午下班时间
     * @param date
     * @return: Date
     * @author: 王金都
     * @Date: 2025/6/16 16:00
     */
    public static Date morningEnd(String date){
        return DateUtil.parseDateTime(date+MORNING_END);
    }

    /**
     * @Description: 下午上班时间
     * @param date
     * @return: Date
     * @author: 王金都
     * @Date: 2025/6/16 16:01
     */
    public static Date afternoonStart(String date){
        return DateUtil.parseDateTime(date+AFTERNOON_START);
    }

    /**
     * @Description: 下午下班时间
     * @param date
     * @return: Date
     * @author: 王金都
     * @Date: 2025/6/16 16:01
     */
    public static Date afternoonEnd(String date){
        return DateUtil.parseDateTime(date+AFTERNOON_END);
    }

    /**
     * @Description: 工作开始时间
     * @param date
     * @return: Date
     * @author: 王金都
     * @Date: 2025/6/16 16:24
     */
    public static Date workStart(Date date){
        String dateStr = DateUtil.formatDate(date);
        Date dateStart = morningStart(dateStr);
        if (DateUtil.compare(dateStart,date)<=0) {
            return date;
        }
        return dateStart;
    }

    /**
     * @Description: 工作结束时间
     * @param date
     * @return: Date
     * @author: 王金都
     * @Date: 2025/6/16 16:25
     */
    public static Date workEnd(Date date){
        String dateStr = DateUtil.formatDate(date);
        Date dateEnd = afternoonEnd(dateStr);
        if (DateUtil.compare(dateEnd,date)>=0) {
            return date;
        }
        return dateEnd;
    }

    /**
     * @Description: 分钟数转xhxmin
     * @param minutes
     * @return: String
     * @author: 王金都
     * @Date: 2025/6/17 10:56
     */
    public static String toHm(long minutes){
        long hours = minutes / 60;
        long remainingMinutes = minutes % 60;
        return String.format("%dh%dmin", hours, remainingMinutes);
    }

    /**
     * 判断字符传是否为日期格式
     *
     * @param str 字符串
     * @return boolean
     * <AUTHOR>
     * @date 2025/7/16 10:33
     */
    public static boolean isDate(String str) {
        if (str == null || str.isEmpty()) {
            return false;
        }

        for (String pattern : parsePatterns) {
            try {
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern(pattern);
                LocalDate.parse(str, formatter);
                return true;
            } catch (Exception ignored) {
            }
        }
        return false;
    }
}
