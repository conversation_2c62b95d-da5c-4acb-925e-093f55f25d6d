package com.wbyy.common.core.utils;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONArray;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * ID转 name 工具类
 */
@Slf4j
public class IdToNameUtils {

    /**
     * JSON 字符串 ids  转 names 字符串（中间逗号分隔）
     * @param idNameMap id name 的关系集合
     * @param ids id集合的字符串 例如：[31231,45325234,54637356]
     * @return
     */
    public static String idsToNames(Map<Long, String> idNameMap, String ids) {
        if (CollUtil.isEmpty(idNameMap)) {
            return null;
        }
        if (StrUtil.isEmpty(ids)) {
            return null;
        }
        List<Long> projectTypeIds = JSONArray.parseArray(ids, Long.class);

        if (CollUtil.isEmpty(projectTypeIds)) {
            return null;
        }
        List<String> names = new ArrayList<>();
        for (Long projectTypeId : projectTypeIds) {
            String name = idNameMap.get(projectTypeId);
            names.add(StrUtil.isEmpty(name) ? "-" : name);
        }

        return CollUtil.join(names, ", ");
    }
}
