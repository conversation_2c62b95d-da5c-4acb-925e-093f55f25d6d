package com.wbyy.common.core.config;


import com.wbyy.common.core.context.SecurityContextHolder;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.atomic.AtomicInteger;


/**
 * <AUTHOR>
 * @date 2025/06/07
 */
@Slf4j
public class WbyyThread extends Thread {

    private static final AtomicInteger created = new AtomicInteger();

    public WbyyThread(Runnable runnable, String threadName) {
        super(runnable, threadName + created.incrementAndGet());
        setUncaughtExceptionHandler((t, e) ->
                log.error("UNCAUGHT in thread [{}]", t.getName(), e));
    }

    @Override
    public void run() {
        // 复制 debug 标志以确保一致的值
        log.debug("[{}] Thread Created.", getName());
        try {
            super.run();
        } finally {
            log.debug("[{}] Thread Exiting.", getName());
            cleanThreadLocal();
        }

    }

    private void cleanThreadLocal() {
        log.debug("[{}] Thread Interrupted.", getName());
        SecurityContextHolder.remove();
        log.debug("【异步方法】 ThreadLocal Cleaning successful");
    }

}