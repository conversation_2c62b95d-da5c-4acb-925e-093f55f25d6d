package com.wbyy.common.core.config;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.config.ConfigurableListableBeanFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * <AUTHOR>
 * @date 2025/5/17  14:45
 * @description 线程池工具类
 */
@Slf4j
@Configuration
@RequiredArgsConstructor
public class ThreadPoolConfiguration {

    private final ThreadPoolConfig threadPoolConfig;

    private final ConfigurableApplicationContext context;

    @Bean
    @ConditionalOnMissingBean(name = "customThreadPool")
    public Map<String, ThreadPoolTaskExecutor> customThreadPool() {
        Map<String, ThreadPoolTaskExecutor> pools = new HashMap<>();
        ConfigurableListableBeanFactory beanFactory = context.getBeanFactory();
        ThreadPoolConfig.Config defaultConfig = new ThreadPoolConfig.Config();
        threadPoolConfig.getThreadPool().forEach((biz, config) -> {
            ThreadPoolConfig.Config mergedConfig = config.mergeWithDefault(defaultConfig);
            ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
            executor.setThreadFactory(new WbyyThreadFactory(mergedConfig.getThreadPrefix()));
            executor.setCorePoolSize(mergedConfig.getCoreSize());
            executor.setMaxPoolSize(mergedConfig.getMaxSize());
            executor.setKeepAliveSeconds(mergedConfig.getKeepAliveTime());
            executor.setThreadNamePrefix(mergedConfig.getThreadPrefix());
            executor.setQueueCapacity(mergedConfig.getQueueCapacity());
            executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
            executor.setWaitForTasksToCompleteOnShutdown(true);
            executor.setAwaitTerminationSeconds(mergedConfig.getAwaitTerminationSeconds());
            executor.initialize();
            pools.put(biz, executor);

            beanFactory.registerSingleton(biz, executor);
            log.info("【自定义线程池】 加载成功, 线程池名称: {}, 核心线程数: {}，最大线程数：{}，队列长度：{}",
                    biz, mergedConfig.getCoreSize(), mergedConfig.getMaxSize(), mergedConfig.getQueueCapacity());
        });
        return pools;
    }

}
