package com.wbyy.common.core.handler.mybatis;

import com.alibaba.fastjson2.TypeReference;
import com.wbyy.common.core.handler.mybatis.bo.HorizontalContentBO;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;

import java.util.List;

/**
 * <AUTHOR>
 */
@MappedTypes(List.class)
@MappedJdbcTypes(JdbcType.VARCHAR)
public class HorizontalContentListTypeHandler extends ListTypeHandler<HorizontalContentBO>{

    @Override
    protected TypeReference<List<HorizontalContentBO>> specificType() {
        return new TypeReference<List<HorizontalContentBO>>() {};
    }
}
