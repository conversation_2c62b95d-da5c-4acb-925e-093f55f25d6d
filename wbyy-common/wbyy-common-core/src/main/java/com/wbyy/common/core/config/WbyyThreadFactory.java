package com.wbyy.common.core.config;

import org.springframework.lang.NonNull;

import java.util.concurrent.ThreadFactory;


/**
 * 自定义线程工厂
 *
 * <AUTHOR>
 */
public class WbyyThreadFactory implements ThreadFactory {
    private final String threadName;

    public WbyyThreadFactory(String threadName) {
        this.threadName = threadName;
    }

    public Thread newThread() {
        return new WbyyThread(() -> {

        }, threadName);
    }

    @Override
    public Thread newThread(@NonNull Runnable runnable) {
        return new WbyyThread(runnable, threadName);
    }

}