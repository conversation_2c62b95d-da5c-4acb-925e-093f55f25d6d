package com.wbyy.common.datascope.aspect;

import cn.hutool.core.util.StrUtil;
import com.wbyy.common.core.context.SecurityContextHolder;
import com.wbyy.common.core.text.Convert;
import com.wbyy.common.core.utils.StringUtils;
import com.wbyy.common.datascope.annotation.SysDataScope;
import com.wbyy.common.security.utils.SecurityUtils;
import com.wbyy.orm.core.domain.BaseEntity;
import com.wbyy.system.api.domain.ApiRole;
import com.wbyy.system.api.domain.ApiUser;
import com.wbyy.system.api.model.ApiBaseEntity;
import com.wbyy.system.api.model.LoginUser;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import static com.wbyy.common.core.constant.ScopeConst.*;


/**
 * 数据过滤处理
 * 
 * <AUTHOR>
 */
@Aspect
@Component
public class SysDataScopeAspect
{


    @Before("@annotation(sysDataScope)")
    public void doBefore(JoinPoint point, SysDataScope sysDataScope) throws Throwable
    {
        clearDataScope(point);
        handleDataScope(point, sysDataScope);
    }

    protected void handleDataScope(final JoinPoint joinPoint, SysDataScope sysDataScope)
    {
        // 获取当前的用户
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if (StringUtils.isNotNull(loginUser))
        {
            ApiUser currentUser = loginUser.getSysUser();
            // 如果是超级管理员，则不过滤数据
            if (StringUtils.isNotNull(currentUser) && !SecurityUtils.isAdmin())
            {
                String permission = StringUtils.defaultIfEmpty(sysDataScope.permission(), SecurityContextHolder.getPermission());
                dataScopeFilter(joinPoint, currentUser, loginUser, sysDataScope.deptAlias(),
                        sysDataScope.userAlias(), permission);
            }
        }
    }

    /**
     * 数据范围过滤
     *
     * @param joinPoint  切点
     * @param user       用户
     * @param loginUser
     * @param deptAlias  部门别名
     * @param userAlias  用户别名
     * @param permission 权限字符
     */
    public static void dataScopeFilter(JoinPoint joinPoint, ApiUser user, LoginUser loginUser, String deptAlias, String userAlias, String permission)
    {
        StringBuilder sqlString = new StringBuilder();
        List<Integer> conditions = new ArrayList<>();
        List<String> scopeCustomIds = new ArrayList<String>();
        user.getRoles().forEach(role -> {
            if (DATA_SCOPE_CUSTOM == role.getDataScope() && (StrUtil.isNotBlank(permission) &&StringUtils.containsAny(role.getPermissions(), Convert.toStrArray(permission))))
            {
                scopeCustomIds.add(Convert.toStr(role.getRoleId()));
            }
        });

        for (ApiRole role : user.getRoles())
        {
            Integer dataScope = role.getDataScope();
            if (conditions.contains(dataScope))
            {
                continue;
            }
            if (StrUtil.isNotBlank(permission) &&!StringUtils.containsAny(loginUser.getPermissions(), Convert.toStrArray(permission)))
            {
                continue;
            }
            if (DATA_SCOPE_ALL == dataScope)
            {
                sqlString = new StringBuilder();
                conditions.add(dataScope);
                break;
            }
            else if (DATA_SCOPE_CUSTOM == dataScope)
            {
                if (scopeCustomIds.size() > 1)
                {
                    // 多个自定数据权限使用in查询，避免多次拼接。
                    sqlString.append(StringUtils.format(" OR {}.dept_id IN ( SELECT dept_id FROM sys_role_dept WHERE role_id in ({}) ) ", deptAlias, String.join(",", scopeCustomIds)));
                }
                else
                {
                    sqlString.append(StringUtils.format(" OR {}.dept_id IN ( SELECT dept_id FROM sys_role_dept WHERE role_id = {} ) ", deptAlias, role.getRoleId()));
                }
            }
            else if (DATA_SCOPE_DEPT == dataScope)
            {
                String deptIdsStr = "0";
                if (StringUtils.isNotEmpty(loginUser.getDepts())){
                    deptIdsStr = loginUser.getDepts().stream().map(item->""+item).collect(Collectors.joining(","));
                }
                sqlString.append(StringUtils.format(" OR {}.dept_id in ({}) ", deptAlias, deptIdsStr));
            }
            else if (DATA_SCOPE_DEPT_AND_CHILD == dataScope)
            {
                String deptIdsStr = "0";
                if (StringUtils.isNotEmpty(loginUser.getDepts())){
                    deptIdsStr = loginUser.getDepts().stream().map(item->""+item).collect(Collectors.joining(","));
                }
                sqlString.append(StringUtils.format(" OR {}.dept_id IN ( SELECT dept_id FROM sys_dept WHERE dept_id in ({}) or (", deptAlias, deptIdsStr, deptIdsStr));
                String[] split = deptIdsStr.split(",");
                for (int i = 0; i < split.length; i++) {
                    if (i!=0){
                        sqlString.append(" OR ");
                    }
                    sqlString.append(" ancestors like '%").append(split[i]).append("%' ");
                }
                sqlString.append(" ))");
            }
            else if (DATA_SCOPE_SELF == dataScope)
            {
                if (StringUtils.isNotBlank(userAlias))
                {
                    sqlString.append(StringUtils.format(" OR {}.user_id = {} ", userAlias, user.getUserId()));
                }
                else
                {
                    // 数据权限为仅本人且没有userAlias别名不查询任何数据
                    sqlString.append(StringUtils.format(" OR {}.dept_id = 0 ", deptAlias));
                }
            }
            conditions.add(dataScope);
        }

        // 角色都不包含传递过来的权限字符，这个时候sqlString也会为空，所以要限制一下,不查询任何数据
        if (StringUtils.isEmpty(conditions))
        {
            sqlString.append(StringUtils.format(" OR {}.dept_id = 0 ", deptAlias));
        }

        if (StringUtils.isNotBlank(sqlString.toString()))
        {
            Object params = joinPoint.getArgs()[0];
            if (StringUtils.isNotNull(params) && (params instanceof ApiBaseEntity baseEntity))
            {
                baseEntity.getParams().put(DATA_SCOPE, " AND (" + sqlString.substring(4) + ")");
            }
            if (StringUtils.isNotNull(params) && (params instanceof BaseEntity baseEntity))
            {
                baseEntity.getParams().put(DATA_SCOPE, " AND (" + sqlString.substring(4) + ")");
            }
        }
    }

    /**
     * 拼接权限sql前先清空params.dataScope参数防止注入
     */
    private void clearDataScope(final JoinPoint joinPoint)
    {
        Object params = joinPoint.getArgs()[0];
        if (StringUtils.isNotNull(params) && params instanceof ApiBaseEntity baseEntity)
        {
            baseEntity.getParams().put(DATA_SCOPE, "");
        }
        if (StringUtils.isNotNull(params) && params instanceof BaseEntity baseEntity)
        {
            baseEntity.getParams().put(DATA_SCOPE, "");
        }
    }
}
