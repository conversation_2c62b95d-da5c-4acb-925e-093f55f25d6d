package com.wbyy.orm.core.domain;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

import java.io.Serial;

@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
@SuperBuilder
@NoArgsConstructor
public class BaseAuditEntity extends BaseEntity {

    @Serial
    private static final long serialVersionUID = -5551063668652006867L;


    /**
     * 创建者名称
     */
    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "创建者名称（新增、修改时，忽略该字段）")
    private String createNameBy;

    /**
     * 更新者名称
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description = "更新者名称（新增、修改时，忽略该字段）")
    private String updateNameBy;
}
