package com.wbyy.orm.handler;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.wbyy.common.core.exception.ServiceException;
import com.wbyy.common.security.utils.SecurityUtils;
import com.wbyy.orm.core.domain.BaseAuditEntity;
import com.wbyy.orm.core.domain.BaseEntity;
import com.wbyy.system.api.model.LoginUser;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.reflection.MetaObject;

import java.util.Date;

@Slf4j
public class InjectionMetaObjectHandler implements MetaObjectHandler {
    private static final String CREATE_TIME = "createTime";
    private static final String UPDATE_TIME = "updateTime";
    private static final String USER_NAME = "SYS_MAN";
    private static final Long USER_ID = -1L;

    /**
     * 插入填充方法，用于在插入数据时自动填充实体对象中的创建时间、更新时间、创建人、更新人等信息
     *
     * @param metaObject 元对象，用于获取原始对象并进行填充
     */
    @Override
    public void insertFill(MetaObject metaObject) {
        try {
            if (ObjectUtil.isNotNull(metaObject) && metaObject.getOriginalObject() instanceof BaseAuditEntity baseAuditEntity) {
                // 获取当前时间作为创建时间和更新时间，如果创建时间不为空，则使用创建时间，否则使用当前时间
                Date current = null==baseAuditEntity.getCreateTime()?new Date():baseAuditEntity.getCreateTime();
                baseAuditEntity.setCreateTime(current);
                baseAuditEntity.setUpdateTime(current);

                // 如果创建人为空，则填充当前登录用户的信息
                if (ObjectUtil.isNull(baseAuditEntity.getCreateBy())) {
                    LoginUser loginUser = SecurityUtils.getLoginUser();
                    if (ObjectUtil.isNotNull(loginUser)) {
                        Long userId = loginUser.getUserId();
                        String realName = loginUser.getRealName();
                        // 填充创建人、更新人和创建部门信息
                        baseAuditEntity.setCreateBy(userId);
                        baseAuditEntity.setUpdateBy(userId);
                        baseAuditEntity.setCreateNameBy(realName);
                        baseAuditEntity.setUpdateNameBy(realName);
                    } else {
                        baseAuditEntity.setCreateBy(USER_ID);
                        baseAuditEntity.setUpdateBy(USER_ID);
                        baseAuditEntity.setCreateNameBy(USER_NAME);
                        baseAuditEntity.setUpdateNameBy(USER_NAME);
                    }
                }
            } else if (ObjectUtil.isNotNull(metaObject) && metaObject.getOriginalObject() instanceof BaseEntity baseEntity) {
                // 获取当前时间作为创建时间和更新时间，如果创建时间不为空，则使用创建时间，否则使用当前时间
                Date current = null==baseEntity.getCreateTime()?new Date():baseEntity.getCreateTime();
                baseEntity.setCreateTime(current);
                baseEntity.setUpdateTime(current);

                // 如果创建人为空，则填充当前登录用户的信息
                if (ObjectUtil.isNull(baseEntity.getCreateBy())) {
                    LoginUser loginUser = SecurityUtils.getLoginUser();
                    if (ObjectUtil.isNotNull(loginUser)) {
                        Long userId = loginUser.getUserId();
                        // 填充创建人、更新人和创建部门信息
                        baseEntity.setCreateBy(userId);
                        baseEntity.setUpdateBy(userId);
                    } else {
                        baseEntity.setCreateBy(USER_ID);
                        baseEntity.setUpdateBy(USER_ID);
                    }
                }
            } else {
                Date date = new Date();
                this.strictInsertFill(metaObject, CREATE_TIME, Date.class, date);
                this.strictInsertFill(metaObject, UPDATE_TIME, Date.class, date);
            }
        } catch (Exception e) {
            log.error("[新增],审计字段自动注入异常",e);
            throw new ServiceException("审计字段自动注入异常");
        }
    }

    /**
     * 更新填充方法，用于在更新数据时自动填充实体对象中的更新时间和更新人信息
     *
     * @param metaObject 元对象，用于获取原始对象并进行填充
     */
    @Override
    public void updateFill(MetaObject metaObject) {
        try {
            if (ObjectUtil.isNotNull(metaObject) && metaObject.getOriginalObject() instanceof BaseAuditEntity baseAuditEntity) {
                // 获取当前时间作为更新时间，无论原始对象中的更新时间是否为空都填充
                Date current = new Date();
                baseAuditEntity.setUpdateTime(current);

                // 获取当前登录用户的ID，并填充更新人信息
                LoginUser loginUser = SecurityUtils.getLoginUser();
                if (ObjectUtil.isNotNull(loginUser)) {
                    Long userId = loginUser.getUserId();
                    String realName = loginUser.getRealName();
                    // 填充创建人、更新人和创建部门信息
                    baseAuditEntity.setUpdateBy(userId);
                    baseAuditEntity.setUpdateNameBy(realName);
                }else {
                    baseAuditEntity.setUpdateBy(USER_ID);
                    baseAuditEntity.setUpdateNameBy(USER_NAME);
                }
            } else if (ObjectUtil.isNotNull(metaObject) && metaObject.getOriginalObject() instanceof BaseEntity baseEntity) {
                // 获取当前时间作为更新时间，无论原始对象中的更新时间是否为空都填充
                Date current = new Date();
                baseEntity.setUpdateTime(current);

                // 获取当前登录用户的ID，并填充更新人信息
                LoginUser loginUser = SecurityUtils.getLoginUser();
                if (ObjectUtil.isNotNull(loginUser)) {
                    Long userId = loginUser.getUserId();
                    // 填充创建人、更新人和创建部门信息
                    baseEntity.setUpdateBy(userId);
                }else {
                    baseEntity.setUpdateBy(USER_ID);
                }
            } else {
                this.strictUpdateFill(metaObject, UPDATE_TIME, Date.class, new Date());
            }
        } catch (Exception e) {
            log.error("[修改],审计字段自动注入异常",e);
            throw new ServiceException("审计字段自动注入异常");
        }
    }

}
