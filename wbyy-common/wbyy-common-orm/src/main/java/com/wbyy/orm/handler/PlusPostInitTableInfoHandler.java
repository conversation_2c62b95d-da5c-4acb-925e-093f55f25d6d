package com.wbyy.orm.handler;

import com.baomidou.mybatisplus.core.handlers.PostInitTableInfoHandler;
import com.baomidou.mybatisplus.core.metadata.TableInfo;
import com.wbyy.common.core.text.Convert;
import com.wbyy.common.core.utils.reflect.ReflectUtils;
import org.apache.ibatis.session.Configuration;
import com.wbyy.common.core.utils.SpringUtils;

public class PlusPostInitTableInfoHandler implements PostInitTableInfoHandler {
    @Override
    public void postTableInfo(TableInfo tableInfo, Configuration configuration) {
        String flag = SpringUtils.getProperty("mybatis-plus.enableLogicDelete", "true");
        // 只有关闭时 统一设置false 为true时mp自动判断不处理
        if (!Convert.toBool(flag)) {
            ReflectUtils.setFieldValue(tableInfo, "withLogicDelete", false);
        }
    }
}
